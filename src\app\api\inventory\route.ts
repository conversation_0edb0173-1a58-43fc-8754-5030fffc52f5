import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canManageInventory } from '@/lib/auth-utils'
import { inventoryItemCreateSchema, paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/inventory - List inventory items with pagination and filtering
export const GET = withAuth(async (req: NextRequest, _user) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 20,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'name',
    sortOrder: searchParams.get('sortOrder') || 'asc',
  })
  
  const branchId = searchParams.get('branchId')
  const category = searchParams.get('category')
  const expired = searchParams.get('expired') === 'true'

  const skip = (page - 1) * limit

  // Build where clause
  const where: Record<string, unknown> = {}
  
  if (branchId) where.branchId = branchId
  if (category) where.category = category
  
  // Low stock filter - we'll handle this in the application logic
  // Note: Prisma doesn't support field-to-field comparisons in where clauses
  // This would need to be handled with raw SQL or post-processing
  
  // Expired items filter
  if (expired) {
    where.expiryDate = { lte: new Date() }
  }
  
  // Search filter
  if (query) {
    where.OR = [
      { name: { contains: query, mode: 'insensitive' } },
      { sku: { contains: query, mode: 'insensitive' } },
      { description: { contains: query, mode: 'insensitive' } },
      { supplier: { contains: query, mode: 'insensitive' } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'name' ? { name: sortOrder } :
                  sortBy === 'sku' ? { sku: sortOrder } :
                  sortBy === 'category' ? { category: sortOrder } :
                  sortBy === 'currentStock' ? { currentStock: sortOrder } :
                  sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  { name: sortOrder as 'asc' | 'desc' }

  const [items, total] = await Promise.all([
    prisma.inventoryItem.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            usageLogs: true,
            stockLogs: true,
          },
        },
      },
    }),
    prisma.inventoryItem.count({ where }),
  ])
  
  return NextResponse.json({
    items,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF])

// POST /api/inventory - Create new inventory item
export const POST = withAuth(async (req: NextRequest, user) => {
  if (!canManageInventory(user.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }
  
  const body = await req.json()
  const validatedData = inventoryItemCreateSchema.parse(body)
  
  // Verify branch exists
  const branch = await prisma.branch.findUnique({
    where: { id: validatedData.branchId },
  })
  
  if (!branch) {
    return NextResponse.json(
      { error: 'Branch not found' },
      { status: 404 }
    )
  }
  
  // Check if SKU already exists
  const existingItem = await prisma.inventoryItem.findUnique({
    where: { sku: validatedData.sku },
  })
  
  if (existingItem) {
    return NextResponse.json(
      { error: 'SKU already exists' },
      { status: 400 }
    )
  }
  
  // Create inventory item
  const item = await prisma.inventoryItem.create({
    data: validatedData,
    include: {
      branch: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })
  
  // Create initial stock log if there's initial stock
  if (validatedData.currentStock > 0) {
    await prisma.stockMovement.create({
      data: {
        itemId: item.id,
        type: 'IN',
        quantity: validatedData.currentStock,
        reason: 'Initial stock',
        performedBy: user.id,
      },
    })
  }
  
  return NextResponse.json(item, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF])
