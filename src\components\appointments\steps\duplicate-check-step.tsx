'use client'

import { useState, useEffect } from 'react'
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, AlertTriangle, Calendar, Clock, User, MapPin, CheckCircle } from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'

interface DuplicateCheckStepProps {
  onNext: () => void
  onContinueAnyway: () => void
  onManageExisting: (appointment: AppointmentDetails) => void
}

export function DuplicateCheckStep({ 
  onNext, 
  onContinueAnyway, 
  onManageExisting 
}: DuplicateCheckStepProps) {
  const [existingAppointments, setExistingAppointments] = useState<AppointmentDetails[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchExistingAppointments()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  const fetchExistingAppointments = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // Get current date in ISO format for filtering future appointments
      const now = new Date().toISOString()
      
      // Fetch upcoming appointments
      const response = await fetch(`/api/appointments?status=SCHEDULED,CONFIRMED,PENDING&dateFrom=${now}&sortBy=scheduledAt&sortOrder=asc`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch existing appointments')
      }
      
      const data = await response.json()
      setExistingAppointments(data.appointments || [])
      
      // If no existing appointments, automatically proceed to next step
      if (!data.appointments || data.appointments.length === 0) {
        onNext()
      }
    } catch (err) {
      console.error('Error fetching existing appointments:', err)
      setError(err instanceof Error ? err.message : 'Failed to check existing appointments')
      // On error, allow user to continue
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    }).format(date)
  }

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Manila'
    }).format(date)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-3 text-gray-600">Checking for existing appointments...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
        
        <div className="flex justify-center">
          <Button onClick={onContinueAnyway} className="bg-blue-600 hover:bg-blue-700">
            <CheckCircle className="h-4 w-4 mr-2" />
            Continue with Booking
          </Button>
        </div>
      </div>
    )
  }

  if (existingAppointments.length === 0) {
    // This shouldn't normally be reached as we auto-proceed, but just in case
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Existing Appointments</h3>
          <p className="text-gray-600">You can proceed with booking your new appointment.</p>
        </div>
        
        <div className="flex justify-center">
          <Button onClick={onNext} className="bg-blue-600 hover:bg-blue-700">
            Continue with Booking
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Warning Alert */}
      <Alert className="bg-yellow-50 border-yellow-200">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-800">
          <strong>Existing Appointments Found:</strong> You have {existingAppointments.length} upcoming appointment{existingAppointments.length > 1 ? 's' : ''}. 
          You may want to reschedule or cancel existing appointments before booking a new one.
        </AlertDescription>
      </Alert>

      {/* Existing Appointments List */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Your Upcoming Appointments</h3>
        
        {existingAppointments.map((appointment) => (
          <Card key={appointment.id} className="border-yellow-200 bg-yellow-50">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg text-gray-900 flex items-center">
                    {appointment.service.name}
                    <Badge className={`ml-2 ${getStatusColor(appointment.status)}`}>
                      {appointment.status.replace('_', ' ')}
                    </Badge>
                  </CardTitle>
                  <CardDescription className="mt-1">
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        Dr. {appointment.dentist.user.name}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {formatDate(new Date(appointment.scheduledAt))}
                      </span>
                      <span className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {formatTime(new Date(appointment.scheduledAt))}
                      </span>
                    </div>
                    <div className="flex items-center mt-1">
                      <MapPin className="h-4 w-4 mr-1" />
                      {appointment.branch.name}
                    </div>
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onManageExisting(appointment)}
                  className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                >
                  Manage
                </Button>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button
          variant="outline"
          onClick={onContinueAnyway}
          className="border-blue-300 text-blue-700 hover:bg-blue-50"
        >
          Continue with New Booking
        </Button>
        <Button
          onClick={() => window.location.href = '/appointments/upcoming'}
          className="bg-yellow-600 hover:bg-yellow-700"
        >
          Manage Existing Appointments
        </Button>
      </div>

      {/* Policy Notice */}
      <Alert className="bg-blue-50 border-blue-200">
        <AlertDescription className="text-blue-800">
          <strong>Booking Policy:</strong> While you can book multiple appointments, we recommend managing your existing appointments first. 
          Multiple appointments on the same day may require confirmation from our clinic.
        </AlertDescription>
      </Alert>
    </div>
  )
}
