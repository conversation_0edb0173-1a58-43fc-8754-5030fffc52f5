import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canViewReports } from '@/lib/auth-utils'
import { UserRole, PaymentStatus } from '@prisma/client'

// GET /api/analytics/dashboard - Get dashboard analytics
export const GET = withAuth(async (req: NextRequest, user) => {
  if (!canViewReports(user.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }
  
  const { searchParams } = new URL(req.url)
  const period = searchParams.get('period') || '30' // days
  const branchId = searchParams.get('branchId')
  
  const periodDays = parseInt(period)
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - periodDays)
  
  // Build base where clause for date filtering
  const dateFilter = {
    gte: startDate,
  }
  
  const branchFilter = branchId ? { branchId } : {}
  
  try {
    // Get basic counts
    const [
      totalPatients,
      totalDentists,
      totalAppointments,
      totalTreatments,
      totalInvoices,
      recentAppointments,
      recentTreatments,
      recentPayments,
    ] = await Promise.all([
      // Total patients
      prisma.patientProfile.count(),
      
      // Total active dentists
      prisma.dentistProfile.count({
        where: { isAvailable: true },
      }),
      
      // Appointments in period
      prisma.appointment.count({
        where: {
          ...branchFilter,
          createdAt: dateFilter,
        },
      }),
      
      // Treatments in period
      prisma.treatment.count({
        where: {
          createdAt: dateFilter,
        },
      }),
      
      // Invoices in period
      prisma.invoice.count({
        where: {
          createdAt: dateFilter,
        },
      }),
      
      // Recent appointments
      prisma.appointment.findMany({
        where: {
          ...branchFilter,
          scheduledAt: {
            gte: new Date(),
          },
        },
        take: 5,
        orderBy: { scheduledAt: 'asc' },
        include: {
          patient: {
            include: {
              user: { select: { name: true } },
            },
          },
          dentist: {
            include: {
              user: { select: { name: true } },
            },
          },
          service: { select: { name: true } },
        },
      }),
      
      // Recent treatments
      prisma.treatment.findMany({
        where: {
          createdAt: dateFilter,
        },
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          patient: {
            include: {
              user: { select: { name: true } },
            },
          },
          dentist: {
            include: {
              user: { select: { name: true } },
            },
          },
          service: { select: { name: true } },
        },
      }),
      
      // Recent payments
      prisma.payment.findMany({
        where: {
          createdAt: dateFilter,
          status: PaymentStatus.PAID,
        },
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          invoice: {
            include: {
              patient: {
                include: {
                  user: { select: { name: true } },
                },
              },
            },
          },
        },
      }),
    ])
    
    // Get appointment status breakdown
    const appointmentsByStatus = await prisma.appointment.groupBy({
      by: ['status'],
      where: {
        ...branchFilter,
        createdAt: dateFilter,
      },
      _count: {
        id: true,
      },
    })
    
    // Get treatment status breakdown
    const treatmentsByStatus = await prisma.treatment.groupBy({
      by: ['status'],
      where: {
        createdAt: dateFilter,
      },
      _count: {
        id: true,
      },
    })
    
    // Get revenue data
    const revenueData = await prisma.payment.aggregate({
      where: {
        createdAt: dateFilter,
        status: PaymentStatus.PAID,
      },
      _sum: {
        amount: true,
      },
    })
    
    // Get pending payments
    const pendingPayments = await prisma.invoice.aggregate({
      where: {
        status: {
          in: [PaymentStatus.PENDING, PaymentStatus.PARTIAL, PaymentStatus.OVERDUE],
        },
      },
      _sum: {
        totalAmount: true,
        paidAmount: true,
      },
    })
    
    // Get popular services
    const popularServices = await prisma.appointment.groupBy({
      by: ['serviceId'],
      where: {
        ...branchFilter,
        createdAt: dateFilter,
      },
      _count: {
        id: true,
      },
      orderBy: {
        _count: {
          id: 'desc',
        },
      },
      take: 5,
    })
    
    // Get service details for popular services
    const serviceDetails = await prisma.service.findMany({
      where: {
        id: {
          in: popularServices.map(s => s.serviceId),
        },
      },
      select: {
        id: true,
        name: true,
        category: true,
      },
    })
    
    const popularServicesWithDetails = popularServices.map(service => ({
      ...service,
      service: serviceDetails.find(s => s.id === service.serviceId),
    }))
    
    return NextResponse.json({
      period: periodDays,
      summary: {
        totalPatients,
        totalDentists,
        totalAppointments,
        totalTreatments,
        totalInvoices,
        totalRevenue: Number(revenueData._sum.amount || 0),
        pendingRevenue: Number(pendingPayments._sum.totalAmount || 0) - Number(pendingPayments._sum.paidAmount || 0),
      },
      appointmentsByStatus: appointmentsByStatus.map(item => ({
        status: item.status,
        count: item._count.id,
      })),
      treatmentsByStatus: treatmentsByStatus.map(item => ({
        status: item.status,
        count: item._count.id,
      })),
      popularServices: popularServicesWithDetails,
      recentActivity: {
        appointments: recentAppointments,
        treatments: recentTreatments,
        payments: recentPayments,
      },
    })
  } catch (error) {
    console.error('Dashboard analytics error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.DENTIST])
