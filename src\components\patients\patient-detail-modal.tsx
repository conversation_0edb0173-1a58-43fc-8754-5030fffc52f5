'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Skeleton } from '@/components/ui/skeleton'
import { ScrollArea } from '@/components/ui/scroll-area'
import {

  Phone,
  Mail,
  MapPin,
  Calendar,
  FileText,
  Activity,
  CreditCard,
  Shield,
  AlertCircle,
  Clock,
  Edit,
  X,
  Download,
  Eye,
  Upload,
  Trash2,
  Image,
  File
} from 'lucide-react'
import { PatientDetails, TreatmentTimelineEntry } from '@/types/patient'
import { FileUploadModal } from './file-upload-modal'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'

interface PatientDetailModalProps {
  patientId: string | null
  isOpen: boolean
  onClose: () => void
  onEdit?: (patientId: string) => void
}

export function PatientDetailModal({ patientId, isOpen, onClose, onEdit }: PatientDetailModalProps) {
  const [patient, setPatient] = useState<PatientDetails | null>(null)
  const [timeline, setTimeline] = useState<TreatmentTimelineEntry[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isFileUploadOpen, setIsFileUploadOpen] = useState(false)


  useEffect(() => {
    if (patientId && isOpen) {
      fetchPatientDetails()
      fetchPatientTimeline()
    }
  }, [patientId, isOpen, fetchPatientDetails, fetchPatientTimeline])

  const fetchPatientDetails = useCallback(async () => {
    if (!patientId) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/patients/${patientId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch patient details')
      }
      const data = await response.json()
      setPatient(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load patient')
    } finally {
      setIsLoading(false)
    }
  }, [patientId])

  const fetchPatientTimeline = useCallback(async () => {
    if (!patientId) return

    try {
      const response = await fetch(`/api/patients/${patientId}/timeline`)
      if (!response.ok) {
        throw new Error('Failed to fetch timeline')
      }
      const data = await response.json()
      setTimeline(data.timeline || [])
    } catch (err) {
      console.error('Failed to fetch timeline:', err)
    }
  }, [patientId])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getInitials = (name: string | null) => {
    if (!name) return 'U'
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleDeleteFile = async (fileId: string) => {
    if (!confirm('Are you sure you want to delete this file?')) return

    try {
      const response = await fetch(`/api/files/patient/${fileId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete file')
      }

      // Refresh patient data to update files list
      fetchPatientDetails()
    } catch (error) {
      console.error('Error deleting file:', error)
    }
  }

  const handleFileUploadComplete = () => {
    fetchPatientDetails() // Refresh patient data to show new files
  }

  const formatAge = (dateOfBirth: Date | null) => {
    if (!dateOfBirth) return null
    const today = new Date()
    const birth = new Date(dateOfBirth)
    const age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1
    }
    return age
  }

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <Calendar className="h-4 w-4" />
      case 'treatment':
        return <Activity className="h-4 w-4" />
      case 'payment':
        return <CreditCard className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getTimelineColor = (type: string) => {
    switch (type) {
      case 'appointment':
        return 'text-blue-600 bg-blue-100'
      case 'treatment':
        return 'text-green-600 bg-green-100'
      case 'payment':
        return 'text-purple-600 bg-purple-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (!isOpen) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-xl font-semibold">Patient Details</DialogTitle>
          <div className="flex items-center space-x-2">
            {patient && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit?.(patient.id)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-16 w-16 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-24" />
              ))}
            </div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Patient</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={fetchPatientDetails} variant="outline">
              Try Again
            </Button>
          </div>
        ) : patient ? (
          <div className="space-y-6">
            {/* Patient Header */}
            <div className="flex items-start space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={patient.user.image || undefined} alt={patient.user.name || 'Patient'} />
                <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold text-lg">
                  {getInitials(patient.user.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h2 className="text-2xl font-bold text-gray-900">
                    {patient.user.name || 'Unknown Patient'}
                  </h2>
                  <Badge className={cn("text-sm", getStatusColor(patient.user.status))}>
                    {patient.user.status}
                  </Badge>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4" />
                    <span>{patient.user.email}</span>
                  </div>
                  {patient.user.phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4" />
                      <span>{patient.user.phone}</span>
                    </div>
                  )}
                  {patient.dateOfBirth && (
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span>Age {formatAge(patient.dateOfBirth)}</span>
                    </div>
                  )}
                  {patient.address && (
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4" />
                      <span className="truncate">{patient.address}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="medical">Medical History</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="files">Files</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                        Appointments
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">
                        {patient.appointments.length}
                      </div>
                      <p className="text-xs text-gray-600">Total appointments</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Activity className="h-4 w-4 mr-2 text-green-600" />
                        Treatments
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {patient.treatments.length}
                      </div>
                      <p className="text-xs text-gray-600">Total treatments</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <CreditCard className="h-4 w-4 mr-2 text-purple-600" />
                        Invoices
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-purple-600">
                        {patient.invoices.length}
                      </div>
                      <p className="text-xs text-gray-600">Total invoices</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Emergency Contact */}
                {patient.emergencyContact && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Shield className="h-4 w-4 mr-2 text-red-600" />
                        Emergency Contact
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="font-medium">{patient.emergencyContact}</p>
                        {patient.emergencyPhone && (
                          <p className="text-sm text-gray-600">{patient.emergencyPhone}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Insurance Information */}
                {patient.insuranceProvider && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Shield className="h-4 w-4 mr-2 text-blue-600" />
                        Insurance Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="font-medium">{patient.insuranceProvider}</p>
                        {patient.insuranceNumber && (
                          <p className="text-sm text-gray-600">Policy: {patient.insuranceNumber}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="medical" className="space-y-4">
                <div className="grid gap-4">
                  {patient.medicalHistory && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm font-medium">Medical History</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-700 whitespace-pre-wrap">
                          {patient.medicalHistory}
                        </p>
                      </CardContent>
                    </Card>
                  )}

                  {patient.allergies && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm font-medium text-red-600">Allergies</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-700 whitespace-pre-wrap">
                          {patient.allergies}
                        </p>
                      </CardContent>
                    </Card>
                  )}

                  {patient.medications && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm font-medium text-blue-600">Current Medications</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-700 whitespace-pre-wrap">
                          {patient.medications}
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="timeline" className="space-y-4">
                <ScrollArea className="h-96">
                  <div className="space-y-4">
                    {timeline.length === 0 ? (
                      <div className="text-center py-8">
                        <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">No timeline entries found</p>
                      </div>
                    ) : (
                      timeline.map((entry) => (
                        <div key={entry.id} className="flex items-start space-x-3">
                          <div className={cn(
                            "flex items-center justify-center w-8 h-8 rounded-full",
                            getTimelineColor(entry.type)
                          )}>
                            {getTimelineIcon(entry.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="font-medium text-gray-900">{entry.title}</p>
                              <span className="text-xs text-gray-500">
                                {format(new Date(entry.date), 'MMM d, yyyy')}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{entry.description}</p>
                            {entry.cost && (
                              <p className="text-sm font-medium text-green-600 mt-1">
                                ₱{entry.cost.toLocaleString()}
                              </p>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="files" className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Patient Files</h3>
                  <Button onClick={() => setIsFileUploadOpen(true)}>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload File
                  </Button>
                </div>

                <div className="grid gap-4">
                  {patient.files.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No files uploaded</p>
                      <Button
                        variant="outline"
                        className="mt-4"
                        onClick={() => setIsFileUploadOpen(true)}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Upload First File
                      </Button>
                    </div>
                  ) : (
                    patient.files.map((file) => {
                      const getFileIcon = () => {
                        if (file.fileType === 'XRAY' || file.fileType === 'PHOTO') {
                          return <Image className="h-8 w-8 text-blue-600" />
                        }
                        return <File className="h-8 w-8 text-gray-600" />
                      }

                      return (
                        <Card key={file.id}>
                          <CardContent className="flex items-center justify-between p-4">
                            <div className="flex items-center space-x-3">
                              {getFileIcon()}
                              <div>
                                <p className="font-medium">{file.originalName}</p>
                                <p className="text-sm text-gray-600">
                                  {file.fileType} • {(file.fileSize / 1024 / 1024).toFixed(2)} MB
                                </p>
                                {file.description && (
                                  <p className="text-xs text-gray-500 mt-1">{file.description}</p>
                                )}
                                <p className="text-xs text-gray-400">
                                  Uploaded {format(new Date(file.createdAt), 'MMM d, yyyy')}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(file.url, '_blank')}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const link = document.createElement('a')
                                  link.href = file.url
                                  link.download = file.originalName
                                  link.click()
                                }}
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteFile(file.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        ) : null}
      </DialogContent>

      {/* File Upload Modal */}
      <FileUploadModal
        patientId={patientId}
        isOpen={isFileUploadOpen}
        onClose={() => setIsFileUploadOpen(false)}
        onUploadComplete={handleFileUploadComplete}
      />
    </Dialog>
  )
}
