import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { paymentCreateSchema, paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole, PaymentStatus, PaymentMethod } from '@prisma/client'

// GET /api/payments - List payments with pagination and filtering
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const invoiceId = searchParams.get('invoiceId')
  const patientId = searchParams.get('patientId')
  const status = searchParams.get('status') as PaymentStatus | null
  const method = searchParams.get('method') as PaymentMethod | null
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')
  
  const skip = (page - 1) * limit
  
  // Build where clause based on user role and filters
  const where: Record<string, unknown> = {}
  
  // Role-based filtering
  if (user.role === UserRole.PATIENT) {
    // Patients can only see their own payments
    const patientProfile = await prisma.patientProfile.findUnique({
      where: { userId: user.id },
    })
    if (!patientProfile) {
      return NextResponse.json({ payments: [], pagination: { page, limit, total: 0, pages: 0 } })
    }
    where.invoice = { patientId: patientProfile.id }
  }
  
  // Additional filters
  if (invoiceId) where.invoiceId = invoiceId
  if (patientId) {
    // Check if user can access this patient's data
    if (!canAccessPatientData(user.role, patientId, user.id)) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }
    where.invoice = { patientId }
  }
  if (status) where.status = status
  if (method) where.method = method
  
  // Date range filter
  if (dateFrom || dateTo) {
    where.createdAt = {}
    if (dateFrom) (where.createdAt as any).gte = new Date(dateFrom)
    if (dateTo) (where.createdAt as any).lte = new Date(dateTo)
  }
  
  // Search filter
  if (query) {
    where.OR = [
      { transactionId: { contains: query, mode: 'insensitive' } },
      { notes: { contains: query, mode: 'insensitive' } },
      { invoice: { invoiceNumber: { contains: query, mode: 'insensitive' } } },
      { invoice: { patient: { user: { name: { contains: query, mode: 'insensitive' } } } } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'processedAt' ? { processedAt: sortOrder } :
                  sortBy === 'amount' ? { amount: sortOrder } :
                  sortBy === 'status' ? { status: sortOrder } :
                  { createdAt: sortOrder as 'asc' | 'desc' }

  const [payments, total] = await Promise.all([
    prisma.payment.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        invoice: {
          include: {
            patient: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    }),
    prisma.payment.count({ where }),
  ])
  
  return NextResponse.json({
    payments,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/payments - Create new payment
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = paymentCreateSchema.parse(body)
  
  // Verify invoice exists
  const invoice = await prisma.invoice.findUnique({
    where: { id: validatedData.invoiceId },
    include: {
      patient: { include: { user: true } },
      payments: true,
    },
  })
  
  if (!invoice) {
    return NextResponse.json(
      { error: 'Invoice not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canCreatePayment = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    (user.role === UserRole.PATIENT && invoice.patient.userId === user.id)
  
  if (!canCreatePayment) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Calculate remaining balance
  const totalPaid = invoice.payments.reduce((sum, payment) => {
    return payment.status === PaymentStatus.PAID ? sum + Number(payment.amount) : sum
  }, 0)
  
  const remainingBalance = Number(invoice.totalAmount) - totalPaid
  
  // Validate payment amount
  if (validatedData.amount > remainingBalance) {
    return NextResponse.json(
      { error: 'Payment amount exceeds remaining balance' },
      { status: 400 }
    )
  }
  
  // Create payment
  const payment = await prisma.payment.create({
    data: {
      ...validatedData,
      status: PaymentStatus.PENDING, // Will be updated by payment processor
    },
    include: {
      invoice: {
        include: {
          patient: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      },
    },
  })
  
  // TODO: Process payment with appropriate gateway (Stripe, PayMongo, etc.)
  // For now, we'll mark cash payments as paid immediately
  if (validatedData.method === PaymentMethod.CASH) {
    const updatedPayment = await prisma.payment.update({
      where: { id: payment.id },
      data: {
        status: PaymentStatus.PAID,
        processedAt: new Date(),
      },
      include: {
        invoice: {
          include: {
            patient: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    })
    
    // Update invoice status if fully paid
    const newTotalPaid = totalPaid + Number(validatedData.amount)
    if (newTotalPaid >= Number(invoice.totalAmount)) {
      await prisma.invoice.update({
        where: { id: validatedData.invoiceId },
        data: {
          status: PaymentStatus.PAID,
          paidAt: new Date(),
          paidAmount: newTotalPaid,
        },
      })
    } else {
      await prisma.invoice.update({
        where: { id: validatedData.invoiceId },
        data: {
          status: PaymentStatus.PARTIAL,
          paidAmount: newTotalPaid,
        },
      })
    }
    
    return NextResponse.json(updatedPayment, { status: 201 })
  }
  
  return NextResponse.json(payment, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.PATIENT])
