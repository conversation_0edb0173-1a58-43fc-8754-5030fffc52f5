'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Loader2, Calendar, MapPin, User, Stethoscope, FileText, CheckCircle } from 'lucide-react'
import { AppointmentFormData, Branch, Service, DentistProfile } from '@/types/appointment'

interface ConfirmationStepProps {
  formData: AppointmentFormData
  onConfirm: () => void
  isLoading: boolean
}

export function ConfirmationStep({ formData, onConfirm, isLoading }: ConfirmationStepProps) {
  const [branch, setBranch] = useState<Branch | null>(null)
  const [service, setService] = useState<Service | null>(null)
  const [dentist, setDentist] = useState<DentistProfile | null>(null)
  const [isLoadingDetails, setIsLoadingDetails] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAppointmentDetails = useCallback(async () => {
    try {
      setIsLoadingDetails(true)
      
      // Fetch all details in parallel
      const [branchRes, serviceRes, dentistRes] = await Promise.all([
        fetch(`/api/branches/${formData.branchId}`),
        fetch(`/api/services/${formData.serviceId}`),
        fetch(`/api/dentists/${formData.dentistId}`),
      ])

      if (!branchRes.ok || !serviceRes.ok || !dentistRes.ok) {
        throw new Error('Failed to fetch appointment details')
      }

      const [branchData, serviceData, dentistData] = await Promise.all([
        branchRes.json(),
        serviceRes.json(),
        dentistRes.json(),
      ])

      setBranch(branchData)
      setService(serviceData)
      setDentist(dentistData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load appointment details')
    } finally {
      setIsLoadingDetails(false)
    }
  }, [formData])

  useEffect(() => {
    fetchAppointmentDetails()
  }, [fetchAppointmentDetails])

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    })
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Manila'
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  if (isLoadingDetails) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading appointment details...</span>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 mb-4">
        Please review your appointment details and confirm your booking.
      </div>

      {/* Appointment Summary */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-xl text-blue-900 flex items-center">
            <CheckCircle className="h-6 w-6 mr-2" />
            Appointment Summary
          </CardTitle>
          <CardDescription className="text-blue-700">
            Review the details below before confirming your appointment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Date & Time */}
          <div className="flex items-center space-x-3">
            <Calendar className="h-5 w-5 text-blue-600" />
            <div>
              <p className="font-medium text-gray-900">{formatDate(formData.scheduledAt)}</p>
              <p className="text-sm text-gray-600">
                {formatTime(formData.scheduledAt)} ({formatDuration(formData.duration)})
              </p>
            </div>
          </div>

          <Separator />

          {/* Location */}
          {branch && (
            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">{branch.name}</p>
                <p className="text-sm text-gray-600">{branch.address}</p>
                {branch.phone && (
                  <p className="text-sm text-gray-600">Phone: {branch.phone}</p>
                )}
              </div>
            </div>
          )}

          <Separator />

          {/* Dentist */}
          {dentist && (
            <div className="flex items-center space-x-3">
              <User className="h-5 w-5 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">Dr. {dentist.user.name}</p>
                {dentist.specialization && (
                  <p className="text-sm text-gray-600">{dentist.specialization}</p>
                )}
              </div>
            </div>
          )}

          <Separator />

          {/* Service */}
          {service && (
            <div className="flex items-start space-x-3">
              <Stethoscope className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-gray-900">{service.name}</p>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {service.category.replace('_', ' ')}
                  </Badge>
                </div>
                {service.description && (
                  <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                )}
                <div className="flex items-center justify-between mt-2">
                  <span className="text-sm text-gray-600">
                    Duration: {formatDuration(service.duration)}
                  </span>
                  <span className="font-medium text-gray-900">
                    {formatPrice(service.price)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Information */}
      {(formData.symptoms || formData.notes) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <FileText className="h-5 w-5 mr-2 text-gray-600" />
              Additional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {formData.symptoms && (
              <div>
                <p className="font-medium text-gray-900 mb-1">Symptoms/Concerns:</p>
                <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  {formData.symptoms}
                </p>
              </div>
            )}
            {formData.notes && (
              <div>
                <p className="font-medium text-gray-900 mb-1">Additional Notes:</p>
                <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                  {formData.notes}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Important Notice */}
      <Alert className="bg-yellow-50 border-yellow-200">
        <AlertDescription className="text-yellow-800">
          <strong>Please note:</strong> You will receive a confirmation email with your appointment details. 
          If you need to reschedule or cancel, please contact us at least 24 hours in advance.
        </AlertDescription>
      </Alert>

      {/* Confirm Button */}
      <div className="flex justify-center pt-4">
        <Button
          onClick={onConfirm}
          disabled={isLoading}
          size="lg"
          className="bg-green-600 hover:bg-green-700 px-8"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Booking Appointment...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Confirm Appointment
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
