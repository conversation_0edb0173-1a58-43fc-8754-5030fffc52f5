# June 30, 2025
- I built the landing page for the dental clinic web app. It includes all the essential sections: hero, services, about us, testimonials, and contact us. I also added modals for patient and staff login, neatly tucked into the Navbar.
- Design a comprehensive database schema that includes all the necessary entities and created an API endpoints with Authentication using NextAuth and Authorization with roles like PATIENT, DENTIST, STAFF, and ADMIN. I also created a seed file to populate the database with initial data and test users.

# July 1, 2025
- Built the appointment booking page with sections like (select branch, select service, select dentist, select date and time, patient information, and confirmation), custom hooks for booking logic, and Google Calendar integration—also added fallback pages like 404 and updated navigation links.

# July 2, 2025
- Enhanced the appointment booking flow by adding checks for existing appointments and integrating calendar features, including Google Calendar and ICS file support. Also added a dashboard where patients can view their upcoming appointments or appointment history. They can also cancel or reschedule appointments directly from the dashboard or from the appointment details page.
- Designed a dashboard UI for dentist-facing features. I also included sidebar with links to different dentist-facing pages like appointments, patient records, schedule, treatment plans, dental charts, prescription, treatment notes, billing, and analytics.
- Implemented the dentist-facing appointment page, where there's feature like appointment status management, real-time updates, filters and search, appointment list component, responsive design and mobile support.