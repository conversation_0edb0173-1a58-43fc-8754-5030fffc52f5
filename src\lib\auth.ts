import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import GoogleProvider from 'next-auth/providers/google'
import CredentialsProvider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from './prisma'
import { UserRole } from '@prisma/client'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
          role: UserRole.PATIENT, // Default role for Google sign-in users
        }
      },
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
          include: {
            patientProfile: true,
            dentistProfile: true,
            staffProfile: true,
          },
        })

        if (!user || !user.password) {
          return null
        }

        // Compare the provided password with the stored hashed password
        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
          role: user.role,
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
      }
      return session
    },
    async signIn({ user, account, profile: _profile }) {
      // Auto-create patient profile for Google OAuth users
      if (account?.provider === 'google' && user.role === UserRole.PATIENT) {
        try {
          // Check if patient profile already exists
          const existingProfile = await prisma.patientProfile.findUnique({
            where: { userId: user.id },
          })

          if (!existingProfile) {
            // Create patient profile for new Google OAuth user
            await prisma.patientProfile.create({
              data: {
                userId: user.id,
              },
            })
          }
        } catch (error) {
          console.error('Failed to create patient profile for Google OAuth user:', error)
          // Don't block sign-in if profile creation fails
        }
      }

      // Prevent sign-in for dentist users without profiles (except during seeding)
      if (user.role === UserRole.DENTIST) {
        try {
          const dentistProfile = await prisma.dentistProfile.findUnique({
            where: { userId: user.id },
          })

          if (!dentistProfile) {
            console.error(`Dentist user ${user.email} (${user.id}) attempted to sign in without a dentist profile`)
            // In production, you might want to redirect to a profile setup page
            // For now, we'll allow sign-in but log the issue
          }
        } catch (error) {
          console.error('Failed to check dentist profile during sign-in:', error)
        }
      }

      return true
    },
  },
  pages: {
    signIn: '/auth/signin',
  },
}

declare module 'next-auth' {
  interface User {
    role: UserRole
  }
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      image?: string | null
      role: UserRole
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
  }
}
