import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

const bulkActionSchema = z.object({
  action: z.enum(['mark_read', 'mark_unread', 'delete']),
  notificationIds: z.array(z.string().cuid()).min(1),
})

// POST /api/notifications/bulk - Perform bulk actions on notifications
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = bulkActionSchema.parse(body)
  
  // Get notifications to verify access permissions
  const notifications = await prisma.notification.findMany({
    where: {
      id: { in: validatedData.notificationIds },
    },
  })
  
  if (notifications.length !== validatedData.notificationIds.length) {
    return NextResponse.json(
      { error: 'Some notifications not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions for each notification
  const unauthorizedNotifications = notifications.filter(notification => {
    return !(
      user.role === UserRole.ADMIN ||
      user.role === UserRole.STAFF ||
      notification.userId === user.id
    )
  })
  
  if (unauthorizedNotifications.length > 0) {
    return NextResponse.json(
      { error: 'Access denied for some notifications' },
      { status: 403 }
    )
  }
  
  let result
  
  switch (validatedData.action) {
    case 'mark_read':
      result = await prisma.notification.updateMany({
        where: {
          id: { in: validatedData.notificationIds },
        },
        data: {
          isRead: true,
        },
      })
      break
      
    case 'mark_unread':
      result = await prisma.notification.updateMany({
        where: {
          id: { in: validatedData.notificationIds },
        },
        data: {
          isRead: false,
        },
      })
      break
      
    case 'delete':
      result = await prisma.notification.deleteMany({
        where: {
          id: { in: validatedData.notificationIds },
        },
      })
      break
  }
  
  return NextResponse.json({
    success: true,
    action: validatedData.action,
    affectedCount: result.count,
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// GET /api/notifications/bulk/unread-count - Get unread notification count
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  const userId = searchParams.get('userId')
  
  // Build where clause based on user role
  const where: Record<string, unknown> = { isRead: false }
  
  if (user.role === UserRole.PATIENT) {
    // Patients can only see their own notifications
    where.userId = user.id
  } else if (userId) {
    // Admin/Staff can check for specific user
    where.userId = userId
  } else {
    // Admin/Staff can see all unread notifications
    // No additional filter needed
  }
  
  const unreadCount = await prisma.notification.count({ where })
  
  return NextResponse.json({ unreadCount })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
