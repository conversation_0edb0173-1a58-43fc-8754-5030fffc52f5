import { NextRequest, NextResponse } from 'next/server'
import { z, ZodSchema } from 'zod'

// Common validation schemas
export const commonSchemas = {
  id: z.string().cuid('Invalid ID format'),
  email: z.string().email('Invalid email format'),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format').optional(),
  url: z.string().url('Invalid URL format'),
  date: z.string().datetime('Invalid date format'),
  positiveNumber: z.number().positive('Must be a positive number'),
  nonNegativeNumber: z.number().min(0, 'Must be non-negative'),
}

// Request validation wrapper
export function withValidation<T>(
  handler: (req: NextRequest, validatedData: T, ...args: unknown[]) => Promise<Response>,
  schema: ZodSchema<T>,
  options: {
    validateBody?: boolean
    validateQuery?: boolean
    validateParams?: boolean
  } = { validateBody: true }
) {
  return async (req: NextRequest, ...args: unknown[]): Promise<Response> => {
    try {
      let validatedData: T

      if (options.validateBody && (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH')) {
        // Validate request body
        const body = await req.json()
        validatedData = schema.parse(body)
      } else if (options.validateQuery) {
        // Validate query parameters
        const { searchParams } = new URL(req.url)
        const queryObject = Object.fromEntries(searchParams.entries())
        validatedData = schema.parse(queryObject)
      } else if (options.validateParams) {
        // Validate URL parameters
        const params = (args[0] as any)?.params || {}
        validatedData = schema.parse(params)
      } else {
        // No validation needed
        validatedData = {} as T
      }

      return await handler(req, validatedData, ...args)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: 'Validation failed',
            details: error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message,
              code: err.code,
            })),
          },
          { status: 400 }
        )
      }

      // Re-throw non-validation errors
      throw error
    }
  }
}

// Sanitization functions
export const sanitizers = {
  // Remove HTML tags and dangerous characters
  sanitizeHtml: (input: string): string => {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .trim()
  },

  // Sanitize SQL injection attempts
  sanitizeSql: (input: string): string => {
    const sqlKeywords = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
      'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', 'JAVASCRIPT'
    ]
    
    let sanitized = input
    sqlKeywords.forEach(keyword => {
      const regex = new RegExp(keyword, 'gi')
      sanitized = sanitized.replace(regex, '')
    })
    
    return sanitized.trim()
  },

  // Sanitize file names
  sanitizeFileName: (input: string): string => {
    return input
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255)
  },

  // Sanitize search queries
  sanitizeSearchQuery: (input: string): string => {
    return input
      .replace(/[<>\"'%;()&+]/g, '')
      .trim()
      .substring(0, 100)
  },
}

// Input validation schemas for common use cases
export const validationSchemas = {
  // Pagination
  pagination: z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(100).default(10),
  }),

  // Search
  search: z.object({
    query: z.string().optional().transform(val => val ? sanitizers.sanitizeSearchQuery(val) : undefined),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  }),

  // Date range
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  }).refine(data => {
    if (data.startDate && data.endDate) {
      return new Date(data.startDate) <= new Date(data.endDate)
    }
    return true
  }, {
    message: 'Start date must be before end date',
  }),

  // File upload
  fileUpload: z.object({
    fileName: z.string().min(1).transform(sanitizers.sanitizeFileName),
    fileSize: z.number().positive().max(50 * 1024 * 1024), // 50MB max
    mimeType: z.string().regex(/^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*$/),
  }),

  // Contact information
  contact: z.object({
    email: commonSchemas.email.optional(),
    phone: commonSchemas.phone.optional(),
  }).refine(data => data.email || data.phone, {
    message: 'Either email or phone must be provided',
  }),
}

// Export commonly used schemas with simpler names
export const paginationSchema = validationSchemas.pagination
export const searchSchema = validationSchemas.search

// Security validation functions
export const securityValidators = {
  // Check for suspicious patterns
  checkSuspiciousPatterns: (input: string): boolean => {
    const suspiciousPatterns = [
      /\b(union|select|insert|delete|update|drop|create|alter|exec|execute)\b/i,
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /\b(eval|setTimeout|setInterval)\s*\(/i,
    ]

    return suspiciousPatterns.some(pattern => pattern.test(input))
  },

  // Validate file type
  validateFileType: (mimeType: string, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(mimeType)
  },

  // Check password strength
  validatePasswordStrength: (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  },
}

// Middleware to validate and sanitize all string inputs
export function sanitizeRequest(req: NextRequest): NextRequest {
  // This is a simplified version - in practice, you'd need to clone the request
  // and modify the body/query parameters
  return req
}

// Rate limiting validation
export const rateLimitSchemas = {
  // Different rate limits for different operations
  auth: { requests: 5, window: 15 * 60 * 1000 }, // 5 requests per 15 minutes
  payment: { requests: 10, window: 60 * 1000 }, // 10 requests per minute
  fileUpload: { requests: 20, window: 60 * 1000 }, // 20 requests per minute
  search: { requests: 100, window: 60 * 1000 }, // 100 requests per minute
  default: { requests: 200, window: 60 * 1000 }, // 200 requests per minute
}
