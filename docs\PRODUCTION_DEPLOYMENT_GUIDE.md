# Production Deployment Guide - Google Calendar Integration

## 🚀 Secure Production Deployment

### Overview
This guide covers secure deployment of the Google Calendar integration for production environments, addressing the security concerns with service account key files in automated deployments.

## ⚠️ Security Issue Identified and Resolved

### The Problem
- Service account key files cannot be included in version control (security risk)
- Automated deployments (Vercel, Netlify, etc.) don't have access to local files
- Traditional file-based authentication doesn't work in serverless environments

### The Solution
- **Environment Variable Authentication**: Convert service account key to base64-encoded environment variable
- **Secure Credential Management**: Use platform-specific secret management
- **Fallback Authentication**: Multiple authentication methods with graceful degradation

## 🔧 Implementation Details

### 1. Environment Variable Authentication
The system now supports both file-based (development) and environment variable (production) authentication:

```typescript
// Development: Use service account key file
GOOGLE_SERVICE_ACCOUNT_KEY_FILE="./config/secrets/google-service-account-key.json"

// Production: Use base64 encoded service account key
GOOGLE_SERVICE_ACCOUNT_KEY_BASE64="ewogICJ0eXBlIjogInNlcnZpY2VfYWNjb3VudCIsCi..."
```

### 2. Authentication Priority
The system tries authentication methods in this order:
1. **Base64 Environment Variable** (Production)
2. **Service Account Key File** (Development)
3. **OAuth2 Fallback** (Limited functionality)

### 3. Encoding Service Account Key
Use the provided script to convert your service account key:

```bash
node scripts/encode-service-account.js
```

This outputs a base64 string that can be safely stored as an environment variable.

## 🌐 Platform-Specific Deployment

### Vercel Deployment

#### Step 1: Set Environment Variables
```bash
# Add the base64 encoded service account key
vercel env add GOOGLE_SERVICE_ACCOUNT_KEY_BASE64
# Paste the base64 string when prompted

# Add other required variables
vercel env add GOOGLE_CALENDAR_ID
vercel env add TIMEZONE
```

#### Step 2: Verify Configuration
The `vercel.json` file is already configured with:
- Proper build commands
- Function timeout settings
- Regional deployment (Singapore)

#### Step 3: Deploy
```bash
vercel --prod
```

### Other Platforms

#### Netlify
```bash
# Set environment variables in Netlify dashboard or CLI
netlify env:set GOOGLE_SERVICE_ACCOUNT_KEY_BASE64 "your-base64-string"
netlify env:set GOOGLE_CALENDAR_ID "primary"
netlify env:set TIMEZONE "Asia/Manila"
```

#### Railway
```bash
railway variables set GOOGLE_SERVICE_ACCOUNT_KEY_BASE64="your-base64-string"
railway variables set GOOGLE_CALENDAR_ID="primary"
railway variables set TIMEZONE="Asia/Manila"
```

#### Docker/Kubernetes
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: google-calendar-secrets
type: Opaque
data:
  GOOGLE_SERVICE_ACCOUNT_KEY_BASE64: <base64-encoded-value>
  GOOGLE_CALENDAR_ID: <base64-encoded-calendar-id>
  TIMEZONE: <base64-encoded-timezone>
```

## 🔒 Security Best Practices

### Environment Variable Security
- ✅ Use platform-specific secret management
- ✅ Never log environment variables containing credentials
- ✅ Rotate service account keys regularly (every 90 days)
- ✅ Use different service accounts for different environments

### Access Control
- ✅ Minimal required permissions for service accounts
- ✅ Separate Google Cloud projects for dev/staging/production
- ✅ Regular audit of service account usage

### Monitoring
- ✅ Monitor Google Calendar API usage and quotas
- ✅ Set up alerts for authentication failures
- ✅ Log calendar integration success/failure rates

## 🧪 Testing Production Deployment

### Pre-Deployment Checklist
- [ ] Service account key encoded to base64
- [ ] Environment variables set in deployment platform
- [ ] Google Calendar API enabled in Google Cloud project
- [ ] Service account has calendar access permissions
- [ ] Test calendar event creation locally

### Post-Deployment Verification
1. **Check Application Logs**
   ```
   ✅ Google Calendar authentication initialized successfully
   ✅ Calendar event created with ID: [event-id]
   ```

2. **Test Appointment Creation**
   - Create a test appointment through the booking form
   - Verify event appears in Google Calendar
   - Check appointment record has `calendarEventId`

3. **Monitor Error Rates**
   - Check for authentication errors
   - Monitor API quota usage
   - Verify graceful fallback behavior

## 🔄 Fallback Behavior

### When Calendar Integration Fails
- ✅ Appointment creation still succeeds
- ✅ Error is logged for debugging
- ✅ Patient can manually add to calendar
- ✅ Staff notification about integration failure

### Graceful Degradation
```typescript
// Calendar integration failure doesn't break appointment booking
try {
  const calendarEventId = await createCalendarEventForAppointment(appointment)
  if (calendarEventId) {
    // Store event ID for future operations
    await updateAppointmentWithCalendarId(appointment.id, calendarEventId)
  }
} catch (error) {
  console.error('Calendar integration failed:', error)
  // Continue with appointment creation
}
```

## 📊 Monitoring and Maintenance

### Key Metrics to Monitor
- Calendar event creation success rate
- API quota usage
- Authentication failure rate
- Patient calendar integration usage

### Regular Maintenance Tasks
- [ ] Rotate service account keys (quarterly)
- [ ] Review and update API quotas
- [ ] Monitor calendar storage usage
- [ ] Update documentation for new team members

## 🆘 Troubleshooting

### Common Issues

#### "Google Calendar not configured"
- **Cause**: Missing environment variables
- **Solution**: Verify `GOOGLE_SERVICE_ACCOUNT_KEY_BASE64` is set

#### "Failed to initialize Google Calendar auth"
- **Cause**: Invalid base64 encoding or malformed JSON
- **Solution**: Re-encode service account key using the provided script

#### "Calendar event creation returned null"
- **Cause**: Service account lacks calendar permissions
- **Solution**: Share calendar with service account email

#### "API quota exceeded"
- **Cause**: High volume of calendar operations
- **Solution**: Increase quotas in Google Cloud Console or implement rate limiting

### Emergency Procedures
1. **Disable Calendar Integration**: Set `GOOGLE_SERVICE_ACCOUNT_KEY_BASE64=""` to disable
2. **Switch to Manual Mode**: Patients can still use "Add to Calendar" buttons
3. **Rollback**: Previous deployment without calendar integration issues

## 📞 Support Contacts
- **Google Cloud Support**: For API and quota issues
- **Platform Support**: For deployment platform specific issues
- **Internal Team**: For application-specific calendar integration issues
