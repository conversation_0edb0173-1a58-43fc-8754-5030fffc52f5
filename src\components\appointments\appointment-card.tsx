'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Calendar,
  MapPin,
  MoreVertical,
  Edit,
  X
} from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'
import { AppointmentStatus } from '@prisma/client'
import { AddToCalendarButton } from './add-to-calendar-button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface AppointmentCardProps {
  appointment: AppointmentDetails
  onReschedule?: (appointment: AppointmentDetails) => void
  onCancel?: (appointment: AppointmentDetails) => void
  onViewDetails?: (appointmentId: string) => void
  showActions?: boolean
}

export function AppointmentCard({ 
  appointment, 
  onReschedule, 
  onCancel, 
  onViewDetails,
  showActions = true 
}: AppointmentCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    })
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Manila'
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case 'SCHEDULED':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'CONFIRMED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'COMPLETED':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'NO_SHOW':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const canReschedule = appointment.status === 'SCHEDULED' || appointment.status === 'CONFIRMED'
  const canCancel = appointment.status === 'SCHEDULED' || appointment.status === 'CONFIRMED'
  const isUpcoming = new Date(appointment.scheduledAt) > new Date()

  return (
    <Card className="hover:shadow-lg transition-all duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <CardTitle className="text-lg text-gray-900">
                {appointment.service.name}
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Badge className={getStatusColor(appointment.status)}>
                  {appointment.status.replace('_', ' ')}
                </Badge>
                {showActions && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewDetails?.(appointment.id)}>
                        View Details
                      </DropdownMenuItem>
                      {canReschedule && isUpcoming && (
                        <DropdownMenuItem onClick={() => onReschedule?.(appointment)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Reschedule
                        </DropdownMenuItem>
                      )}
                      {canCancel && isUpcoming && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onCancel?.(appointment)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <X className="h-4 w-4 mr-2" />
                            Cancel
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
            <CardDescription>
              {appointment.service.category.replace('_', ' ')} • {formatPrice(appointment.service.price)}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Date & Time */}
        <div className="flex items-center space-x-3">
          <Calendar className="h-4 w-4 text-gray-400" />
          <div>
            <p className="font-medium text-gray-900">{formatDate(appointment.scheduledAt)}</p>
            <p className="text-sm text-gray-600">
              {formatTime(appointment.scheduledAt)} ({formatDuration(appointment.duration)})
            </p>
          </div>
        </div>

        {/* Dentist */}
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
              {getInitials(appointment.dentist.user.name)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-gray-900">Dr. {appointment.dentist.user.name}</p>
            {appointment.dentist.specialization && (
              <p className="text-sm text-gray-600">{appointment.dentist.specialization}</p>
            )}
          </div>
        </div>

        {/* Location */}
        <div className="flex items-start space-x-3">
          <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
          <div>
            <p className="font-medium text-gray-900">{appointment.branch.name}</p>
            <p className="text-sm text-gray-600">{appointment.branch.address}</p>
          </div>
        </div>

        {/* Expandable Details */}
        {(appointment.symptoms || appointment.notes) && (
          <div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-blue-600 hover:text-blue-700 p-0 h-auto"
            >
              {isExpanded ? 'Hide' : 'Show'} Additional Information
            </Button>
            
            {isExpanded && (
              <div className="mt-3 space-y-2 p-3 bg-gray-50 rounded-md">
                {appointment.symptoms && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Symptoms/Concerns:</p>
                    <p className="text-sm text-gray-600">{appointment.symptoms}</p>
                  </div>
                )}
                {appointment.notes && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Additional Notes:</p>
                    <p className="text-sm text-gray-600">{appointment.notes}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Add to Calendar Button */}
        <div className="pt-2">
          <AddToCalendarButton
            appointment={appointment}
            variant="outline"
            size="sm"
            className="w-full sm:w-auto"
          />
        </div>

        {/* Action Buttons for Mobile */}
        {showActions && (canReschedule || canCancel) && isUpcoming && (
          <div className="flex space-x-2 pt-2 sm:hidden">
            {canReschedule && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onReschedule?.(appointment)}
                className="flex-1"
              >
                <Edit className="h-4 w-4 mr-1" />
                Reschedule
              </Button>
            )}
            {canCancel && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCancel?.(appointment)}
                className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
