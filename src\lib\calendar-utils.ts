import { AppointmentDetails } from '@/types/appointment'

export interface CalendarEvent {
  title: string
  description: string
  location: string
  startTime: Date
  endTime: Date
  timezone: string
}

// Get the configured timezone from environment variable
export function getTimezone(): string {
  return process.env.TIMEZONE || 'Asia/Manila'
}

// Convert a Date to Philippine timezone and format for calendar URLs
export function formatDateInPhilippineTime(date: Date): string {
  // Create a new date in Philippine timezone
  const philippineTime = new Date(date.toLocaleString("en-US", {timeZone: "Asia/Manila"}))

  // Format as YYYYMMDDTHHMMSS (local time, not UTC)
  const year = philippineTime.getFullYear()
  const month = String(philippineTime.getMonth() + 1).padStart(2, '0')
  const day = String(philippineTime.getDate()).padStart(2, '0')
  const hours = String(philippineTime.getHours()).padStart(2, '0')
  const minutes = String(philippineTime.getMinutes()).padStart(2, '0')
  const seconds = String(philippineTime.getSeconds()).padStart(2, '0')

  return `${year}${month}${day}T${hours}${minutes}${seconds}`
}

// Format date for ICS files with proper timezone
export function formatDateForICS(date: Date, includeTimezone: boolean = true): string {
  if (includeTimezone) {
    // For ICS files, we need to specify the timezone
    const philippineTime = new Date(date.toLocaleString("en-US", {timeZone: "Asia/Manila"}))
    const year = philippineTime.getFullYear()
    const month = String(philippineTime.getMonth() + 1).padStart(2, '0')
    const day = String(philippineTime.getDate()).padStart(2, '0')
    const hours = String(philippineTime.getHours()).padStart(2, '0')
    const minutes = String(philippineTime.getMinutes()).padStart(2, '0')
    const seconds = String(philippineTime.getSeconds()).padStart(2, '0')

    return `${year}${month}${day}T${hours}${minutes}${seconds}`
  } else {
    // UTC format for DTSTAMP
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'
  }
}

export interface CalendarUrls {
  google: string
  outlook: string
  outlookDesktop: string
  yahoo: string
  ics: string
}

/**
 * Create a calendar event object from appointment details
 */
export function createCalendarEvent(appointment: AppointmentDetails): CalendarEvent {
  const startTime = new Date(appointment.scheduledAt)
  const endTime = new Date(startTime.getTime() + appointment.duration * 60000)

  const title = `${appointment.service.name} - Bright Smile Dental`
  const location = `${appointment.branch.name}, ${appointment.branch.address}`
  
  let description = `Dental Appointment Details:\n\n`
  description += `Service: ${appointment.service.name}\n`
  description += `Dentist: Dr. ${appointment.dentist.user.name}\n`
  description += `Duration: ${appointment.duration} minutes\n`
  description += `Location: ${appointment.branch.name}\n`
  description += `Address: ${appointment.branch.address}\n\n`

  if (appointment.symptoms) {
    description += `Symptoms/Concerns: ${appointment.symptoms}\n\n`
  }

  if (appointment.notes) {
    description += `Additional Notes: ${appointment.notes}\n\n`
  }

  description += `Please arrive 15 minutes early for check-in.\n\n`
  description += `Appointment ID: ${appointment.id}`

  return {
    title,
    description,
    location,
    startTime,
    endTime,
    timezone: 'Asia/Manila'
  }
}

/**
 * Format date for calendar URLs (YYYYMMDDTHHMMSS in Philippine timezone)
 */
export function formatDateForCalendar(date: Date): string {
  return formatDateInPhilippineTime(date)
}

/**
 * Format date for Yahoo Calendar (YYYYMMDDTHHMMSS in Philippine timezone)
 */
export function formatDateForYahoo(date: Date): string {
  return formatDateInPhilippineTime(date)
}

/**
 * Generate calendar URLs for different platforms
 */
export function generateCalendarUrls(appointment: AppointmentDetails): CalendarUrls {
  const event = createCalendarEvent(appointment)
  const startFormatted = formatDateForCalendar(event.startTime)
  const endFormatted = formatDateForCalendar(event.endTime)

  // For Outlook web, we need to convert Philippine time to ISO format
  // but specify the timezone in the URL parameters
  const startPhilippineISO = new Date(event.startTime.toLocaleString("en-US", {timeZone: "Asia/Manila"})).toISOString()
  const endPhilippineISO = new Date(event.endTime.toLocaleString("en-US", {timeZone: "Asia/Manila"})).toISOString()

  // Google Calendar URL (uses local time format)
  const googleUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(event.title)}&dates=${startFormatted}/${endFormatted}&details=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}&ctz=${encodeURIComponent(getTimezone())}`

  // Outlook Web URL (uses ISO format but we'll specify timezone)
  const outlookUrl = `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(event.title)}&startdt=${startPhilippineISO}&enddt=${endPhilippineISO}&body=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}`

  // Outlook Desktop URL (uses local time format)
  const outlookDesktopUrl = `outlook://calendar/action/compose?subject=${encodeURIComponent(event.title)}&startdt=${startFormatted}&enddt=${endFormatted}&body=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}`

  // Yahoo Calendar URL (uses local time format)
  const duration = Math.floor(appointment.duration / 60)
  const yahooUrl = `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${encodeURIComponent(event.title)}&st=${formatDateForYahoo(event.startTime)}&dur=${duration.toString().padStart(2, '0')}00&desc=${encodeURIComponent(event.description)}&in_loc=${encodeURIComponent(event.location)}`

  // ICS download URL
  const icsUrl = `/api/appointments/${appointment.id}/calendar`

  return {
    google: googleUrl,
    outlook: outlookUrl,
    outlookDesktop: outlookDesktopUrl,
    yahoo: yahooUrl,
    ics: icsUrl
  }
}

/**
 * Generate ICS file content with proper Philippine timezone
 */
export function generateICSContent(appointment: AppointmentDetails): string {
  const event = createCalendarEvent(appointment)
  const timezone = getTimezone()

  // Format dates for ICS with timezone
  const startFormatted = formatDateForICS(event.startTime, true)
  const endFormatted = formatDateForICS(event.endTime, true)
  const nowFormatted = formatDateForICS(new Date(), false) // DTSTAMP should be in UTC

  const icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Bright Smile Dental//Appointment//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH',
    // Add timezone definition
    'BEGIN:VTIMEZONE',
    `TZID:${timezone}`,
    'BEGIN:STANDARD',
    'DTSTART:19700101T000000',
    'TZOFFSETFROM:+0800',
    'TZOFFSETTO:+0800',
    'TZNAME:PST',
    'END:STANDARD',
    'END:VTIMEZONE',
    'BEGIN:VEVENT',
    `UID:appointment-${appointment.id}@brightsmile.com`,
    `DTSTAMP:${nowFormatted}`,
    `DTSTART;TZID=${timezone}:${startFormatted}`,
    `DTEND;TZID=${timezone}:${endFormatted}`,
    `SUMMARY:${event.title}`,
    `DESCRIPTION:${event.description.replace(/\n/g, '\\n')}`,
    `LOCATION:${event.location}`,
    'STATUS:CONFIRMED',
    'TRANSP:OPAQUE',
    'CATEGORIES:APPOINTMENT,HEALTHCARE',
    'BEGIN:VALARM',
    'TRIGGER:-PT1H',
    'ACTION:DISPLAY',
    'DESCRIPTION:Appointment reminder - Please arrive 15 minutes early',
    'END:VALARM',
    'BEGIN:VALARM',
    'TRIGGER:-P1D',
    'ACTION:DISPLAY',
    'DESCRIPTION:Appointment tomorrow - Bright Smile Dental',
    'END:VALARM',
    'END:VEVENT',
    'END:VCALENDAR'
  ].join('\r\n')

  return icsContent
}

/**
 * Download ICS file
 */
export function downloadICSFile(appointment: AppointmentDetails): void {
  const icsContent = generateICSContent(appointment)
  const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `appointment-${appointment.id}.ics`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * Open calendar URL in new window/tab
 */
export function openCalendarUrl(url: string): void {
  window.open(url, '_blank', 'noopener,noreferrer')
}

/**
 * Open calendar app (for desktop apps)
 */
export function openCalendarApp(url: string): void {
  window.location.href = url
}

/**
 * Detect user's platform for smart calendar suggestions
 */
export function detectPlatform(): 'ios' | 'android' | 'mac' | 'windows' | 'linux' | 'unknown' {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (/iphone|ipad|ipod/.test(userAgent)) return 'ios'
  if (/android/.test(userAgent)) return 'android'
  if (/mac/.test(userAgent)) return 'mac'
  if (/win/.test(userAgent)) return 'windows'
  if (/linux/.test(userAgent)) return 'linux'
  
  return 'unknown'
}

/**
 * Get recommended calendar apps based on platform
 */
export function getRecommendedCalendars(platform: ReturnType<typeof detectPlatform>): string[] {
  switch (platform) {
    case 'ios':
      return ['Apple Calendar', 'Google Calendar', 'Outlook']
    case 'android':
      return ['Google Calendar', 'Outlook', 'Samsung Calendar']
    case 'mac':
      return ['Apple Calendar', 'Google Calendar', 'Outlook']
    case 'windows':
      return ['Outlook', 'Google Calendar', 'Windows Calendar']
    default:
      return ['Google Calendar', 'Outlook', 'Yahoo Calendar']
  }
}

/**
 * Check if calendar app is likely installed
 */
export function isCalendarAppLikelyInstalled(app: string, platform: ReturnType<typeof detectPlatform>): boolean {
  switch (app) {
    case 'Apple Calendar':
      return platform === 'ios' || platform === 'mac'
    case 'Outlook':
      return true // Outlook is available on all platforms
    case 'Google Calendar':
      return true // Google Calendar is web-based and available everywhere
    default:
      return false
  }
}
