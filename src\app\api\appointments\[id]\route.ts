import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { appointmentUpdateSchema } from '@/lib/validations'
import { UserRole, AppointmentStatus } from '@prisma/client'

// GET /api/appointments/[id] - Get appointment by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const appointment = await prisma.appointment.findUnique({
    where: { id },
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
      },
      dentist: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      branch: {
        select: {
          id: true,
          name: true,
          address: true,
          phone: true,
        },
      },
      service: {
        select: {
          id: true,
          name: true,
          category: true,
          duration: true,
          price: true,
          description: true,
        },
      },
      treatments: {
        include: {
          prescriptions: true,
          files: true,
        },
      },
    },
  })
  
  if (!appointment) {
    return NextResponse.json(
      { error: 'Appointment not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canAccess = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    (user.role === UserRole.DENTIST && appointment.dentist.userId === user.id) ||
    (user.role === UserRole.PATIENT && appointment.patient.userId === user.id)
  
  if (!canAccess) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  return NextResponse.json(appointment)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/appointments/[id] - Update appointment
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  const existingAppointment = await prisma.appointment.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
      dentist: { include: { user: true } },
    },
  })
  
  if (!existingAppointment) {
    return NextResponse.json(
      { error: 'Appointment not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canUpdate = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    (user.role === UserRole.DENTIST && existingAppointment.dentist.userId === user.id) ||
    (user.role === UserRole.PATIENT && existingAppointment.patient.userId === user.id)
  
  if (!canUpdate) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Patients can only update certain fields
  let allowedFields = body
  if (user.role === UserRole.PATIENT) {
    allowedFields = {
      notes: body.notes,
      symptoms: body.symptoms,
    }
    
    // Patients can only cancel their own appointments
    if (body.status === AppointmentStatus.CANCELLED) {
      allowedFields.status = AppointmentStatus.CANCELLED
      allowedFields.cancelReason = body.cancelReason
    }
  }
  
  const validatedData = appointmentUpdateSchema.parse(allowedFields)
  
  // If rescheduling, check for conflicts
  if (validatedData.scheduledAt && validatedData.scheduledAt !== existingAppointment.scheduledAt) {
    const duration = validatedData.duration || existingAppointment.duration
    const conflictingAppointment = await prisma.appointment.findFirst({
      where: {
        id: { not: id },
        dentistId: existingAppointment.dentistId,
        scheduledAt: {
          gte: validatedData.scheduledAt,
          lt: new Date(validatedData.scheduledAt.getTime() + duration * 60000),
        },
        status: {
          in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS],
        },
      },
    })
    
    if (conflictingAppointment) {
      return NextResponse.json(
        { error: 'Time slot is not available' },
        { status: 409 }
      )
    }
  }
  
  const updatedAppointment = await prisma.appointment.update({
    where: { id },
    data: validatedData,
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
      },
      dentist: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      branch: {
        select: {
          id: true,
          name: true,
          address: true,
        },
      },
      service: {
        select: {
          id: true,
          name: true,
          category: true,
          duration: true,
          price: true,
        },
      },
    },
  })
  
  // TODO: Send notification about appointment update
  
  return NextResponse.json(updatedAppointment)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// DELETE /api/appointments/[id] - Cancel/Delete appointment
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const appointment = await prisma.appointment.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
      dentist: { include: { user: true } },
    },
  })
  
  if (!appointment) {
    return NextResponse.json(
      { error: 'Appointment not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canDelete = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    (user.role === UserRole.PATIENT && appointment.patient.userId === user.id)
  
  if (!canDelete) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Instead of hard delete, mark as cancelled
  await prisma.appointment.update({
    where: { id },
    data: {
      status: AppointmentStatus.CANCELLED,
      cancelReason: 'Cancelled by user',
    },
  })
  
  // TODO: Send cancellation notification
  
  return NextResponse.json({ message: 'Appointment cancelled successfully' })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.PATIENT])
