'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { PatientList } from '@/components/patients/patient-list'
import { PatientStatisticsCards } from '@/components/patients/patient-statistics'
import { PatientDetailModal } from '@/components/patients/patient-detail-modal'
import { PatientEditModal } from '@/components/patients/patient-edit-modal'
import { PatientWithCounts } from '@/types/patient'
import { UserRole } from '@prisma/client'
import { Loader2 } from 'lucide-react'

export default function DentistPatients() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [selectedPatient, setSelectedPatient] = useState<PatientWithCounts | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/patients')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  const handleViewPatient = (patient: PatientWithCounts) => {
    setSelectedPatient(patient)
    setIsDetailModalOpen(true)
  }

  const handleEditPatient = (patient: PatientWithCounts) => {
    setSelectedPatient(patient)
    setIsEditModalOpen(true)
  }

  const handleDeletePatient = (patient: PatientWithCounts) => {
    // TODO: Show confirmation dialog and delete patient
    console.log('Delete patient:', patient)
  }

  const handleAddPatient = () => {
    // TODO: Open add patient modal or navigate to add patient page
    console.log('Add new patient')
  }

  const handleRefreshData = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false)
    setSelectedPatient(null)
  }

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false)
    setSelectedPatient(null)
  }

  const handleEditFromDetail = (_patientId: string) => {
    setIsDetailModalOpen(false)
    setIsEditModalOpen(true)
  }

  const handleSavePatient = () => {
    handleRefreshData()
    setIsEditModalOpen(false)
    setSelectedPatient(null)
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patient Records</h1>
          <p className="text-gray-600 mt-2">
            Manage and view patient information, medical history, and treatment records
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <PatientStatisticsCards key={refreshKey} />

      {/* Patient List */}
      <PatientList
        key={refreshKey}
        onViewPatient={handleViewPatient}
        onEditPatient={handleEditPatient}
        onDeletePatient={handleDeletePatient}
        onAddPatient={handleAddPatient}
      />

      {/* Patient Detail Modal */}
      <PatientDetailModal
        patientId={selectedPatient?.id || null}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        onEdit={handleEditFromDetail}
      />

      {/* Patient Edit Modal */}
      <PatientEditModal
        patientId={selectedPatient?.id || null}
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        onSave={handleSavePatient}
      />
    </div>
  )
}
