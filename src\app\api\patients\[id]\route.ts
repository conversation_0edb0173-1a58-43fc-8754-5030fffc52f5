import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { patientProfileUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/patients/[id] - Get patient profile by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const patientProfile = await prisma.patientProfile.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          image: true,
          phone: true,
          status: true,
          createdAt: true,
        },
      },
      appointments: {
        orderBy: { scheduledAt: 'desc' },
        take: 10,
        include: {
          service: { select: { name: true, category: true } },
          dentist: { 
            include: { 
              user: { select: { name: true } } 
            } 
          },
          branch: { select: { name: true, address: true } },
        },
      },
      treatments: {
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          service: { select: { name: true, category: true } },
          dentist: { 
            include: { 
              user: { select: { name: true } } 
            } 
          },
          prescriptions: true,
        },
      },
      invoices: {
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          items: {
            include: {
              treatment: {
                include: {
                  service: { select: { name: true } },
                },
              },
            },
          },
          payments: true,
        },
      },
      dentalChart: {
        include: {
          teeth: {
            orderBy: { toothNumber: 'asc' },
          },
        },
      },
      files: {
        orderBy: { createdAt: 'desc' },
        take: 20,
      },
    },
  })
  
  if (!patientProfile) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, id, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  return NextResponse.json(patientProfile)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/patients/[id] - Update patient profile
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  // Check if patient exists
  const existingProfile = await prisma.patientProfile.findUnique({
    where: { id },
    include: { user: true },
  })
  
  if (!existingProfile) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, id, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const validatedData = patientProfileUpdateSchema.parse(body)
  
  const updatedProfile = await prisma.patientProfile.update({
    where: { id },
    data: validatedData,
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          phone: true,
        },
      },
    },
  })
  
  return NextResponse.json(updatedProfile)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.PATIENT])

// DELETE /api/patients/[id] - Delete patient profile (Admin only)
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const patientProfile = await prisma.patientProfile.findUnique({
    where: { id },
    include: { user: true },
  })
  
  if (!patientProfile) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Delete the entire user account (cascade will handle profile deletion)
  await prisma.user.delete({
    where: { id: patientProfile.userId },
  })
  
  return NextResponse.json({ message: 'Patient deleted successfully' })
}, [UserRole.ADMIN])
