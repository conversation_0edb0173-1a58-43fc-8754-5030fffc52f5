import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { branchUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/branches/[id] - Get branch by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const branch = await prisma.branch.findUnique({
    where: { id },
    include: {
      dentists: {
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
              schedules: {
                where: { isActive: true },
                orderBy: { dayOfWeek: 'asc' },
              },
            },
          },
        },
      },
      staff: {
        include: {
          staff: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  phone: true,
                },
              },
            },
          },
        },
      },
      services: {
        include: {
          service: {
            select: {
              id: true,
              name: true,
              description: true,
              category: true,
              duration: true,
              price: true,
              isActive: true,
            },
          },
        },
      },
      appointments: {
        where: {
          scheduledAt: {
            gte: new Date(),
          },
        },
        orderBy: { scheduledAt: 'asc' },
        take: 10,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          dentist: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          service: {
            select: {
              name: true,
              category: true,
            },
          },
        },
      },
      _count: {
        select: {
          appointments: true,
          staff: true,
          dentists: true,
        },
      },
    },
  })
  
  if (!branch) {
    return NextResponse.json(
      { error: 'Branch not found' },
      { status: 404 }
    )
  }
  
  return NextResponse.json(branch)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/branches/[id] - Update branch
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  const existingBranch = await prisma.branch.findUnique({
    where: { id },
  })
  
  if (!existingBranch) {
    return NextResponse.json(
      { error: 'Branch not found' },
      { status: 404 }
    )
  }
  
  const validatedData = branchUpdateSchema.parse(body)
  
  const updatedBranch = await prisma.branch.update({
    where: { id },
    data: validatedData,
    include: {
      _count: {
        select: {
          appointments: true,
          staff: true,
          dentists: true,
        },
      },
    },
  })
  
  return NextResponse.json(updatedBranch)
}, [UserRole.ADMIN, UserRole.STAFF])

// DELETE /api/branches/[id] - Delete branch (Admin only)
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const branch = await prisma.branch.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          appointments: true,
          staff: true,
          dentists: true,
        },
      },
    },
  })
  
  if (!branch) {
    return NextResponse.json(
      { error: 'Branch not found' },
      { status: 404 }
    )
  }
  
  // Check if branch has active appointments
  const activeAppointments = await prisma.appointment.count({
    where: {
      branchId: id,
      scheduledAt: {
        gte: new Date(),
      },
      status: {
        in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'],
      },
    },
  })
  
  if (activeAppointments > 0) {
    return NextResponse.json(
      { error: 'Cannot delete branch with active appointments' },
      { status: 400 }
    )
  }
  
  await prisma.branch.delete({
    where: { id },
  })
  
  return NextResponse.json({ message: 'Branch deleted successfully' })
}, [UserRole.ADMIN])
