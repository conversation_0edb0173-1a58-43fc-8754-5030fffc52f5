import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { appointmentCreateSchema, paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole, AppointmentStatus } from '@prisma/client'

// GET /api/appointments - List appointments with pagination and filtering
export const GET = withAuth(async (req: NextRequest, user) => {
  try {
    const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'scheduledAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const patientId = searchParams.get('patientId')
  const dentistId = searchParams.get('dentistId')
  const branchId = searchParams.get('branchId')
  const statusParam = searchParams.get('status')
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')

  // Parse status parameter - can be single status or comma-separated list
  let statusFilter: AppointmentStatus[] | AppointmentStatus | null = null
  if (statusParam) {
    const statusValues = statusParam.split(',').map(s => s.trim())
    // Validate that all status values are valid AppointmentStatus enum values
    const validStatuses = statusValues.filter(status =>
      Object.values(AppointmentStatus).includes(status as AppointmentStatus)
    ) as AppointmentStatus[]

    if (validStatuses.length > 0) {
      statusFilter = validStatuses.length === 1 ? validStatuses[0] : validStatuses
    }
  }
  
  const skip = (page - 1) * limit
  
  // Build where clause based on user role and filters
  const where: Record<string, unknown> = {}
  
  // Role-based filtering
  if (user.role === UserRole.PATIENT) {
    // Patients can only see their own appointments
    const patientProfile = await prisma.patientProfile.findUnique({
      where: { userId: user.id },
    })
    if (!patientProfile) {
      return NextResponse.json({ appointments: [], pagination: { page, limit, total: 0, pages: 0 } })
    }
    where.patientId = patientProfile.id
  } else if (user.role === UserRole.DENTIST) {
    // Dentists can see their own appointments
    const dentistProfile = await prisma.dentistProfile.findUnique({
      where: { userId: user.id },
    })
    if (!dentistProfile) {
      return NextResponse.json({ appointments: [], pagination: { page, limit, total: 0, pages: 0 } })
    }
    where.dentistId = dentistProfile.id
  }
  
  // Additional filters
  if (patientId) where.patientId = patientId
  if (dentistId) where.dentistId = dentistId
  if (branchId) where.branchId = branchId
  if (statusFilter) {
    if (Array.isArray(statusFilter)) {
      where.status = { in: statusFilter }
    } else {
      where.status = statusFilter
    }
  }
  
  // Date range filter
  if (dateFrom || dateTo) {
    where.scheduledAt = {}
    if (dateFrom) (where.scheduledAt as any).gte = new Date(dateFrom)
    if (dateTo) (where.scheduledAt as any).lte = new Date(dateTo)
  }
  
  // Search filter
  if (query) {
    where.OR = [
      { notes: { contains: query, mode: 'insensitive' } },
      { symptoms: { contains: query, mode: 'insensitive' } },
      { patient: { user: { name: { contains: query, mode: 'insensitive' } } } },
      { dentist: { user: { name: { contains: query, mode: 'insensitive' } } } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'scheduledAt' ? { scheduledAt: sortOrder } :
                  sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'status' ? { status: sortOrder } :
                  { scheduledAt: sortOrder as 'asc' | 'desc' }

  const [appointments, total] = await Promise.all([
    prisma.appointment.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
              },
            },
          },
        },
        dentist: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
        service: {
          select: {
            id: true,
            name: true,
            category: true,
            duration: true,
            price: true,
          },
        },
      },
    }),
    prisma.appointment.count({ where }),
  ])
  
  return NextResponse.json({
    appointments,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
  } catch (error) {
    console.error('Error fetching appointments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch appointments' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/appointments - Create new appointment
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = appointmentCreateSchema.parse(body)
  
  // Verify patient exists
  const patient = await prisma.patientProfile.findUnique({
    where: { id: validatedData.patientId },
    include: { user: true },
  })
  
  if (!patient) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Patients can only book appointments for themselves
  if (user.role === UserRole.PATIENT && patient.userId !== user.id) {
    return NextResponse.json(
      { error: 'You can only book appointments for yourself' },
      { status: 403 }
    )
  }
  
  // Verify dentist exists and is available
  const dentist = await prisma.dentistProfile.findUnique({
    where: { id: validatedData.dentistId },
    include: { user: true },
  })
  
  if (!dentist || !dentist.isAvailable) {
    return NextResponse.json(
      { error: 'Dentist not found or not available' },
      { status: 404 }
    )
  }
  
  // Verify branch and service exist
  const [branch, service] = await Promise.all([
    prisma.branch.findUnique({ where: { id: validatedData.branchId } }),
    prisma.service.findUnique({ where: { id: validatedData.serviceId } }),
  ])
  
  if (!branch || !service) {
    return NextResponse.json(
      { error: 'Branch or service not found' },
      { status: 404 }
    )
  }
  
  // Check for scheduling conflicts
  const conflictingAppointment = await prisma.appointment.findFirst({
    where: {
      dentistId: validatedData.dentistId,
      scheduledAt: {
        gte: validatedData.scheduledAt,
        lt: new Date(validatedData.scheduledAt.getTime() + validatedData.duration * 60000),
      },
      status: {
        in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS],
      },
    },
  })
  
  if (conflictingAppointment) {
    return NextResponse.json(
      { error: 'Time slot is not available' },
      { status: 409 }
    )
  }
  
  // Create appointment
  const appointment = await prisma.appointment.create({
    data: validatedData,
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
      },
      dentist: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      branch: {
        select: {
          id: true,
          name: true,
          address: true,
        },
      },
      service: {
        select: {
          id: true,
          name: true,
          category: true,
          duration: true,
          price: true,
        },
      },
    },
  })

  // Create Google Calendar event (optional - don't fail if it doesn't work)
  try {
    const { createCalendarEventForAppointment } = await import('@/lib/google-calendar')
    const calendarEventId = await createCalendarEventForAppointment(appointment as any)

    if (calendarEventId) {
      // Store the calendar event ID in the dedicated field
      await prisma.appointment.update({
        where: { id: appointment.id },
        data: {
          calendarEventId: calendarEventId
        },
      })
      console.log(`Calendar event created with ID: ${calendarEventId}`)
    } else {
      console.warn('Calendar event creation returned null - check Google Calendar configuration')
    }
  } catch (error) {
    console.error('Failed to create calendar event:', error)
    // Continue without failing the appointment creation
  }

  // TODO: Send appointment confirmation notification

  return NextResponse.json(appointment, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
