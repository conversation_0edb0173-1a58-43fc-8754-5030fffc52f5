import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'
import { UserRole } from '@prisma/client'

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Rate limiting configuration
const RATE_LIMITS = {
  '/api/auth': { requests: 10, window: 60 * 1000 }, // 10 requests per minute
  '/api/appointments': { requests: 100, window: 60 * 1000 }, // 100 requests per minute
  '/api/payments': { requests: 20, window: 60 * 1000 }, // 20 requests per minute
  '/api/notifications': { requests: 50, window: 60 * 1000 }, // 50 requests per minute
  default: { requests: 200, window: 60 * 1000 }, // 200 requests per minute for other endpoints
}

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/api/auth',
  '/api/health',
  '/api/services', // GET only for public viewing
]

// Admin-only routes
const ADMIN_ROUTES = [
  '/api/users',
  '/api/analytics',
  '/api/inventory',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for non-API routes
  if (!pathname.startsWith('/api/')) {
    return NextResponse.next()
  }
  
  // Skip middleware for public routes
  if (PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    // Still apply rate limiting to public routes
    const rateLimitResponse = await applyRateLimit(request)
    if (rateLimitResponse) return rateLimitResponse
    
    return NextResponse.next()
  }
  
  // Apply rate limiting
  const rateLimitResponse = await applyRateLimit(request)
  if (rateLimitResponse) return rateLimitResponse
  
  // Check authentication
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })
  
  if (!token) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  }
  
  // Check authorization for admin routes
  if (ADMIN_ROUTES.some(route => pathname.startsWith(route))) {
    if (token.role !== UserRole.ADMIN && token.role !== UserRole.STAFF) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }
  }
  
  // Add user info to headers for API routes
  const response = NextResponse.next()
  response.headers.set('x-user-id', token.sub || '')
  response.headers.set('x-user-role', token.role as string || '')
  
  return response
}

async function applyRateLimit(request: NextRequest): Promise<NextResponse | null> {
  const ip = (request as { ip?: string }).ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
  const { pathname } = request.nextUrl
  
  // Determine rate limit for this endpoint
  let rateLimit = RATE_LIMITS.default
  for (const [route, limit] of Object.entries(RATE_LIMITS)) {
    if (route !== 'default' && pathname.startsWith(route)) {
      rateLimit = limit
      break
    }
  }
  
  const key = `${ip}:${pathname}`
  const now = Date.now()
  const windowStart = now - rateLimit.window
  
  // Clean up old entries
  for (const [k, v] of rateLimitStore.entries()) {
    if (v.resetTime < windowStart) {
      rateLimitStore.delete(k)
    }
  }
  
  // Get current count for this IP/endpoint
  const current = rateLimitStore.get(key)
  
  if (!current) {
    // First request in window
    rateLimitStore.set(key, { count: 1, resetTime: now + rateLimit.window })
    return null
  }
  
  if (current.resetTime < now) {
    // Window has expired, reset
    rateLimitStore.set(key, { count: 1, resetTime: now + rateLimit.window })
    return null
  }
  
  if (current.count >= rateLimit.requests) {
    // Rate limit exceeded
    return NextResponse.json(
      { 
        error: 'Rate limit exceeded',
        retryAfter: Math.ceil((current.resetTime - now) / 1000)
      },
      { 
        status: 429,
        headers: {
          'Retry-After': Math.ceil((current.resetTime - now) / 1000).toString(),
          'X-RateLimit-Limit': rateLimit.requests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': current.resetTime.toString(),
        }
      }
    )
  }
  
  // Increment count
  current.count++
  rateLimitStore.set(key, current)
  
  return null
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
