import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

const fileUpdateSchema = z.object({
  description: z.string().optional(),
})

// GET /api/files/patient/[id] - Get patient file by ID or download file
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const { searchParams } = new URL(req.url)
  const download = searchParams.get('download') === 'true'

  const file = await prisma.patientFile.findUnique({
    where: { id },
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  })

  if (!file) {
    return NextResponse.json(
      { error: 'File not found' },
      { status: 404 }
    )
  }

  // Check access permissions
  if (!canAccessPatientData(user.role, file.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }

  // If download is requested, redirect to file URL
  if (download) {
    return NextResponse.redirect(file.url)
  }

  return NextResponse.json(file)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/files/patient/[id] - Update patient file metadata
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  const existingFile = await prisma.patientFile.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
    },
  })
  
  if (!existingFile) {
    return NextResponse.json(
      { error: 'File not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, existingFile.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const validatedData = fileUpdateSchema.parse(body)
  
  const updatedFile = await prisma.patientFile.update({
    where: { id },
    data: validatedData,
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  })
  
  return NextResponse.json(updatedFile)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// DELETE /api/files/patient/[id] - Delete patient file
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const file = await prisma.patientFile.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
    },
  })
  
  if (!file) {
    return NextResponse.json(
      { error: 'File not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canDelete = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    (user.role === UserRole.PATIENT && file.patient.userId === user.id) ||
    file.uploadedBy === user.id
  
  if (!canDelete) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  await prisma.patientFile.delete({
    where: { id },
  })
  
  // TODO: Delete actual file from storage (UploadThing, AWS S3, etc.)
  
  return NextResponse.json({ message: 'File deleted successfully' })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])


