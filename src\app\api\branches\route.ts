import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { branchCreateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'
import { paginationSchema, searchSchema } from '@/lib/validation-middleware'

// GET /api/branches - List branches with pagination and filtering
export const GET = withAuth(async (req: NextRequest) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'name',
    sortOrder: searchParams.get('sortOrder') || 'asc',
  })
  
  const isActive = searchParams.get('isActive')
  
  // Build where clause
  const where: any = {}
  
  if (query) {
    where.OR = [
      { name: { contains: query, mode: 'insensitive' } },
      { address: { contains: query, mode: 'insensitive' } },
      { description: { contains: query, mode: 'insensitive' } },
    ]
  }
  
  if (isActive !== null) {
    where.isActive = isActive === 'true'
  }
  
  // Get total count for pagination
  const totalCount = await prisma.branch.count({ where })
  
  // Get branches with pagination
  const branches = await prisma.branch.findMany({
    where,
    orderBy: { [sortBy as keyof typeof prisma.branch.fields]: sortOrder },
    skip: (page - 1) * limit,
    take: limit,
    include: {
      dentists: {
        include: {
          dentist: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      },
      services: {
        include: {
          service: {
            select: {
              id: true,
              name: true,
              category: true,
              duration: true,
              price: true,
            },
          },
        },
      },
      _count: {
        select: {
          appointments: true,
          staff: true,
        },
      },
    },
  })
  
  return NextResponse.json({
    branches,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/branches - Create new branch
export const POST = withAuth(async (req: NextRequest) => {
  const body = await req.json()
  const validatedData = branchCreateSchema.parse(body)
  
  // Create branch
  const branch = await prisma.branch.create({
    data: validatedData,
    include: {
      _count: {
        select: {
          appointments: true,
          staff: true,
          dentists: true,
        },
      },
    },
  })
  
  return NextResponse.json(branch, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF])
