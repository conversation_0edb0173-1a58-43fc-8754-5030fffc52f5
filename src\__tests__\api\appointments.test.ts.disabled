import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/appointments/route'
import { prisma } from '@/lib/prisma'
import { getToken } from 'next-auth/jwt'
import { UserRole, AppointmentStatus } from '@prisma/client'
import * as authUtils from '@/lib/auth-utils'

jest.mock('@/lib/prisma')
jest.mock('next-auth/jwt')
jest.mock('@/lib/auth-utils')

const mockPrisma = prisma as jest.Mocked<typeof prisma>
const mockGetToken = getToken as jest.MockedFunction<typeof getToken>
const mockRequireAuth = jest.spyOn(authUtils, 'requireAuth')

describe('/api/appointments', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/appointments', () => {
    it('should return appointments for admin', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      mockRequireAuth.mockResolvedValue({
        id: 'admin-id',
        role: UserRole.ADMIN,
        email: '<EMAIL>',
      })

      const mockAppointments = [
        {
          id: 'appointment-1',
          patientId: 'patient-1',
          dentistId: 'dentist-1',
          branchId: 'branch-1',
          serviceId: 'service-1',
          scheduledAt: new Date(),
          duration: 60,
          status: AppointmentStatus.SCHEDULED,
          patient: {
            user: { name: 'Patient One', email: '<EMAIL>' },
          },
          dentist: {
            user: { name: 'Dr. Smith' },
          },
          branch: { name: 'Main Clinic' },
          service: { name: 'Consultation' },
        },
      ]

      mockPrisma.appointment.findMany.mockResolvedValue(mockAppointments as any)
      mockPrisma.appointment.count.mockResolvedValue(1)

      const request = new NextRequest('http://localhost:3000/api/appointments')
      request.headers.set('x-user-id', 'admin-id')
      request.headers.set('x-user-role', UserRole.ADMIN)

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.appointments).toHaveLength(1)
      expect(data.appointments[0].id).toBe('appointment-1')
    })

    it('should filter appointments for patient role', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'patient-user-id',
        role: UserRole.PATIENT,
      } as any)

      // Mock patient profile lookup
      mockPrisma.patientProfile.findUnique.mockResolvedValue({
        id: 'patient-profile-id',
        userId: 'patient-user-id',
      } as any)

      mockPrisma.appointment.findMany.mockResolvedValue([])
      mockPrisma.appointment.count.mockResolvedValue(0)

      const request = new NextRequest('http://localhost:3000/api/appointments')
      request.headers.set('x-user-id', 'patient-user-id')
      request.headers.set('x-user-role', UserRole.PATIENT)

      await GET(request)

      // Verify that the query was filtered by patient ID
      expect(mockPrisma.appointment.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            patientId: 'patient-profile-id',
          }),
        })
      )
    })

    it('should handle date range filtering', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      mockPrisma.appointment.findMany.mockResolvedValue([])
      mockPrisma.appointment.count.mockResolvedValue(0)

      const request = new NextRequest(
        'http://localhost:3000/api/appointments?dateFrom=2024-01-01&dateTo=2024-01-31'
      )
      request.headers.set('x-user-id', 'admin-id')
      request.headers.set('x-user-role', UserRole.ADMIN)

      await GET(request)

      expect(mockPrisma.appointment.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            scheduledAt: {
              gte: new Date('2024-01-01'),
              lte: new Date('2024-01-31'),
            },
          }),
        })
      )
    })
  })

  describe('POST /api/appointments', () => {
    it('should create appointment for valid request', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      // Mock patient exists
      mockPrisma.patientProfile.findUnique.mockResolvedValue({
        id: 'patient-1',
        userId: 'patient-user-1',
        user: { name: 'Patient One' },
      } as any)

      // Mock dentist exists and is available
      mockPrisma.dentistProfile.findUnique.mockResolvedValue({
        id: 'dentist-1',
        isAvailable: true,
        user: { name: 'Dr. Smith' },
      } as any)

      // Mock branch and service exist
      mockPrisma.branch.findUnique.mockResolvedValue({ id: 'branch-1' } as any)
      mockPrisma.service.findUnique.mockResolvedValue({ id: 'service-1' } as any)

      // Mock no conflicting appointments
      mockPrisma.appointment.findFirst.mockResolvedValue(null)

      // Mock appointment creation
      const mockCreatedAppointment = {
        id: 'new-appointment-id',
        patientId: 'patient-1',
        dentistId: 'dentist-1',
        branchId: 'branch-1',
        serviceId: 'service-1',
        scheduledAt: new Date('2024-01-01T10:00:00Z'),
        duration: 60,
        status: AppointmentStatus.SCHEDULED,
      }

      mockPrisma.appointment.create.mockResolvedValue(mockCreatedAppointment as any)

      const requestBody = {
        patientId: 'patient-1',
        dentistId: 'dentist-1',
        branchId: 'branch-1',
        serviceId: 'service-1',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 60,
        type: 'CONSULTATION',
      }

      const request = new NextRequest('http://localhost:3000/api/appointments', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'admin-id',
          'x-user-role': UserRole.ADMIN,
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.id).toBe('new-appointment-id')
      expect(mockPrisma.appointment.create).toHaveBeenCalled()
    })

    it('should return 404 if patient not found', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      // Mock patient doesn't exist
      mockPrisma.patientProfile.findUnique.mockResolvedValue(null)

      const requestBody = {
        patientId: 'non-existent-patient',
        dentistId: 'dentist-1',
        branchId: 'branch-1',
        serviceId: 'service-1',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 60,
      }

      const request = new NextRequest('http://localhost:3000/api/appointments', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'admin-id',
          'x-user-role': UserRole.ADMIN,
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Patient not found')
    })

    it('should return 409 if time slot conflicts', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      // Mock all entities exist
      mockPrisma.patientProfile.findUnique.mockResolvedValue({
        id: 'patient-1',
        userId: 'patient-user-1',
        user: { name: 'Patient One' },
      } as any)

      mockPrisma.dentistProfile.findUnique.mockResolvedValue({
        id: 'dentist-1',
        isAvailable: true,
        user: { name: 'Dr. Smith' },
      } as any)

      mockPrisma.branch.findUnique.mockResolvedValue({ id: 'branch-1' } as any)
      mockPrisma.service.findUnique.mockResolvedValue({ id: 'service-1' } as any)

      // Mock conflicting appointment exists
      mockPrisma.appointment.findFirst.mockResolvedValue({
        id: 'conflicting-appointment',
        scheduledAt: new Date('2024-01-01T10:00:00Z'),
      } as any)

      const requestBody = {
        patientId: 'patient-1',
        dentistId: 'dentist-1',
        branchId: 'branch-1',
        serviceId: 'service-1',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 60,
      }

      const request = new NextRequest('http://localhost:3000/api/appointments', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'admin-id',
          'x-user-role': UserRole.ADMIN,
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(409)
      expect(data.error).toBe('Time slot is not available')
    })

    it('should restrict patient to book only for themselves', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'patient-user-id',
        role: UserRole.PATIENT,
      } as any)

      // Mock patient exists but user ID doesn't match
      mockPrisma.patientProfile.findUnique.mockResolvedValue({
        id: 'patient-1',
        userId: 'different-user-id', // Different from token sub
        user: { name: 'Patient One' },
      } as any)

      const requestBody = {
        patientId: 'patient-1',
        dentistId: 'dentist-1',
        branchId: 'branch-1',
        serviceId: 'service-1',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 60,
      }

      const request = new NextRequest('http://localhost:3000/api/appointments', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'patient-user-id',
          'x-user-role': UserRole.PATIENT,
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toBe('You can only book appointments for yourself')
    })
  })
})
