'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { UserRole } from '@prisma/client'
import {
  Calendar,
  Clock,
  Users,
  FileText,
  Settings,
  Stethoscope,
  Loader2,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Activity,
  Bell,
  ArrowRight,
  Plus,
  Eye,
  Edit,
  BarChart3
} from 'lucide-react'

export default function DentistDashboard() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/dashboard')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="text-center">
          <div className="relative">
            <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
            <div className="absolute inset-0 rounded-full bg-blue-100 animate-ping opacity-20"></div>
          </div>
          <p className="text-gray-600 font-medium">Loading your dashboard...</p>
          <p className="text-gray-500 text-sm mt-1">Preparing your patient data</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  const currentHour = currentTime.getHours()
  const greeting = currentHour < 12 ? 'Good morning' : currentHour < 17 ? 'Good afternoon' : 'Good evening'
  const doctorName = user?.name?.split(' ')[0] || 'Doctor'

  return (
    <div className="p-6 space-y-8">
      {/* Enhanced Header with Time and Status */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-transparent to-green-600/5 rounded-2xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-sm border border-blue-100/50">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <Stethoscope className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                    {greeting}, Dr. {doctorName}!
                  </h1>
                  <p className="text-gray-600 font-medium">
                    {currentTime.toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="font-mono text-blue-700">
                    {currentTime.toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit'
                    })}
                  </span>
                </div>
                <Badge variant="secondary" className="bg-green-100 text-green-700 hover:bg-green-200">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Online
                </Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/settings')}
                className="hover:bg-blue-50 hover:border-blue-200 transition-all duration-200"
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/patients')}
                className="hover:bg-green-50 hover:border-green-200 transition-all duration-200"
              >
                <FileText className="h-4 w-4 mr-2" />
                Patient Records
              </Button>
              <Button
                size="sm"
                onClick={() => router.push('/dentist/appointments')}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Appointment
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards with Hover Effects */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="group hover:shadow-lg hover:shadow-blue-100/50 transition-all duration-300 hover:-translate-y-1 border-blue-100/50 bg-gradient-to-br from-blue-50/50 to-white cursor-pointer"
              onClick={() => router.push('/dentist/appointments')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors">
              Today&apos;s Appointments
            </CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
              <Calendar className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-blue-600">8</div>
              <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs">
                Active
              </Badge>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">3 completed, 5 upcoming</span>
              <ArrowRight className="h-3 w-3 text-gray-400 group-hover:text-blue-500 transition-colors" />
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
              <div className="bg-blue-600 h-1.5 rounded-full transition-all duration-500" style={{width: '37.5%'}}></div>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-green-100/50 transition-all duration-300 hover:-translate-y-1 border-green-100/50 bg-gradient-to-br from-green-50/50 to-white cursor-pointer"
              onClick={() => router.push('/dentist/appointments')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-green-700 transition-colors">
              Next Appointment
            </CardTitle>
            <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
              <Clock className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-green-600">2:30 PM</div>
              <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs">
                Soon
              </Badge>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">John Doe - Cleaning</span>
              <ArrowRight className="h-3 w-3 text-gray-400 group-hover:text-green-500 transition-colors" />
            </div>
            <div className="flex items-center gap-1 text-xs text-green-600 mt-2">
              <AlertCircle className="h-3 w-3" />
              <span>In 45 minutes</span>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-purple-100/50 transition-all duration-300 hover:-translate-y-1 border-purple-100/50 bg-gradient-to-br from-purple-50/50 to-white cursor-pointer"
              onClick={() => router.push('/dentist/patients')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-purple-700 transition-colors">
              Total Patients
            </CardTitle>
            <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
              <Users className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-purple-600">156</div>
              <Badge variant="secondary" className="bg-purple-100 text-purple-700 text-xs">
                +12 this month
              </Badge>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Active patients</span>
              <ArrowRight className="h-3 w-3 text-gray-400 group-hover:text-purple-500 transition-colors" />
            </div>
            <div className="flex items-center gap-1 text-xs text-purple-600 mt-2">
              <TrendingUp className="h-3 w-3" />
              <span>8.3% growth</span>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-orange-100/50 transition-all duration-300 hover:-translate-y-1 border-orange-100/50 bg-gradient-to-br from-orange-50/50 to-white cursor-pointer"
              onClick={() => router.push('/dentist/billing')}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-orange-700 transition-colors">
              Monthly Revenue
            </CardTitle>
            <div className="p-2 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors">
              <BarChart3 className="h-4 w-4 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-orange-600">₱125,400</div>
              <Badge variant="secondary" className="bg-orange-100 text-orange-700 text-xs">
                +12%
              </Badge>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">vs last month</span>
              <ArrowRight className="h-3 w-3 text-gray-400 group-hover:text-orange-500 transition-colors" />
            </div>
            <div className="flex items-center gap-1 text-xs text-orange-600 mt-2">
              <TrendingUp className="h-3 w-3" />
              <span>Above target</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Visualization Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Weekly Performance Chart */}
        <Card className="lg:col-span-2 hover:shadow-lg transition-all duration-300 border-blue-100/50">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-gray-900">Weekly Performance</CardTitle>
              <Button variant="outline" size="sm" className="text-xs">
                <Eye className="h-3 w-3 mr-1" />
                View Details
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Appointments Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Appointments Completed</span>
                  <span className="font-medium text-blue-600">28/35 (80%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-1000" style={{width: '80%'}}></div>
                </div>
              </div>

              {/* Revenue Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Revenue Target</span>
                  <span className="font-medium text-green-600">₱125,400/₱150,000 (84%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-1000" style={{width: '84%'}}></div>
                </div>
              </div>

              {/* Patient Satisfaction */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Patient Satisfaction</span>
                  <span className="font-medium text-purple-600">4.8/5.0 (96%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full transition-all duration-1000" style={{width: '96%'}}></div>
                </div>
              </div>

              {/* Simple Bar Chart Visualization */}
              <div className="mt-6 pt-4 border-t border-gray-100">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Daily Appointments This Week</h4>
                <div className="flex items-end justify-between h-24 gap-2">
                  {[
                    { day: 'Mon', count: 6, height: '60%' },
                    { day: 'Tue', count: 8, height: '80%' },
                    { day: 'Wed', count: 5, height: '50%' },
                    { day: 'Thu', count: 9, height: '90%' },
                    { day: 'Fri', count: 7, height: '70%' },
                    { day: 'Sat', count: 4, height: '40%' },
                    { day: 'Sun', count: 2, height: '20%' }
                  ].map((item) => (
                    <div key={item.day} className="flex flex-col items-center flex-1">
                      <div
                        className="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-500 hover:from-blue-600 hover:to-blue-500 cursor-pointer"
                        style={{ height: item.height }}
                        title={`${item.day}: ${item.count} appointments`}
                      ></div>
                      <span className="text-xs text-gray-500 mt-1">{item.day}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats and Alerts */}
        <Card className="hover:shadow-lg transition-all duration-300 border-orange-100/50">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Bell className="h-5 w-5 text-orange-600" />
              Alerts & Updates
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Urgent Alert */}
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-red-800">Urgent: Patient Follow-up</p>
                  <p className="text-xs text-red-600">Maria Santos requires post-surgery check</p>
                  <Button size="sm" variant="outline" className="text-xs h-6 mt-2 border-red-200 text-red-700 hover:bg-red-100">
                    View Details
                  </Button>
                </div>
              </div>
            </div>

            {/* Info Alert */}
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-2">
                <Activity className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-blue-800">Equipment Maintenance</p>
                  <p className="text-xs text-blue-600">X-ray machine scheduled for service</p>
                  <Button size="sm" variant="outline" className="text-xs h-6 mt-2 border-blue-200 text-blue-700 hover:bg-blue-100">
                    Schedule
                  </Button>
                </div>
              </div>
            </div>

            {/* Success Alert */}
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-green-800">Monthly Target Achieved</p>
                  <p className="text-xs text-green-600">Revenue goal reached 5 days early</p>
                  <Button size="sm" variant="outline" className="text-xs h-6 mt-2 border-green-200 text-green-700 hover:bg-green-100">
                    View Report
                  </Button>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="pt-4 border-t border-gray-100 space-y-3">
              <h4 className="text-sm font-medium text-gray-700">Quick Stats</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-2 bg-gray-50 rounded-lg">
                  <div className="text-lg font-bold text-gray-900">94%</div>
                  <div className="text-xs text-gray-600">On-time Rate</div>
                </div>
                <div className="text-center p-2 bg-gray-50 rounded-lg">
                  <div className="text-lg font-bold text-gray-900">2.3h</div>
                  <div className="text-xs text-gray-600">Avg. Treatment</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Quick Actions with Better Information Architecture */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="group hover:shadow-lg hover:shadow-blue-100/50 transition-all duration-300 border-blue-100/50">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Today&apos;s Schedule
              </CardTitle>
              <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                8 appointments
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-800">Next: 2:30 PM</span>
                <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                  In 45 min
                </Badge>
              </div>
              <p className="text-xs text-blue-600">John Doe - Routine Cleaning</p>
            </div>
            <Button
              className="w-full justify-between group-hover:bg-blue-600 transition-colors"
              onClick={() => router.push('/dentist/appointments')}
            >
              <span className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                View All Appointments
              </span>
              <ArrowRight className="h-4 w-4" />
            </Button>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/schedule')}
                className="hover:bg-blue-50 hover:border-blue-200"
              >
                <Edit className="h-3 w-3 mr-1" />
                Edit Schedule
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/notes')}
                className="hover:bg-blue-50 hover:border-blue-200"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Notes
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-green-100/50 transition-all duration-300 border-green-100/50">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Users className="h-5 w-5 text-green-600" />
                Patient Management
              </CardTitle>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                156 active
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-3 bg-green-50 rounded-lg border border-green-100">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-green-800">Recent Activity</span>
                <Badge variant="outline" className="text-xs border-green-200 text-green-700">
                  3 updates
                </Badge>
              </div>
              <p className="text-xs text-green-600">2 new patients, 1 treatment completed</p>
            </div>
            <Button
              className="w-full justify-between bg-green-600 hover:bg-green-700 group-hover:bg-green-700 transition-colors"
              onClick={() => router.push('/dentist/patients')}
            >
              <span className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Patient Records
              </span>
              <ArrowRight className="h-4 w-4" />
            </Button>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/treatments')}
                className="hover:bg-green-50 hover:border-green-200"
              >
                <Stethoscope className="h-3 w-3 mr-1" />
                Treatments
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/patients')}
                className="hover:bg-green-50 hover:border-green-200"
              >
                <Bell className="h-3 w-3 mr-1" />
                Messages
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-purple-100/50 transition-all duration-300 border-purple-100/50">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Activity className="h-5 w-5 text-purple-600" />
                Clinical Tools
              </CardTitle>
              <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                All systems
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-3 bg-purple-50 rounded-lg border border-purple-100">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-purple-800">Quick Access</span>
                <Badge variant="outline" className="text-xs border-purple-200 text-purple-700">
                  Ready
                </Badge>
              </div>
              <p className="text-xs text-purple-600">All clinical tools are available</p>
            </div>
            <Button
              className="w-full justify-between bg-purple-600 hover:bg-purple-700 group-hover:bg-purple-700 transition-colors"
              onClick={() => router.push('/dentist/charts')}
            >
              <span className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Dental Charts
              </span>
              <ArrowRight className="h-4 w-4" />
            </Button>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/notes')}
                className="hover:bg-purple-50 hover:border-purple-200"
              >
                <FileText className="h-3 w-3 mr-1" />
                Notes
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dentist/prescriptions')}
                className="hover:bg-purple-50 hover:border-purple-200"
              >
                <Activity className="h-3 w-3 mr-1" />
                Prescriptions
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
