# Dental Clinic Management System API Documentation

## Overview

This API provides comprehensive endpoints for managing a dental clinic system including patient management, appointments, treatments, billing, inventory, and more.

## Base URL
```
http://localhost:3000/api
```

## Authentication

The API uses NextAuth.js with JWT tokens for authentication. Most endpoints require authentication.

### Authentication Methods
1. **Google OAuth** (for patients)
2. **Credentials** (email/password for staff, dentists, admins)

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## User Roles

- **PATIENT**: Can view own data, book appointments, make payments
- **DENTIST**: Can manage own appointments, treatments, view patient data
- **STAFF**: Can manage appointments, patients, inventory, billing
- **ADMIN**: Full system access including user management and analytics

## Rate Limiting

- Authentication endpoints: 10 requests per minute
- Payment endpoints: 20 requests per minute
- General endpoints: 200 requests per minute

## Error Responses

All errors follow this format:
```json
{
  "error": "Error message",
  "details": [
    {
      "field": "fieldName",
      "message": "Specific error message",
      "code": "error_code"
    }
  ]
}
```

## Endpoints

### Authentication

#### POST /api/auth/signin
Sign in with credentials or OAuth provider.

#### POST /api/auth/signout
Sign out current user.

#### GET /api/auth/session
Get current user session.

### Users

#### GET /api/users
List users with pagination and search.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 100)
- `query` (string): Search query
- `role` (string): Filter by user role
- `status` (string): Filter by user status

**Response:**
```json
{
  "users": [
    {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "PATIENT",
      "status": "ACTIVE",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

#### POST /api/users
Create a new user (Admin only).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "name": "User Name",
  "phone": "+**********",
  "role": "PATIENT"
}
```

#### GET /api/users/[id]
Get user by ID.

#### PUT /api/users/[id]
Update user information.

#### DELETE /api/users/[id]
Delete user (Admin only).

### Patients

#### GET /api/patients
List patients with pagination and search.

#### POST /api/patients
Create patient profile.

#### GET /api/patients/[id]
Get patient profile with medical history.

#### PUT /api/patients/[id]
Update patient profile.

#### DELETE /api/patients/[id]
Delete patient (Admin only).

### Appointments

#### GET /api/appointments
List appointments with filtering.

**Query Parameters:**
- `page`, `limit`: Pagination
- `patientId`: Filter by patient
- `dentistId`: Filter by dentist
- `branchId`: Filter by branch
- `status`: Filter by appointment status
- `dateFrom`, `dateTo`: Date range filter

#### POST /api/appointments
Create new appointment.

**Request Body:**
```json
{
  "patientId": "patient_id",
  "dentistId": "dentist_id",
  "branchId": "branch_id",
  "serviceId": "service_id",
  "scheduledAt": "2024-01-01T10:00:00Z",
  "duration": 60,
  "type": "CONSULTATION",
  "notes": "Patient notes",
  "symptoms": "Reported symptoms"
}
```

#### GET /api/appointments/[id]
Get appointment details.

#### PUT /api/appointments/[id]
Update appointment.

#### DELETE /api/appointments/[id]
Cancel appointment.

#### GET /api/appointments/availability
Check dentist availability.

**Query Parameters:**
- `dentistId` (required): Dentist ID
- `date` (required): Date in YYYY-MM-DD format
- `duration`: Appointment duration in minutes (default: 60)

### Treatments

#### GET /api/treatments
List treatments with filtering.

#### POST /api/treatments
Create treatment record.

**Request Body:**
```json
{
  "appointmentId": "appointment_id",
  "patientId": "patient_id",
  "dentistId": "dentist_id",
  "serviceId": "service_id",
  "diagnosis": "Treatment diagnosis",
  "procedure": "Procedure performed",
  "notes": "SOAP notes",
  "cost": 150.00
}
```

#### GET /api/treatments/[id]
Get treatment details.

#### PUT /api/treatments/[id]
Update treatment.

#### DELETE /api/treatments/[id]
Delete treatment (Admin only).

### Dental Charts

#### GET /api/dental-charts
Get dental chart by patient ID.

**Query Parameters:**
- `patientId` (required): Patient ID

#### POST /api/dental-charts
Create dental chart.

#### PUT /api/dental-charts/[id]/teeth
Update tooth records.

**Request Body:**
```json
{
  "toothNumber": 1,
  "condition": "HEALTHY",
  "notes": "Tooth notes"
}
```

### Billing & Payments

#### GET /api/invoices
List invoices with filtering.

#### POST /api/invoices
Create invoice.

**Request Body:**
```json
{
  "patientId": "patient_id",
  "items": [
    {
      "treatmentId": "treatment_id",
      "description": "Service description",
      "quantity": 1,
      "unitPrice": 100.00
    }
  ],
  "dueDate": "2024-01-31T00:00:00Z",
  "notes": "Invoice notes"
}
```

#### GET /api/invoices/[id]
Get invoice details.

#### PUT /api/invoices/[id]
Update invoice.

#### DELETE /api/invoices/[id]
Delete invoice (Admin only).

#### GET /api/payments
List payments.

#### POST /api/payments
Create payment.

**Request Body:**
```json
{
  "invoiceId": "invoice_id",
  "amount": 100.00,
  "method": "CASH",
  "transactionId": "txn_123",
  "notes": "Payment notes"
}
```

#### POST /api/payments/process
Process payment with external gateway.

### Inventory

#### GET /api/inventory
List inventory items.

**Query Parameters:**
- `branchId`: Filter by branch
- `category`: Filter by category
- `lowStock`: Show only low stock items (true/false)
- `expired`: Show only expired items (true/false)

#### POST /api/inventory
Create inventory item.

#### GET /api/inventory/[id]
Get inventory item details.

#### PUT /api/inventory/[id]
Update inventory item.

#### DELETE /api/inventory/[id]
Delete inventory item.

#### GET /api/inventory/stock-movements
List stock movements.

#### POST /api/inventory/stock-movements
Create stock movement.

**Request Body:**
```json
{
  "itemId": "item_id",
  "type": "IN",
  "quantity": 10,
  "reason": "Purchase order #123",
  "reference": "PO-123",
  "notes": "Stock notes"
}
```

### Notifications

#### GET /api/notifications
List notifications.

#### POST /api/notifications
Create notification.

#### GET /api/notifications/[id]
Get notification details.

#### PUT /api/notifications/[id]
Mark notification as read/unread.

#### DELETE /api/notifications/[id]
Delete notification.

#### POST /api/notifications/bulk
Perform bulk actions on notifications.

#### GET /api/notifications/bulk/unread-count
Get unread notification count.

### Files

#### GET /api/files/patient
List patient files.

#### POST /api/files/patient
Upload patient file.

#### GET /api/files/patient/[id]
Get patient file details.

#### PUT /api/files/patient/[id]
Update file metadata.

#### DELETE /api/files/patient/[id]
Delete patient file.

### Services

#### GET /api/services
List dental services.

#### POST /api/services
Create service (Admin/Staff only).

#### GET /api/services/[id]
Get service details.

#### PUT /api/services/[id]
Update service.

#### DELETE /api/services/[id]
Delete service (Admin only).

### Analytics

#### GET /api/analytics/dashboard
Get dashboard analytics.

**Query Parameters:**
- `period`: Number of days (default: 30)
- `branchId`: Filter by branch

#### GET /api/analytics/reports
Generate detailed reports.

**Query Parameters:**
- `type`: Report type (revenue, appointments, patients, treatments, dentist-performance)
- `startDate`: Start date (ISO format)
- `endDate`: End date (ISO format)
- `branchId`: Filter by branch
- `dentistId`: Filter by dentist
- `groupBy`: Group by (day, week, month)

## Pagination

All list endpoints support pagination:
- `page`: Page number (starts from 1)
- `limit`: Items per page (max 100)

Response includes pagination metadata:
```json
{
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

## Search and Filtering

Most list endpoints support:
- `query`: Text search across relevant fields
- `sortBy`: Field to sort by
- `sortOrder`: 'asc' or 'desc'

## Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request (validation error)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict
- `429`: Rate Limited
- `500`: Internal Server Error

## Security

- All endpoints use HTTPS in production
- Input validation and sanitization
- SQL injection protection
- XSS protection
- Rate limiting
- Audit logging
- Role-based access control
