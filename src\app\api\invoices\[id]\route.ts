import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { UserRole, PaymentStatus } from '@prisma/client'
import { z } from 'zod'

const invoiceUpdateSchema = z.object({
  dueDate: z.string().transform((str) => new Date(str)).optional(),
  notes: z.string().optional(),
  status: z.nativeEnum(PaymentStatus).optional(),
})

// GET /api/invoices/[id] - Get invoice by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const invoice = await prisma.invoice.findUnique({
    where: { id },
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
      },
      items: {
        include: {
          treatment: {
            include: {
              service: { 
                select: { 
                  id: true,
                  name: true, 
                  category: true,
                  description: true,
                } 
              },
              dentist: { 
                include: { 
                  user: { select: { name: true } } 
                } 
              },
              appointment: {
                select: {
                  id: true,
                  scheduledAt: true,
                },
              },
            },
          },
        },
      },
      payments: {
        orderBy: { createdAt: 'desc' },
      },
    },
  })
  
  if (!invoice) {
    return NextResponse.json(
      { error: 'Invoice not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, invoice.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Calculate payment summary
  const totalPaid = invoice.payments.reduce((sum, payment) => {
    return payment.status === PaymentStatus.PAID ? sum + Number(payment.amount) : sum
  }, 0)
  
  const remainingBalance = Number(invoice.totalAmount) - totalPaid
  
  return NextResponse.json({
    ...invoice,
    paymentSummary: {
      totalAmount: Number(invoice.totalAmount),
      totalPaid,
      remainingBalance,
      isFullyPaid: remainingBalance <= 0,
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/invoices/[id] - Update invoice
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  const existingInvoice = await prisma.invoice.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
    },
  })
  
  if (!existingInvoice) {
    return NextResponse.json(
      { error: 'Invoice not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canUpdate = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF
  
  if (!canUpdate) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const validatedData = invoiceUpdateSchema.parse(body)
  
  const updatedInvoice = await prisma.invoice.update({
    where: { id },
    data: validatedData,
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
      },
      items: {
        include: {
          treatment: {
            include: {
              service: { select: { name: true, category: true } },
              dentist: { 
                include: { 
                  user: { select: { name: true } } 
                } 
              },
            },
          },
        },
      },
      payments: {
        orderBy: { createdAt: 'desc' },
      },
    },
  })
  
  return NextResponse.json(updatedInvoice)
}, [UserRole.ADMIN, UserRole.STAFF])

// DELETE /api/invoices/[id] - Delete invoice (Admin only)
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const invoice = await prisma.invoice.findUnique({
    where: { id },
    include: {
      payments: true,
    },
  })
  
  if (!invoice) {
    return NextResponse.json(
      { error: 'Invoice not found' },
      { status: 404 }
    )
  }
  
  // Check if invoice has payments
  const paidPayments = invoice.payments.filter(p => p.status === PaymentStatus.PAID)
  if (paidPayments.length > 0) {
    return NextResponse.json(
      { error: 'Cannot delete invoice with paid payments' },
      { status: 400 }
    )
  }
  
  await prisma.invoice.delete({
    where: { id },
  })
  
  return NextResponse.json({ message: 'Invoice deleted successfully' })
}, [UserRole.ADMIN])
