'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Calendar } from '@/components/ui/calendar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Check,
  X,
  Calendar as CalendarIcon,
  Loader2,
  User,
  Phone,
  MapPin,
  FileText
} from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'
import { AppointmentStatus } from '@prisma/client'
import { formatAppointmentDate, formatAppointmentTime, formatPrice } from '@/lib/appointment-utils'

interface AppointmentStatusDialogProps {
  appointment: AppointmentDetails | null
  isOpen: boolean
  onClose: () => void
  onStatusUpdate: (appointmentId: string, status: AppointmentStatus, reason?: string, newDate?: Date) => Promise<void>
}

const getStatusDisplay = (status: AppointmentStatus) => {
  switch (status) {
    case 'SCHEDULED':
      return {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: <Clock className="h-4 w-4" />,
        label: 'Scheduled'
      }
    case 'CONFIRMED':
      return {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: <CheckCircle className="h-4 w-4" />,
        label: 'Confirmed'
      }
    case 'IN_PROGRESS':
      return {
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: <AlertCircle className="h-4 w-4" />,
        label: 'In Progress'
      }
    case 'COMPLETED':
      return {
        color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
        icon: <Check className="h-4 w-4" />,
        label: 'Completed'
      }
    case 'CANCELLED':
      return {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: <XCircle className="h-4 w-4" />,
        label: 'Cancelled'
      }
    case 'NO_SHOW':
      return {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: <X className="h-4 w-4" />,
        label: 'No Show'
      }
    default:
      return {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: <AlertCircle className="h-4 w-4" />,
        label: status
      }
  }
}

const getAvailableStatusTransitions = (currentStatus: AppointmentStatus, isUpcoming: boolean) => {
  const transitions: { status: AppointmentStatus; label: string; description: string; color: string; requiresReason?: boolean }[] = []

  if (!isUpcoming) {
    // Past appointments can only be marked as completed, cancelled, or no-show
    if (currentStatus !== 'COMPLETED') {
      transitions.push({
        status: 'COMPLETED',
        label: 'Mark as Completed',
        description: 'Mark this past appointment as completed',
        color: 'text-emerald-600'
      })
    }
    if (currentStatus !== 'CANCELLED') {
      transitions.push({
        status: 'CANCELLED',
        label: 'Mark as Cancelled',
        description: 'Mark this past appointment as cancelled',
        color: 'text-red-600',
        requiresReason: true
      })
    }
    if (currentStatus !== 'NO_SHOW') {
      transitions.push({
        status: 'NO_SHOW',
        label: 'Mark as No Show',
        description: 'Patient did not show up for the appointment',
        color: 'text-gray-600',
        requiresReason: true
      })
    }
    return transitions
  }

  // Upcoming appointments
  switch (currentStatus) {
    case 'SCHEDULED':
      transitions.push(
        {
          status: 'CONFIRMED',
          label: 'Confirm Appointment',
          description: 'Confirm the appointment with the patient',
          color: 'text-green-600'
        },
        {
          status: 'CANCELLED',
          label: 'Cancel Appointment',
          description: 'Cancel the appointment',
          color: 'text-red-600',
          requiresReason: true
        }
      )
      break
    case 'CONFIRMED':
      transitions.push(
        {
          status: 'IN_PROGRESS',
          label: 'Start Treatment',
          description: 'Begin the appointment/treatment',
          color: 'text-yellow-600'
        },
        {
          status: 'CANCELLED',
          label: 'Cancel Appointment',
          description: 'Cancel the appointment',
          color: 'text-red-600',
          requiresReason: true
        },
        {
          status: 'NO_SHOW',
          label: 'Mark as No Show',
          description: 'Patient did not show up',
          color: 'text-gray-600'
        }
      )
      break
    case 'IN_PROGRESS':
      transitions.push({
        status: 'COMPLETED',
        label: 'Complete Treatment',
        description: 'Mark the treatment as completed',
        color: 'text-emerald-600'
      })
      break
  }

  return transitions
}

export function AppointmentStatusDialog({
  appointment,
  isOpen,
  onClose,
  onStatusUpdate
}: AppointmentStatusDialogProps) {
  const [selectedStatus, setSelectedStatus] = useState<AppointmentStatus | null>(null)
  const [reason, setReason] = useState('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showReschedule, setShowReschedule] = useState(false)
  const [newDate, setNewDate] = useState<Date | undefined>(undefined)
  const [newTime, setNewTime] = useState<string>('')

  if (!appointment) return null

  const currentStatusDisplay = getStatusDisplay(appointment.status)
  const isUpcoming = new Date(appointment.scheduledAt) > new Date()
  const availableTransitions = getAvailableStatusTransitions(appointment.status, isUpcoming)
  const selectedTransition = availableTransitions.find(t => t.status === selectedStatus)

  const handleStatusUpdate = async () => {
    if (!selectedStatus) return

    setIsUpdating(true)
    setError(null)

    try {
      let finalDate = undefined
      if (showReschedule && newDate && newTime) {
        const [hours, minutes] = newTime.split(':').map(Number)
        finalDate = new Date(newDate)
        finalDate.setHours(hours, minutes, 0, 0)
      }

      await onStatusUpdate(appointment.id, selectedStatus, reason || undefined, finalDate)
      
      // Reset form
      setSelectedStatus(null)
      setReason('')
      setShowReschedule(false)
      setNewDate(undefined)
      setNewTime('')
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update appointment status')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleClose = () => {
    setSelectedStatus(null)
    setReason('')
    setError(null)
    setShowReschedule(false)
    setNewDate(undefined)
    setNewTime('')
    onClose()
  }

  const canReschedule = selectedStatus === 'SCHEDULED' || selectedStatus === 'CONFIRMED'

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Manage Appointment Status
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Appointment Details */}
          <div className="bg-blue-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">{appointment.patient.user.name}</h3>
              <Badge className={`${currentStatusDisplay.color} flex items-center gap-1`}>
                {currentStatusDisplay.icon}
                {currentStatusDisplay.label}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4 text-blue-600" />
                <span>{formatAppointmentDate(appointment.scheduledAt)} at {formatAppointmentTime(appointment.scheduledAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-600" />
                <span>{appointment.service.name}</span>
              </div>
              {appointment.patient.user.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-blue-600" />
                  <span>{appointment.patient.user.phone}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-blue-600" />
                <span>{appointment.branch.name}</span>
              </div>
            </div>
            
            <div className="text-sm">
              <span className="font-medium">Price: </span>
              <span className="text-green-600 font-semibold">{formatPrice(appointment.service.price)}</span>
            </div>
          </div>

          {/* Status Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Select New Status</Label>
            <div className="grid gap-3">
              {availableTransitions.map((transition) => (
                <div
                  key={transition.status}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedStatus === transition.status
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedStatus(transition.status)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className={`font-medium ${transition.color}`}>
                        {transition.label}
                      </div>
                      <div className="text-sm text-gray-600">
                        {transition.description}
                      </div>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedStatus === transition.status
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}>
                      {selectedStatus === transition.status && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Reschedule Option */}
          {canReschedule && selectedStatus && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="reschedule"
                  checked={showReschedule}
                  onChange={(e) => setShowReschedule(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="reschedule">Also reschedule to a new date/time</Label>
              </div>
              
              {showReschedule && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label>New Date</Label>
                    <Calendar
                      mode="single"
                      selected={newDate}
                      onSelect={setNewDate}
                      disabled={(date) => date < new Date()}
                      className="rounded-md border"
                    />
                  </div>
                  <div>
                    <Label>New Time</Label>
                    <Select value={newTime} onValueChange={setNewTime}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select time" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 18 }, (_, i) => {
                          const hour = Math.floor(i / 2) + 8
                          const minute = i % 2 === 0 ? '00' : '30'
                          const time = `${hour.toString().padStart(2, '0')}:${minute}`
                          return (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Reason Input */}
          {selectedTransition?.requiresReason && (
            <div className="space-y-2">
              <Label htmlFor="reason">
                Reason {selectedTransition.requiresReason ? '*' : '(Optional)'}
              </Label>
              <Textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder={`Please provide a reason for ${selectedTransition.label.toLowerCase()}...`}
                rows={3}
                className="resize-none"
              />
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isUpdating}>
            Cancel
          </Button>
          <Button
            onClick={handleStatusUpdate}
            disabled={!selectedStatus || isUpdating || (selectedTransition?.requiresReason && !reason.trim()) || (showReschedule && (!newDate || !newTime))}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isUpdating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Update Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
