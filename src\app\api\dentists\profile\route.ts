import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/dentists/profile?userId=xxx - Get dentist profile by user ID
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  const userId = searchParams.get('userId')
  
  if (!userId) {
    return NextResponse.json(
      { error: 'userId parameter is required' },
      { status: 400 }
    )
  }
  
  // Dentists can only access their own profile unless user is admin/staff
  if (user.role === UserRole.DENTIST && userId !== user.id) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const dentistProfile = await prisma.dentistProfile.findUnique({
    where: { userId },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          image: true,
          status: true,
          createdAt: true,
        },
      },
      branches: {
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
              phone: true,
              email: true,
            },
          },
        },
      },
      schedules: {
        where: { isActive: true },
        orderBy: { dayOfWeek: 'asc' },
      },
      appointments: {
        where: {
          scheduledAt: {
            gte: new Date(),
          },
        },
        orderBy: { scheduledAt: 'asc' },
        take: 10,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                  phone: true,
                },
              },
            },
          },
          service: {
            select: {
              name: true,
              category: true,
              duration: true,
            },
          },
          branch: {
            select: {
              name: true,
            },
          },
        },
      },
      treatments: {
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          service: {
            select: {
              name: true,
              category: true,
            },
          },
        },
      },
      _count: {
        select: {
          appointments: true,
          treatments: true,
        },
      },
    },
  })
  
  if (!dentistProfile) {
    return NextResponse.json(
      { error: 'Dentist profile not found' },
      { status: 404 }
    )
  }
  
  return NextResponse.json(dentistProfile)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
