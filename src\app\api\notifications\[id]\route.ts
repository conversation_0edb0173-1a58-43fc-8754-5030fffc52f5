import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/notifications/[id] - Get notification by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const notification = await prisma.notification.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      appointment: {
        select: {
          id: true,
          scheduledAt: true,
          service: { select: { name: true } },
          patient: {
            include: {
              user: { select: { name: true } },
            },
          },
          dentist: {
            include: {
              user: { select: { name: true } },
            },
          },
        },
      },
    },
  })
  
  if (!notification) {
    return NextResponse.json(
      { error: 'Notification not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canAccess = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    notification.userId === user.id
  
  if (!canAccess) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  return NextResponse.json(notification)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/notifications/[id] - Mark notification as read/unread
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  const notification = await prisma.notification.findUnique({
    where: { id },
  })
  
  if (!notification) {
    return NextResponse.json(
      { error: 'Notification not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canUpdate = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    notification.userId === user.id
  
  if (!canUpdate) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const updatedNotification = await prisma.notification.update({
    where: { id },
    data: {
      isRead: body.isRead ?? true,
    },
  })
  
  return NextResponse.json(updatedNotification)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// DELETE /api/notifications/[id] - Delete notification
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const notification = await prisma.notification.findUnique({
    where: { id },
  })
  
  if (!notification) {
    return NextResponse.json(
      { error: 'Notification not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canDelete = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    notification.userId === user.id
  
  if (!canDelete) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  await prisma.notification.delete({
    where: { id },
  })
  
  return NextResponse.json({ message: 'Notification deleted successfully' })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
