import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { treatmentUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/treatments/[id] - Get treatment by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const treatment = await prisma.treatment.findUnique({
    where: { id },
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          dentalChart: {
            include: {
              teeth: {
                orderBy: { toothNumber: 'asc' },
              },
            },
          },
        },
      },
      dentist: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      service: {
        select: {
          id: true,
          name: true,
          category: true,
          description: true,
          duration: true,
          price: true,
        },
      },
      appointment: {
        select: {
          id: true,
          scheduledAt: true,
          status: true,
          notes: true,
          symptoms: true,
        },
      },
      prescriptions: {
        orderBy: { createdAt: 'desc' },
      },
      files: {
        orderBy: { createdAt: 'desc' },
      },
      invoiceItems: {
        include: {
          invoice: {
            select: {
              id: true,
              invoiceNumber: true,
              status: true,
              totalAmount: true,
            },
          },
        },
      },
    },
  })
  
  if (!treatment) {
    return NextResponse.json(
      { error: 'Treatment not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, treatment.patientId, user.id)) {
    // Also allow dentist who performed the treatment
    if (user.role !== UserRole.DENTIST || treatment.dentist.userId !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }
  }
  
  return NextResponse.json(treatment)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/treatments/[id] - Update treatment
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  const existingTreatment = await prisma.treatment.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
      dentist: { include: { user: true } },
    },
  })
  
  if (!existingTreatment) {
    return NextResponse.json(
      { error: 'Treatment not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canUpdate = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    (user.role === UserRole.DENTIST && existingTreatment.dentist.userId === user.id)
  
  if (!canUpdate) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const validatedData = treatmentUpdateSchema.parse(body)
  
  // Handle status changes
  const updateData: Record<string, unknown> = { ...validatedData }
  if (validatedData.status === 'COMPLETED' && !existingTreatment.completedAt) {
    updateData.completedAt = new Date()
  }
  
  const updatedTreatment = await prisma.treatment.update({
    where: { id },
    data: updateData,
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
      dentist: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      service: {
        select: {
          id: true,
          name: true,
          category: true,
          price: true,
        },
      },
      prescriptions: true,
      files: true,
    },
  })
  
  return NextResponse.json(updatedTreatment)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])

// DELETE /api/treatments/[id] - Delete treatment (Admin only)
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const treatment = await prisma.treatment.findUnique({
    where: { id },
    include: {
      invoiceItems: true,
    },
  })
  
  if (!treatment) {
    return NextResponse.json(
      { error: 'Treatment not found' },
      { status: 404 }
    )
  }
  
  // Check if treatment has associated invoices
  if (treatment.invoiceItems.length > 0) {
    return NextResponse.json(
      { error: 'Cannot delete treatment with associated invoices' },
      { status: 400 }
    )
  }
  
  await prisma.treatment.delete({
    where: { id },
  })
  
  return NextResponse.json({ message: 'Treatment deleted successfully' })
}, [UserRole.ADMIN])
