'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { PatientCalendarIntegration } from '@/components/appointments/patient-calendar-integration'
import { RescheduleAppointmentModal } from '@/components/appointments/modals/reschedule-appointment-modal'
import { CancelAppointmentModal } from '@/components/appointments/modals/cancel-appointment-modal'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Loader2, AlertCircle, Calendar, Home, Edit, X } from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'
import { canCancelAppointment, canRescheduleAppointment } from '@/lib/appointment-utils'

interface AppointmentPageProps {
  params: Promise<{ id: string }>
}

export default function AppointmentPage({ params }: AppointmentPageProps) {
  const { isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [appointment, setAppointment] = useState<AppointmentDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [appointmentId, setAppointmentId] = useState<string | null>(null)
  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false)
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)

  // Extract appointment ID from params
  useEffect(() => {
    const extractId = async () => {
      const resolvedParams = await params
      setAppointmentId(resolvedParams.id)
    }
    extractId()
  }, [params])

  // Fetch appointment details
  useEffect(() => {
    if (appointmentId && isAuthenticated) {
      fetchAppointment()
    }
  }, [appointmentId, isAuthenticated]) // eslint-disable-line react-hooks/exhaustive-deps

  const handleGoBack = () => {
    router.back()
  }

  const handleGoHome = () => {
    router.push('/dashboard')
  }

  const handleBookAnother = () => {
    router.push('/appointments/book')
  }

  const handleReschedule = () => {
    setIsRescheduleModalOpen(true)
  }

  const handleCancel = () => {
    setIsCancelModalOpen(true)
  }

  const handleModalSuccess = () => {
    // Refresh appointment data after successful cancel/reschedule
    if (appointmentId && isAuthenticated) {
      fetchAppointment()
    }
  }

  const fetchAppointment = async () => {
    if (!appointmentId) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/appointments/${appointmentId}`)

      if (!response.ok) {
        if (response.status === 404) {
          setError('Appointment not found')
        } else if (response.status === 403) {
          setError('You do not have permission to view this appointment')
        } else {
          setError('Failed to load appointment details')
        }
        return
      }

      const appointmentData = await response.json()
      setAppointment(appointmentData)
    } catch (err) {
      setError('Failed to load appointment details')
      console.error('Error fetching appointment:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // Loading state
  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600">Loading appointment details...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Authentication required
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card className="border-red-200 bg-red-50">
              <CardHeader className="text-center">
                <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
                <CardTitle className="text-xl text-red-900">Authentication Required</CardTitle>
                <CardDescription className="text-red-700">
                  Please sign in to view your appointment details.
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button onClick={() => router.push('/auth/signin')} className="bg-blue-600 hover:bg-blue-700">
                  Sign In
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <div className="mb-6">
              <Button
                variant="ghost"
                onClick={handleGoBack}
                className="mb-4 hover:bg-white/50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </div>

            <Card className="border-red-200 bg-red-50">
              <CardHeader className="text-center">
                <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
                <CardTitle className="text-xl text-red-900">Error</CardTitle>
                <CardDescription className="text-red-700">
                  {error}
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-3">
                <Button onClick={handleGoBack} variant="outline">
                  Go Back
                </Button>
                <Button onClick={handleGoHome} className="bg-blue-600 hover:bg-blue-700">
                  Go to Dashboard
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Success state with appointment details
  if (appointment) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                onClick={handleGoBack}
                className="hover:bg-white/50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="text-center">
                <h1 className="text-2xl font-bold text-gray-900">Appointment Details</h1>
                <p className="text-gray-600">View and manage your appointment</p>
              </div>
              <div className="w-20"></div> {/* Spacer for centering */}
            </div>

            {/* Patient Calendar Integration Component */}
            <PatientCalendarIntegration 
              appointment={appointment}
              showAppointmentDetails={true}
            />

            {/* Action buttons */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  {/* Cancel and Reschedule buttons - only show if appointment can be modified */}
                  {(canCancelAppointment(appointment) || canRescheduleAppointment(appointment)) && (
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      {canRescheduleAppointment(appointment) && (
                        <Button
                          onClick={handleReschedule}
                          variant="outline"
                          className="flex items-center border-blue-300 text-blue-700 hover:bg-blue-100"
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Reschedule Appointment
                        </Button>
                      )}
                      {canCancelAppointment(appointment) && (
                        <Button
                          onClick={handleCancel}
                          variant="outline"
                          className="flex items-center border-red-300 text-red-700 hover:bg-red-100"
                        >
                          <X className="h-4 w-4 mr-2" />
                          Cancel Appointment
                        </Button>
                      )}
                    </div>
                  )}

                  {/* Other action buttons */}
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      onClick={handleBookAnother}
                      variant="outline"
                      className="flex items-center"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      Book Another Appointment
                    </Button>
                    <Button
                      onClick={handleGoHome}
                      className="bg-blue-600 hover:bg-blue-700 flex items-center"
                    >
                      <Home className="h-4 w-4 mr-2" />
                      Go to Dashboard
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Modals */}
            <RescheduleAppointmentModal
              isOpen={isRescheduleModalOpen}
              onClose={() => setIsRescheduleModalOpen(false)}
              appointment={appointment}
              onSuccess={handleModalSuccess}
            />

            <CancelAppointmentModal
              isOpen={isCancelModalOpen}
              onClose={() => setIsCancelModalOpen(false)}
              appointment={appointment}
              onSuccess={handleModalSuccess}
            />
          </div>
        </div>
      </div>
    )
  }

  return null
}
