import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { dentistProfileCreateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'
import { paginationSchema, searchSchema } from '@/lib/validation-middleware'

// GET /api/dentists - List dentist profiles with pagination and filtering
export const GET = withAuth(async (req: NextRequest) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'user.name',
    sortOrder: searchParams.get('sortOrder') || 'asc',
  })
  
  const branchId = searchParams.get('branchId')
  const specialization = searchParams.get('specialization')
  const isAvailable = searchParams.get('isAvailable')
  
  // Build where clause
  const where: any = {}
  
  if (query) {
    where.OR = [
      { user: { name: { contains: query, mode: 'insensitive' } } },
      { specialization: { contains: query, mode: 'insensitive' } },
      { licenseNumber: { contains: query, mode: 'insensitive' } },
    ]
  }
  
  if (branchId) {
    where.branches = {
      some: {
        branchId: branchId,
      },
    }
  }
  
  if (specialization) {
    where.specialization = { contains: specialization, mode: 'insensitive' }
  }
  
  if (isAvailable !== null) {
    where.isAvailable = isAvailable === 'true'
  }
  
  // Get total count for pagination
  const totalCount = await prisma.dentistProfile.count({ where })
  
  // Get dentists with pagination
  const dentists = await prisma.dentistProfile.findMany({
    where,
    skip: (page - 1) * limit,
    take: limit,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          image: true,
          status: true,
        },
      },
      branches: {
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
            },
          },
        },
      },
      schedules: {
        where: { isActive: true },
        orderBy: { dayOfWeek: 'asc' },
      },
      _count: {
        select: {
          appointments: true,
          treatments: true,
        },
      },
    },
    orderBy: sortBy === 'user.name' ? { user: { name: sortOrder } } : { [sortBy as keyof typeof prisma.dentistProfile.fields]: sortOrder },
  })
  
  return NextResponse.json({
    dentists,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/dentists - Create new dentist profile
export const POST = withAuth(async (req: NextRequest) => {
  const body = await req.json()
  const { userId, ...profileData } = body
  
  // Verify the user exists and is a dentist
  const user = await prisma.user.findUnique({
    where: { id: userId },
  })
  
  if (!user || user.role !== UserRole.DENTIST) {
    return NextResponse.json(
      { error: 'Invalid user or user is not a dentist' },
      { status: 400 }
    )
  }
  
  // Check if profile already exists
  const existingProfile = await prisma.dentistProfile.findUnique({
    where: { userId },
  })
  
  if (existingProfile) {
    return NextResponse.json(
      { error: 'Dentist profile already exists' },
      { status: 400 }
    )
  }
  
  const validatedData = dentistProfileCreateSchema.parse(profileData)
  
  // Check if license number is unique
  const existingLicense = await prisma.dentistProfile.findUnique({
    where: { licenseNumber: validatedData.licenseNumber },
  })
  
  if (existingLicense) {
    return NextResponse.json(
      { error: 'License number already exists' },
      { status: 400 }
    )
  }
  
  // Create dentist profile
  const dentistProfile = await prisma.dentistProfile.create({
    data: {
      userId,
      ...validatedData,
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          image: true,
        },
      },
    },
  })
  
  return NextResponse.json(dentistProfile, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF])
