import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Smile, 
  Shield, 
  Zap, 
  Heart, 
  Stethoscope, 
  Sparkles,
  ArrowRight,
  CheckCircle
} from "lucide-react"

const services = [
  {
    id: 1,
    icon: Shield,
    title: "General Dentistry",
    description: "Comprehensive oral health care including cleanings, fillings, and preventive treatments to maintain your dental health.",
    features: ["Regular Cleanings", "Cavity Fillings", "Oral Exams", "Fluoride Treatments"],
    price: "Starting at $150",
    popular: false
  },
  {
    id: 2,
    icon: Sparkles,
    title: "Cosmetic Dentistry",
    description: "Transform your smile with our advanced cosmetic procedures designed to enhance the appearance of your teeth.",
    features: ["Teeth Whitening", "Porcelain Veneers", "Bonding", "Smile Makeovers"],
    price: "Starting at $300",
    popular: true
  },
  {
    id: 3,
    icon: Smile,
    title: "Orthodontics",
    description: "Straighten your teeth and correct bite issues with traditional braces or modern clear aligner treatments.",
    features: ["Traditional Braces", "Clear Aligners", "Retainers", "Bite Correction"],
    price: "Starting at $2,500",
    popular: false
  },
  {
    id: 4,
    icon: Stethoscope,
    title: "Oral Surgery",
    description: "Expert surgical procedures including extractions, implants, and other specialized oral health treatments.",
    features: ["Tooth Extractions", "Dental Implants", "Wisdom Teeth", "Bone Grafting"],
    price: "Starting at $500",
    popular: false
  },
  {
    id: 5,
    icon: Zap,
    title: "Emergency Care",
    description: "Immediate dental care for urgent situations. We're here to help when you need us most.",
    features: ["24/7 Emergency Line", "Same-Day Appointments", "Pain Management", "Urgent Repairs"],
    price: "Call for Pricing",
    popular: false
  },
  {
    id: 6,
    icon: Heart,
    title: "Pediatric Dentistry",
    description: "Specialized dental care for children in a fun, comfortable environment that makes visits enjoyable.",
    features: ["Child-Friendly Environment", "Preventive Care", "Sealants", "Education"],
    price: "Starting at $100",
    popular: false
  }
]

export function ServicesSection() {
  return (
    <section id="services" className="py-16 lg:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Stethoscope className="h-4 w-4" />
            <span>Our Services</span>
          </div>
          
          <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-4">
            Comprehensive Dental Care
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From routine cleanings to complex procedures, we offer a full range of dental services 
            to keep your smile healthy and beautiful.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {services.map((service) => {
            const IconComponent = service.icon
            return (
              <Card 
                key={service.id} 
                className={`relative transition-all duration-300 hover:shadow-lg hover:-translate-y-1 ${
                  service.popular ? 'ring-2 ring-blue-600 shadow-lg' : ''
                }`}
              >
                {service.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <CardHeader className="text-center">
                  <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4 ${
                    service.popular ? 'bg-blue-600 text-white' : 'bg-blue-50 text-blue-600'
                  }`}>
                    <IconComponent className="h-8 w-8" />
                  </div>
                  
                  <CardTitle className="text-xl font-bold">{service.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Features */}
                  <ul className="space-y-2">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-blue-600 flex-shrink-0" />
                        <span className="text-sm text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  {/* Pricing */}
                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-lg font-semibold text-blue-600">
                        {service.price}
                      </span>
                    </div>
                    
                    <Button 
                      className="w-full" 
                      variant={service.popular ? "default" : "outline"}
                    >
                      Learn More
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Call to Action */}
        <div className="text-center bg-white rounded-2xl p-8 lg:p-12 shadow-sm">
          <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            Not Sure Which Service You Need?
          </h3>
          <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
            Our experienced team will help you determine the best treatment plan for your specific needs. 
            Schedule a consultation today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              Schedule Consultation
            </Button>
            <Button variant="outline" size="lg">
              Call (555) 123-4567
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
