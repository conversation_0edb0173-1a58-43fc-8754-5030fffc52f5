import {
  userCreateSchema,
  appointmentCreateSchema,
  patientProfileCreateSchema,
  paginationSchema,
  searchSchema,
} from '@/lib/validations'
import { UserRole, AppointmentType } from '@prisma/client'

describe('Validation Schemas', () => {
  describe('userCreateSchema', () => {
    it('should validate valid user data', () => {
      const validData = {
        email: '<EMAIL>',
        name: '<PERSON>',
        phone: '+**********',
        role: UserRole.PATIENT,
      }

      const result = userCreateSchema.parse(validData)
      expect(result).toEqual(validData)
    })

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        name: '<PERSON>',
        role: UserRole.PATIENT,
      }

      expect(() => userCreateSchema.parse(invalidData)).toThrow()
    })

    it('should reject short name', () => {
      const invalidData = {
        email: '<EMAIL>',
        name: '<PERSON>', // Too short
        role: UserRole.PATIENT,
      }

      expect(() => userCreateSchema.parse(invalidData)).toThrow()
    })

    it('should reject invalid role', () => {
      const invalidData = {
        email: '<EMAIL>',
        name: 'John Doe',
        role: 'INVALID_ROLE',
      }

      expect(() => userCreateSchema.parse(invalidData)).toThrow()
    })

    it('should allow optional phone', () => {
      const validData = {
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.PATIENT,
        // phone is optional
      }

      const result = userCreateSchema.parse(validData)
      expect(result.phone).toBeUndefined()
    })
  })

  describe('appointmentCreateSchema', () => {
    it('should validate valid appointment data', () => {
      const validData = {
        patientId: 'cuid_patient_id_**********12345',
        dentistId: 'cuid_dentist_id_**********12345',
        branchId: 'cuid_branch_id_**********123456',
        serviceId: 'cuid_service_id_**********12345',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 60,
        type: AppointmentType.CONSULTATION,
        notes: 'Patient notes',
        symptoms: 'Tooth pain',
      }

      const result = appointmentCreateSchema.parse(validData)
      expect(result.scheduledAt).toBeInstanceOf(Date)
      expect(result.duration).toBe(60)
      expect(result.type).toBe(AppointmentType.CONSULTATION)
    })

    it('should reject invalid CUID', () => {
      const invalidData = {
        patientId: 'invalid-id',
        dentistId: 'cuid_dentist_id_**********12345',
        branchId: 'cuid_branch_id_**********123456',
        serviceId: 'cuid_service_id_**********12345',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 60,
      }

      expect(() => appointmentCreateSchema.parse(invalidData)).toThrow()
    })

    it('should reject invalid duration', () => {
      const invalidData = {
        patientId: 'cuid_patient_id_**********12345',
        dentistId: 'cuid_dentist_id_**********12345',
        branchId: 'cuid_branch_id_**********123456',
        serviceId: 'cuid_service_id_**********12345',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 10, // Too short (minimum is 15)
      }

      expect(() => appointmentCreateSchema.parse(invalidData)).toThrow()
    })

    it('should reject duration too long', () => {
      const invalidData = {
        patientId: 'cuid_patient_id_**********12345',
        dentistId: 'cuid_dentist_id_**********12345',
        branchId: 'cuid_branch_id_**********123456',
        serviceId: 'cuid_service_id_**********12345',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 500, // Too long (maximum is 480)
      }

      expect(() => appointmentCreateSchema.parse(invalidData)).toThrow()
    })

    it('should set default appointment type', () => {
      const validData = {
        patientId: 'cuid_patient_id_**********12345',
        dentistId: 'cuid_dentist_id_**********12345',
        branchId: 'cuid_branch_id_**********123456',
        serviceId: 'cuid_service_id_**********12345',
        scheduledAt: '2024-01-01T10:00:00Z',
        duration: 60,
        // type not provided
      }

      const result = appointmentCreateSchema.parse(validData)
      expect(result.type).toBe(AppointmentType.CONSULTATION)
    })
  })

  describe('patientProfileCreateSchema', () => {
    it('should validate valid patient profile data', () => {
      const validData = {
        dateOfBirth: '1990-01-01',
        gender: 'Male',
        address: '123 Main St, City, State',
        emergencyContact: 'Jane Doe',
        emergencyPhone: '+**********',
        insuranceProvider: 'Health Insurance Co.',
        insuranceNumber: 'INS123456',
        medicalHistory: 'No significant medical history',
        allergies: 'None known',
        medications: 'None',
      }

      const result = patientProfileCreateSchema.parse(validData)
      expect(result.dateOfBirth).toBeInstanceOf(Date)
      expect(result.gender).toBe('Male')
    })

    it('should allow all fields to be optional', () => {
      const validData = {}

      const result = patientProfileCreateSchema.parse(validData)
      expect(result).toEqual({})
    })

    it('should transform date string to Date object', () => {
      const validData = {
        dateOfBirth: '1990-01-01',
      }

      const result = patientProfileCreateSchema.parse(validData)
      expect(result.dateOfBirth).toBeInstanceOf(Date)
      expect(result.dateOfBirth?.getFullYear()).toBe(1990)
    })
  })

  describe('paginationSchema', () => {
    it('should validate valid pagination data', () => {
      const validData = {
        page: 2,
        limit: 20,
      }

      const result = paginationSchema.parse(validData)
      expect(result.page).toBe(2)
      expect(result.limit).toBe(20)
    })

    it('should set default values', () => {
      const result = paginationSchema.parse({})
      expect(result.page).toBe(1)
      expect(result.limit).toBe(10)
    })

    it('should reject page less than 1', () => {
      const invalidData = {
        page: 0,
        limit: 10,
      }

      expect(() => paginationSchema.parse(invalidData)).toThrow()
    })

    it('should reject limit greater than 100', () => {
      const invalidData = {
        page: 1,
        limit: 101,
      }

      expect(() => paginationSchema.parse(invalidData)).toThrow()
    })

    it('should reject negative limit', () => {
      const invalidData = {
        page: 1,
        limit: -1,
      }

      expect(() => paginationSchema.parse(invalidData)).toThrow()
    })
  })

  describe('searchSchema', () => {
    it('should validate valid search data', () => {
      const validData = {
        query: 'search term',
        sortBy: 'name',
        sortOrder: 'asc' as const,
      }

      const result = searchSchema.parse(validData)
      expect(result.query).toBe('search term')
      expect(result.sortBy).toBe('name')
      expect(result.sortOrder).toBe('asc')
    })

    it('should set default sort order', () => {
      const validData = {
        query: 'search term',
        sortBy: 'name',
      }

      const result = searchSchema.parse(validData)
      expect(result.sortOrder).toBe('desc')
    })

    it('should allow undefined query', () => {
      const validData = {
        sortBy: 'name',
        sortOrder: 'asc' as const,
      }

      const result = searchSchema.parse(validData)
      expect(result.query).toBeUndefined()
    })

    it('should reject invalid sort order', () => {
      const invalidData = {
        query: 'search term',
        sortBy: 'name',
        sortOrder: 'invalid',
      }

      expect(() => searchSchema.parse(invalidData)).toThrow()
    })
  })
})
