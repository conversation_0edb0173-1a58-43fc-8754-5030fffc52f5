import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { UserRole, FileType } from '@prisma/client'
import { z } from 'zod'

const fileUploadSchema = z.object({
  patientId: z.string().cuid(),
  fileName: z.string().min(1),
  originalName: z.string().min(1),
  fileType: z.nativeEnum(FileType),
  fileSize: z.number().min(1),
  mimeType: z.string().min(1),
  url: z.string().url(),
  description: z.string().optional(),
})

// GET /api/files/patient - List patient files
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  const patientId = searchParams.get('patientId')
  const fileType = searchParams.get('fileType') as FileType | null
  const page = Number(searchParams.get('page')) || 1
  const limit = Number(searchParams.get('limit')) || 20
  
  if (!patientId) {
    return NextResponse.json(
      { error: 'patientId is required' },
      { status: 400 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const skip = (page - 1) * limit
  
  const where = {
    patientId,
    ...(fileType && { fileType }),
  }
  
  const [files, total] = await Promise.all([
    prisma.patientFile.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    }),
    prisma.patientFile.count({ where }),
  ])
  
  return NextResponse.json({
    files,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/files/patient - Upload patient file
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = fileUploadSchema.parse(body)
  
  // Check access permissions
  if (!canAccessPatientData(user.role, validatedData.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Verify patient exists
  const patient = await prisma.patientProfile.findUnique({
    where: { id: validatedData.patientId },
  })
  
  if (!patient) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Create file record
  const file = await prisma.patientFile.create({
    data: {
      ...validatedData,
      uploadedBy: user.id,
    },
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  })
  
  return NextResponse.json(file, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
