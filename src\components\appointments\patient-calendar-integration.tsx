'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  CheckCircle,
  Clock,
  MapPin,
  User
} from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'
import { AddToCalendarButton } from './add-to-calendar-button'

interface PatientCalendarIntegrationProps {
  appointment: AppointmentDetails
  showAppointmentDetails?: boolean
}

export function PatientCalendarIntegration({ 
  appointment, 
  showAppointmentDetails = true 
}: PatientCalendarIntegrationProps) {
  const [addedToCalendar] = useState(false)

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    }).format(date)
  }

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Manila'
    }).format(date)
  }

  const appointmentDate = new Date(appointment.scheduledAt)
  const endTime = new Date(appointmentDate.getTime() + appointment.duration * 60000)

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-2">
          <CheckCircle className="h-8 w-8 text-green-600 mr-2" />
          <CardTitle className="text-2xl text-green-600">Appointment Confirmed!</CardTitle>
        </div>
        <CardDescription>
          Your dental appointment has been successfully scheduled. Add it to your calendar so you don&apos;t forget!
        </CardDescription>
      </CardHeader>

      {showAppointmentDetails && (
        <CardContent className="space-y-4">
          {/* Appointment Details */}
          <div className="bg-blue-50 p-4 rounded-lg space-y-3">
            <h3 className="font-semibold text-lg text-blue-900 mb-3">Appointment Details</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-start space-x-3">
                <Calendar className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-900">{formatDate(appointmentDate)}</p>
                  <p className="text-blue-700">{formatTime(appointmentDate)} - {formatTime(endTime)}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-900">{appointment.service.name}</p>
                  <p className="text-blue-700">{appointment.duration} minutes</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <User className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-900">Dr. {appointment.dentist.user.name}</p>
                  <p className="text-blue-700">Dentist</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-900">{appointment.branch.name}</p>
                  <p className="text-blue-700">{appointment.branch.address}</p>
                </div>
              </div>
            </div>

            <div className="pt-2">
              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                Status: {appointment.status}
              </Badge>
            </div>
          </div>

          {/* Calendar Integration */}
          <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg">
            <div className="text-center mb-4">
              <h3 className="font-semibold text-lg text-gray-900 mb-2">
                📅 Add to Your Calendar
              </h3>
              <p className="text-gray-600 text-sm">
                Never miss your appointment! Add it to your preferred calendar app.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
              <AddToCalendarButton 
                appointment={appointment}
                variant="default"
                size="default"
                className="w-full sm:w-auto"
              />
              
              {addedToCalendar && (
                <div className="flex items-center text-green-600 text-sm">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Added to calendar!
                </div>
              )}
            </div>

            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500">
                Supports Google Calendar, Outlook, Apple Calendar, Yahoo Calendar, and more
              </p>
            </div>
          </div>

          {/* Important Reminders */}
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">📋 Important Reminders</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Please arrive 15 minutes early for check-in</li>
              <li>• Bring a valid ID and insurance card (if applicable)</li>
              <li>• If you need to reschedule, please call at least 24 hours in advance</li>
              <li>• You&apos;ll receive a reminder 24 hours before your appointment</li>
            </ul>
          </div>

          {/* Contact Information */}
          <div className="text-center text-sm text-gray-600">
            <p>Questions? Contact us at <span className="font-medium"><EMAIL></span></p>
            <p>Appointment ID: <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">{appointment.id}</span></p>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
