'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, Clock, DollarSign, CheckCircle } from 'lucide-react'
import { Service } from '@/types/appointment'

interface ServiceSelectionStepProps {
  branchId: string
  selectedServiceId?: string
  onSelect: (serviceId: string, duration: number) => void
  onNext: () => void
}

export function ServiceSelectionStep({ branchId, selectedServiceId, onSelect, onNext }: ServiceSelectionStepProps) {
  const [services, setServices] = useState<Service[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedService, setSelectedService] = useState<Service | null>(null)

  const fetchServices = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/services?branchId=${branchId}&isActive=true`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch services')
      }
      
      const data = await response.json()
      setServices(data.services || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load services')
    } finally {
      setIsLoading(false)
    }
  }, [branchId])

  useEffect(() => {
    fetchServices()
  }, [branchId, fetchServices])

  const handleServiceSelect = (service: Service) => {
    setSelectedService(service)
    onSelect(service.id, service.duration)
  }

  const handleNext = () => {
    if (selectedService) {
      onNext()
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const groupServicesByCategory = (services: Service[]) => {
    return services.reduce((groups, service) => {
      const category = service.category
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(service)
      return groups
    }, {} as Record<string, Service[]>)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading services...</span>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (services.length === 0) {
    return (
      <Alert>
        <AlertDescription>No services are currently available for this branch. Please try another branch.</AlertDescription>
      </Alert>
    )
  }

  const serviceGroups = groupServicesByCategory(services)

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 mb-4">
        Please select the dental service you need for your appointment.
      </div>

      <div className="space-y-6">
        {Object.entries(serviceGroups).map(([category, categoryServices]) => (
          <div key={category}>
            <h3 className="text-lg font-semibold text-gray-900 mb-3 capitalize">
              {category.replace('_', ' ')}
            </h3>
            <div className="grid gap-4 md:grid-cols-2">
              {categoryServices.map((service) => (
                <Card
                  key={service.id}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                    selectedServiceId === service.id
                      ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                      : 'hover:border-gray-400'
                  }`}
                  onClick={() => handleServiceSelect(service)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg text-gray-900 flex items-center">
                          {service.name}
                          {selectedServiceId === service.id && (
                            <CheckCircle className="h-5 w-5 text-blue-600 ml-2" />
                          )}
                        </CardTitle>
                        {service.description && (
                          <CardDescription className="mt-1">
                            {service.description}
                          </CardDescription>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="h-4 w-4 mr-1 text-gray-400" />
                          <span>{formatDuration(service.duration)}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                          <span>{formatPrice(service.price)}</span>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        {service.category.replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>

      {selectedService && (
        <div className="flex justify-end pt-4">
          <Button onClick={handleNext} className="bg-blue-600 hover:bg-blue-700">
            Continue to Dentist Selection
          </Button>
        </div>
      )}
    </div>
  )
}
