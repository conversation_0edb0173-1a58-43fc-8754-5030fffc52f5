import { AppointmentStatus, AppointmentType } from '@prisma/client'

export interface Branch {
  id: string
  name: string
  address: string
  phone?: string
  email?: string
  description?: string
  isActive: boolean
}

export interface Service {
  id: string
  name: string
  description?: string
  category: string
  duration: number
  price: number
  isActive: boolean
}

export interface DentistProfile {
  id: string
  licenseNumber: string
  specialization?: string
  yearsExperience?: number
  bio?: string
  consultationFee?: number
  isAvailable: boolean
  user: {
    id: string
    name: string
    image?: string
  }
  schedules: DentistSchedule[]
}

export interface DentistSchedule {
  id: string
  dayOfWeek: number
  startTime: string
  endTime: string
  isActive: boolean
}

export interface TimeSlot {
  startTime: string
  endTime: string
  available: boolean
}

export interface AvailabilityResponse {
  available: boolean
  dentist: {
    id: string
    name: string
    specialization?: string
  }
  date: string
  workingHours: {
    start: string
    end: string
  }
  timeSlots: TimeSlot[]
}

export interface AppointmentFormData {
  branchId: string
  serviceId: string
  dentistId: string
  scheduledAt: Date
  duration: number
  type: AppointmentType
  notes?: string
  symptoms?: string
}

export interface AppointmentBookingStep {
  id: string
  title: string
  description: string
  isComplete: boolean
  isActive: boolean
}

export interface PatientInfo {
  id: string
  name: string
  email: string
  phone?: string
  dateOfBirth?: Date
  gender?: string
  address?: string
  emergencyContact?: string
  emergencyPhone?: string
  medicalHistory?: string
  allergies?: string
  medications?: string
}

export interface AppointmentDetails {
  id: string
  scheduledAt: Date
  duration: number
  status: AppointmentStatus
  type: AppointmentType
  notes?: string
  symptoms?: string
  patient: {
    user: {
      name: string
      email: string
      phone?: string
    }
  }
  dentist: {
    id: string
    user: {
      name: string
    }
    specialization?: string
  }
  branch: {
    name: string
    address: string
  }
  service: {
    name: string
    category: string
    duration: number
    price: number
  }
}

export interface AppointmentListFilters {
  status?: string
  dateFrom?: string
  dateTo?: string
  sortBy?: 'scheduledAt' | 'createdAt' | 'status'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}

export interface AppointmentListResponse {
  appointments: AppointmentDetails[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    totalCount?: number
  }
}

export interface DashboardStats {
  upcomingAppointments: number
  completedAppointments: number
  totalSpent: number
  nextAppointment?: AppointmentDetails
}
