'use client'

import { useState, useCallback, useEffect } from 'react'
import { AvailabilityResponse } from '@/types/appointment'

interface UseAvailabilityReturn {
  availability: AvailabilityResponse | null
  isLoading: boolean
  error: string | null
  checkAvailability: (dentistId: string, date: Date, duration?: number) => Promise<void>
  clearError: () => void
}

export function useAvailability(): UseAvailabilityReturn {
  const [availability, setAvailability] = useState<AvailabilityResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const checkAvailability = useCallback(async (dentistId: string, date: Date, duration = 60): Promise<void> => {
    setIsLoading(true)
    setError(null)

    try {
      const dateString = date.toISOString().split('T')[0]
      const response = await fetch(
        `/api/appointments/availability?dentistId=${dentistId}&date=${dateString}&duration=${duration}`
      )

      if (!response.ok) {
        throw new Error('Failed to fetch availability')
      }

      const data = await response.json()
      setAvailability(data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load availability'
      setError(errorMessage)
      setAvailability(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    availability,
    isLoading,
    error,
    checkAvailability,
    clearError,
  }
}

// Hook for checking multiple dentists availability
interface UseBulkAvailabilityReturn {
  availabilities: Record<string, AvailabilityResponse>
  isLoading: boolean
  error: string | null
  checkBulkAvailability: (dentistIds: string[], date: Date, duration?: number) => Promise<void>
  clearError: () => void
}

export function useBulkAvailability(): UseBulkAvailabilityReturn {
  const [availabilities, setAvailabilities] = useState<Record<string, AvailabilityResponse>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const checkBulkAvailability = useCallback(async (dentistIds: string[], date: Date, duration = 60): Promise<void> => {
    setIsLoading(true)
    setError(null)

    try {
      const dateString = date.toISOString().split('T')[0]
      const response = await fetch('/api/appointments/availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dentistIds,
          date: dateString,
          duration,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to fetch bulk availability')
      }

      const data = await response.json()
      
      // Convert array response to object keyed by dentist ID
      const availabilityMap: Record<string, AvailabilityResponse> = {}
      data.dentists.forEach((dentistAvailability: any) => {
        if (dentistAvailability.dentistId) {
          availabilityMap[dentistAvailability.dentistId] = dentistAvailability
        }
      })
      
      setAvailabilities(availabilityMap)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load bulk availability'
      setError(errorMessage)
      setAvailabilities({})
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    availabilities,
    isLoading,
    error,
    checkBulkAvailability,
    clearError,
  }
}

// Hook for real-time availability updates
export function useRealTimeAvailability(dentistId: string, date: Date, duration = 60, intervalMs = 30000) {
  const { availability, isLoading, error, checkAvailability, clearError } = useAvailability()
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(false)

  useEffect(() => {
    if (!isRealTimeEnabled || !dentistId || !date) return

    // Initial check
    checkAvailability(dentistId, date, duration)

    // Set up interval for real-time updates
    const interval = setInterval(() => {
      checkAvailability(dentistId, date, duration)
    }, intervalMs)

    return () => clearInterval(interval)
  }, [dentistId, date, duration, intervalMs, isRealTimeEnabled, checkAvailability])

  const enableRealTime = useCallback(() => {
    setIsRealTimeEnabled(true)
  }, [])

  const disableRealTime = useCallback(() => {
    setIsRealTimeEnabled(false)
  }, [])

  return {
    availability,
    isLoading,
    error,
    isRealTimeEnabled,
    enableRealTime,
    disableRealTime,
    checkAvailability,
    clearError,
  }
}
