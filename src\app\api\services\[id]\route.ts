import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { serviceUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/services/[id] - Get service by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const service = await prisma.service.findUnique({
    where: { id },
    include: {
      branches: {
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
            },
          },
        },
      },
      appointments: {
        take: 10,
        orderBy: { scheduledAt: 'desc' },
        include: {
          patient: {
            include: {
              user: { select: { name: true } },
            },
          },
          dentist: {
            include: {
              user: { select: { name: true } },
            },
          },
        },
      },
      treatments: {
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          patient: {
            include: {
              user: { select: { name: true } },
            },
          },
          dentist: {
            include: {
              user: { select: { name: true } },
            },
          },
        },
      },
      _count: {
        select: {
          appointments: true,
          treatments: true,
        },
      },
    },
  })
  
  if (!service) {
    return NextResponse.json(
      { error: 'Service not found' },
      { status: 404 }
    )
  }
  
  return NextResponse.json(service)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/services/[id] - Update service
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  const existingService = await prisma.service.findUnique({
    where: { id },
  })
  
  if (!existingService) {
    return NextResponse.json(
      { error: 'Service not found' },
      { status: 404 }
    )
  }
  
  const validatedData = serviceUpdateSchema.parse(body)
  
  const updatedService = await prisma.service.update({
    where: { id },
    data: validatedData,
    include: {
      branches: {
        include: {
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      _count: {
        select: {
          appointments: true,
          treatments: true,
        },
      },
    },
  })
  
  return NextResponse.json(updatedService)
}, [UserRole.ADMIN, UserRole.STAFF])

// DELETE /api/services/[id] - Delete service
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const service = await prisma.service.findUnique({
    where: { id },
    include: {
      appointments: true,
      treatments: true,
    },
  })
  
  if (!service) {
    return NextResponse.json(
      { error: 'Service not found' },
      { status: 404 }
    )
  }
  
  // Check if service has associated appointments or treatments
  if (service.appointments.length > 0 || service.treatments.length > 0) {
    return NextResponse.json(
      { error: 'Cannot delete service with associated appointments or treatments. Consider marking as inactive instead.' },
      { status: 400 }
    )
  }
  
  await prisma.service.delete({
    where: { id },
  })
  
  return NextResponse.json({ message: 'Service deleted successfully' })
}, [UserRole.ADMIN])
