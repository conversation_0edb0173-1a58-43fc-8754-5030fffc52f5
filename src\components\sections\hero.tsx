"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Star, Shield, Clock, Award } from "lucide-react"

const heroSlides = [
  {
    id: 1,
    title: "Your Smile is Our Priority",
    subtitle: "Professional Dental Care You Can Trust",
    description: "Experience exceptional dental care with our team of experienced professionals. We provide comprehensive dental services in a comfortable, modern environment.",
    cta: "Book Your Appointment",
    image: "/api/placeholder/800/600",
    features: ["Advanced Technology", "Gentle Care", "Flexible Scheduling"]
  },
  {
    id: 2,
    title: "Transform Your Smile Today",
    subtitle: "Cosmetic Dentistry Excellence",
    description: "Discover the confidence that comes with a beautiful smile. Our cosmetic dentistry services help you achieve the smile you've always dreamed of.",
    cta: "Schedule Consultation",
    image: "/api/placeholder/800/600",
    features: ["Teeth Whitening", "Veneers", "Smile Makeovers"]
  },
  {
    id: 3,
    title: "Emergency Dental Care",
    subtitle: "We're Here When You Need Us",
    description: "Dental emergencies can happen anytime. Our experienced team provides prompt, professional emergency dental care to relieve your pain and protect your oral health.",
    cta: "Emergency Contact",
    image: "/api/placeholder/800/600",
    features: ["24/7 Emergency Line", "Same-Day Appointments", "Pain Relief"]
  }
]

export function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 5000)

    return () => clearInterval(timer)
  }, [])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length)
  }

  const currentSlideData = heroSlides[currentSlide]

  return (
    <section id="home" className="relative bg-gradient-to-br from-blue-50 via-green-50/30 to-white overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-100 rounded-full opacity-20 blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 py-16 lg:py-24 relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-green-50 text-blue-700 px-4 py-2 rounded-full text-sm font-semibold shadow-sm border border-blue-100">
                <Star className="h-4 w-4 text-yellow-500" />
                <span>Trusted by 5,000+ Patients</span>
              </div>
              
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                {currentSlideData.title}
              </h1>
              
              <h2 className="text-xl lg:text-2xl text-blue-600 font-semibold">
                {currentSlideData.subtitle}
              </h2>
              
              <p className="text-lg text-gray-600 leading-relaxed">
                {currentSlideData.description}
              </p>
            </div>

            {/* Features */}
            <div className="flex flex-wrap gap-4">
              {currentSlideData.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 bg-white rounded-lg px-4 py-2 shadow-sm border">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-3">
                {currentSlideData.cta}
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                Learn More
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center space-x-8 pt-4">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-blue-600" />
                <span className="text-sm text-gray-600">HIPAA Compliant</span>
              </div>
              <div className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-blue-600" />
                <span className="text-sm text-gray-600">ADA Certified</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-blue-600" />
                <span className="text-sm text-gray-600">Same Day Appointments</span>
              </div>
            </div>
          </div>

          {/* Image/Visual */}
          <div className="relative">
            <div className="relative bg-gradient-to-br from-blue-100 to-blue-50 rounded-2xl p-8 lg:p-12">
              {/* Placeholder for dental clinic image */}
              <div className="aspect-[4/3] bg-white rounded-xl shadow-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">🦷</span>
                  </div>
                  <p className="text-gray-500">Professional Dental Care Image</p>
                  <p className="text-sm text-gray-400 mt-2">Placeholder - Upload your clinic photos</p>
                </div>
              </div>
              
              {/* Floating elements */}
              <div className="absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-4">
                <div className="flex items-center space-x-2">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-current" />
                    ))}
                  </div>
                  <span className="text-sm font-medium">4.9/5</span>
                </div>
                <p className="text-xs text-gray-600 mt-1">500+ Reviews</p>
              </div>
            </div>
          </div>
        </div>

        {/* Carousel Controls */}
        <div className="flex justify-center items-center space-x-4 mt-12">
          <button
            onClick={prevSlide}
            className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
            aria-label="Previous slide"
          >
            <ChevronLeft className="h-5 w-5 text-gray-600" />
          </button>
          
          <div className="flex space-x-2">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentSlide ? 'bg-blue-600' : 'bg-gray-300'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
          
          <button
            onClick={nextSlide}
            className="p-2 rounded-full bg-white shadow-md hover:shadow-lg transition-shadow"
            aria-label="Next slide"
          >
            <ChevronRight className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>
    </section>
  )
}
