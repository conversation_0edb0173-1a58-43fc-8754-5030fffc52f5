import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/appointments/[id]/calendar - Generate calendar file for appointment
export const GET = withAuth(async (req: NextRequest, user, { params }) => {
  try {
    const appointmentId = params.id as string
    
    // Get appointment with all related data
    const appointment = await prisma.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
              },
            },
          },
        },
        dentist: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
        service: {
          select: {
            id: true,
            name: true,
            category: true,
            duration: true,
            price: true,
          },
        },
      },
    })

    if (!appointment) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      )
    }

    // Check if user has access to this appointment
    if (user.role === UserRole.PATIENT && appointment.patient.userId !== user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Generate ICS content with proper Philippine timezone
    const startTime = new Date(appointment.scheduledAt)
    const endTime = new Date(startTime.getTime() + appointment.duration * 60000)
    const timezone = process.env.TIMEZONE || 'Asia/Manila'

    const formatDateForICS = (date: Date, includeTimezone: boolean = true): string => {
      if (includeTimezone) {
        // Format in Philippine timezone
        const philippineTime = new Date(date.toLocaleString("en-US", {timeZone: timezone}))
        const year = philippineTime.getFullYear()
        const month = String(philippineTime.getMonth() + 1).padStart(2, '0')
        const day = String(philippineTime.getDate()).padStart(2, '0')
        const hours = String(philippineTime.getHours()).padStart(2, '0')
        const minutes = String(philippineTime.getMinutes()).padStart(2, '0')
        const seconds = String(philippineTime.getSeconds()).padStart(2, '0')

        return `${year}${month}${day}T${hours}${minutes}${seconds}`
      } else {
        // UTC format for DTSTAMP
        return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'
      }
    }

    const createEventDescription = (): string => {
      let description = `Dental Appointment Details:\\n\\n`
      description += `Service: ${appointment.service.name}\\n`
      description += `Dentist: Dr. ${appointment.dentist.user.name}\\n`
      description += `Duration: ${appointment.duration} minutes\\n`
      description += `Location: ${appointment.branch.name}\\n`
      description += `Address: ${appointment.branch.address}\\n\\n`

      if (appointment.symptoms) {
        description += `Symptoms/Concerns: ${appointment.symptoms}\\n\\n`
      }

      if (appointment.notes) {
        description += `Additional Notes: ${appointment.notes}\\n\\n`
      }

      description += `Please arrive 15 minutes early for check-in.\\n\\n`
      description += `Appointment ID: ${appointment.id}`

      return description
    }

    const icsContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Bright Smile Dental//Appointment//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:PUBLISH',
      // Add timezone definition
      'BEGIN:VTIMEZONE',
      `TZID:${timezone}`,
      'BEGIN:STANDARD',
      'DTSTART:19700101T000000',
      'TZOFFSETFROM:+0800',
      'TZOFFSETTO:+0800',
      'TZNAME:PST',
      'END:STANDARD',
      'END:VTIMEZONE',
      'BEGIN:VEVENT',
      `UID:appointment-${appointment.id}@brightsmile.com`,
      `DTSTAMP:${formatDateForICS(new Date(), false)}`,
      `DTSTART;TZID=${timezone}:${formatDateForICS(startTime, true)}`,
      `DTEND;TZID=${timezone}:${formatDateForICS(endTime, true)}`,
      `SUMMARY:${appointment.service.name} - Bright Smile Dental`,
      `DESCRIPTION:${createEventDescription()}`,
      `LOCATION:${appointment.branch.name}, ${appointment.branch.address}`,
      'STATUS:CONFIRMED',
      'TRANSP:OPAQUE',
      'CATEGORIES:APPOINTMENT,HEALTHCARE',
      'BEGIN:VALARM',
      'TRIGGER:-PT1H',
      'ACTION:DISPLAY',
      'DESCRIPTION:Appointment reminder - Please arrive 15 minutes early',
      'END:VALARM',
      'BEGIN:VALARM',
      'TRIGGER:-P1D',
      'ACTION:DISPLAY',
      'DESCRIPTION:Appointment tomorrow - Bright Smile Dental',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n')

    // Return ICS file
    return new NextResponse(icsContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/calendar; charset=utf-8',
        'Content-Disposition': `attachment; filename="appointment-${appointment.id}.ics"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    })
  } catch (error) {
    console.error('Failed to generate calendar file:', error)
    return NextResponse.json(
      { error: 'Failed to generate calendar file' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
