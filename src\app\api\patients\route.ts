import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/patients - List patients with pagination and search
export const GET = withAuth(async (req: NextRequest) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const skip = (page - 1) * limit
  
  const where = {
    user: {
      role: UserRole.PATIENT,
      ...(query && {
        OR: [
          { name: { contains: query, mode: 'insensitive' as const } },
          { email: { contains: query, mode: 'insensitive' as const } },
        ],
      }),
    },
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'updatedAt' ? { updatedAt: sortOrder } :
                  { createdAt: sortOrder as 'asc' | 'desc' }

  const [patients, total] = await Promise.all([
    prisma.patientProfile.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
            phone: true,
            status: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            appointments: true,
            treatments: true,
            invoices: true,
          },
        },
      },
    }),
    prisma.patientProfile.count({ where }),
  ])
  
  return NextResponse.json({
    patients,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])

// POST /api/patients - Create patient profile (usually done automatically during user creation)
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const { userId, ...profileData } = body

  // Patients can only create their own profile, admins/staff can create for anyone
  if (user.role === UserRole.PATIENT && user.id !== userId) {
    return NextResponse.json(
      { error: 'You can only create your own patient profile' },
      { status: 403 }
    )
  }

  // Verify the target user exists and is a patient
  const targetUser = await prisma.user.findUnique({
    where: { id: userId },
  })

  if (!targetUser || targetUser.role !== UserRole.PATIENT) {
    return NextResponse.json(
      { error: 'Invalid user or user is not a patient' },
      { status: 400 }
    )
  }

  // Check if profile already exists
  const existingProfile = await prisma.patientProfile.findUnique({
    where: { userId },
  })

  if (existingProfile) {
    return NextResponse.json(
      { error: 'Patient profile already exists' },
      { status: 400 }
    )
  }

  const patientProfile = await prisma.patientProfile.create({
    data: {
      userId,
      ...profileData,
    },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          phone: true,
        },
      },
    },
  })

  return NextResponse.json(patientProfile, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.PATIENT])
