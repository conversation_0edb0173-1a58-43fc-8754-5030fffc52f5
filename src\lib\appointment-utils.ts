import { AppointmentStatus, AppointmentType } from '@prisma/client'
import { AppointmentDetails, TimeSlot } from '@/types/appointment'

// Date and time formatting utilities with Philippine timezone
export const formatAppointmentDate = (date: Date | string): string => {
  const appointmentDate = new Date(date)
  return appointmentDate.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Asia/Manila'
  })
}

export const formatAppointmentTime = (date: Date | string): string => {
  const appointmentDate = new Date(date)
  return appointmentDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    timeZone: 'Asia/Manila'
  })
}

export const formatAppointmentDateTime = (date: Date | string): string => {
  return `${formatAppointmentDate(date)} at ${formatAppointmentTime(date)}`
}

export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} min`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
}

export const formatPrice = (price: number, currency = 'PHP'): string => {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency,
  }).format(price)
}

// Status utilities
export const getStatusColor = (status: AppointmentStatus): string => {
  switch (status) {
    case 'SCHEDULED':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'CONFIRMED':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'IN_PROGRESS':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'COMPLETED':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'CANCELLED':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'NO_SHOW':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export const getStatusLabel = (status: AppointmentStatus): string => {
  return status.replace('_', ' ')
}

export const getTypeLabel = (type: AppointmentType): string => {
  switch (type) {
    case 'CONSULTATION':
      return 'Consultation'
    case 'CLEANING':
      return 'Cleaning'
    case 'FILLING':
      return 'Filling'
    case 'EXTRACTION':
      return 'Extraction'
    case 'ROOT_CANAL':
      return 'Root Canal'
    case 'CROWN':
      return 'Crown'
    case 'BRIDGE':
      return 'Bridge'
    case 'IMPLANT':
      return 'Implant'
    case 'ORTHODONTICS':
      return 'Orthodontics'
    case 'EMERGENCY':
      return 'Emergency'
    case 'FOLLOW_UP':
      return 'Follow-up'
    default:
      return String(type).replace('_', ' ')
  }
}

// Appointment state checks
export const canRescheduleAppointment = (appointment: AppointmentDetails): boolean => {
  const isUpcoming = new Date(appointment.scheduledAt) > new Date()
  const validStatuses: AppointmentStatus[] = ['SCHEDULED', 'CONFIRMED']
  return isUpcoming && validStatuses.includes(appointment.status)
}

export const canCancelAppointment = (appointment: AppointmentDetails): boolean => {
  const isUpcoming = new Date(appointment.scheduledAt) > new Date()
  const validStatuses: AppointmentStatus[] = ['SCHEDULED', 'CONFIRMED']
  return isUpcoming && validStatuses.includes(appointment.status)
}

export const isAppointmentUpcoming = (appointment: AppointmentDetails): boolean => {
  return new Date(appointment.scheduledAt) > new Date()
}

export const isAppointmentToday = (appointment: AppointmentDetails): boolean => {
  const today = new Date()
  const appointmentDate = new Date(appointment.scheduledAt)
  return (
    today.getFullYear() === appointmentDate.getFullYear() &&
    today.getMonth() === appointmentDate.getMonth() &&
    today.getDate() === appointmentDate.getDate()
  )
}

export const getAppointmentTimeUntil = (appointment: AppointmentDetails): string => {
  const now = new Date()
  const appointmentDate = new Date(appointment.scheduledAt)
  const diffMs = appointmentDate.getTime() - now.getTime()
  
  if (diffMs <= 0) {
    return 'Past appointment'
  }
  
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (diffDays > 0) {
    return `in ${diffDays} day${diffDays > 1 ? 's' : ''}`
  } else if (diffHours > 0) {
    return `in ${diffHours} hour${diffHours > 1 ? 's' : ''}`
  } else if (diffMinutes > 0) {
    return `in ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`
  } else {
    return 'very soon'
  }
}

// Time slot utilities
export const isTimeSlotAvailable = (slot: TimeSlot): boolean => {
  return slot.available
}

export const getAvailableTimeSlots = (slots: TimeSlot[]): TimeSlot[] => {
  return slots.filter(isTimeSlotAvailable)
}

export const formatTimeSlot = (slot: TimeSlot): string => {
  return `${formatAppointmentTime(`2000-01-01T${slot.startTime}:00`)} - ${formatAppointmentTime(`2000-01-01T${slot.endTime}:00`)}`
}

// Date validation utilities
export const isDateDisabled = (date: Date): boolean => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  // Disable past dates
  if (date < today) {
    return true
  }
  
  // Disable Sundays (assuming clinic is closed)
  if (date.getDay() === 0) {
    return true
  }
  
  return false
}

export const getNextAvailableDate = (): Date => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  // If tomorrow is Sunday, move to Monday
  if (tomorrow.getDay() === 0) {
    tomorrow.setDate(tomorrow.getDate() + 1)
  }
  
  return tomorrow
}

// Appointment grouping utilities
export const groupAppointmentsByDate = (appointments: AppointmentDetails[]): Record<string, AppointmentDetails[]> => {
  return appointments.reduce((groups, appointment) => {
    const date = new Date(appointment.scheduledAt).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(appointment)
    return groups
  }, {} as Record<string, AppointmentDetails[]>)
}

export const groupAppointmentsByStatus = (appointments: AppointmentDetails[]): Record<AppointmentStatus, AppointmentDetails[]> => {
  return appointments.reduce((groups, appointment) => {
    if (!groups[appointment.status]) {
      groups[appointment.status] = []
    }
    groups[appointment.status].push(appointment)
    return groups
  }, {} as Record<AppointmentStatus, AppointmentDetails[]>)
}

// Search and filter utilities
export const filterAppointmentsBySearch = (appointments: AppointmentDetails[], query: string): AppointmentDetails[] => {
  if (!query.trim()) {
    return appointments
  }
  
  const searchTerm = query.toLowerCase()
  return appointments.filter(appointment => 
    appointment.service.name.toLowerCase().includes(searchTerm) ||
    appointment.dentist.user.name.toLowerCase().includes(searchTerm) ||
    appointment.branch.name.toLowerCase().includes(searchTerm) ||
    appointment.service.category.toLowerCase().includes(searchTerm) ||
    (appointment.symptoms && appointment.symptoms.toLowerCase().includes(searchTerm)) ||
    (appointment.notes && appointment.notes.toLowerCase().includes(searchTerm))
  )
}

export const filterAppointmentsByStatus = (appointments: AppointmentDetails[], status: AppointmentStatus | 'all'): AppointmentDetails[] => {
  if (status === 'all') {
    return appointments
  }
  return appointments.filter(appointment => appointment.status === status)
}

export const sortAppointments = (appointments: AppointmentDetails[], sortBy: string, sortOrder: 'asc' | 'desc'): AppointmentDetails[] => {
  return [...appointments].sort((a, b) => {
    let aValue: any
    let bValue: any
    
    switch (sortBy) {
      case 'scheduledAt':
        aValue = new Date(a.scheduledAt).getTime()
        bValue = new Date(b.scheduledAt).getTime()
        break
      case 'service':
        aValue = a.service.name
        bValue = b.service.name
        break
      case 'dentist':
        aValue = a.dentist.user.name
        bValue = b.dentist.user.name
        break
      case 'status':
        aValue = a.status
        bValue = b.status
        break
      default:
        return 0
    }
    
    if (aValue < bValue) {
      return sortOrder === 'asc' ? -1 : 1
    }
    if (aValue > bValue) {
      return sortOrder === 'asc' ? 1 : -1
    }
    return 0
  })
}

// Validation utilities
export const validateAppointmentData = (data: Partial<any>): string[] => {
  const errors: string[] = []
  
  if (!data.branchId) {
    errors.push('Branch selection is required')
  }
  
  if (!data.serviceId) {
    errors.push('Service selection is required')
  }
  
  if (!data.dentistId) {
    errors.push('Dentist selection is required')
  }
  
  if (!data.scheduledAt) {
    errors.push('Appointment date and time is required')
  } else {
    const appointmentDate = new Date(data.scheduledAt)
    if (appointmentDate <= new Date()) {
      errors.push('Appointment must be scheduled for a future date and time')
    }
  }
  
  if (!data.duration || data.duration < 15) {
    errors.push('Appointment duration must be at least 15 minutes')
  }
  
  return errors
}
