import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/patients/by-user/[userId] - Get patient profile by user ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ userId: string }> }) => {
  const { userId } = await params
  
  // Users can only access their own patient profile unless they're admin/staff
  if (user.role === UserRole.PATIENT && user.id !== userId) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const patientProfile = await prisma.patientProfile.findUnique({
    where: { userId },
    include: {
      user: {
        select: {
          id: true,
          email: true,
          name: true,
          phone: true,
          image: true,
        },
      },
    },
  })
  
  if (!patientProfile) {
    return NextResponse.json(
      { error: 'Patient profile not found' },
      { status: 404 }
    )
  }
  
  return NextResponse.json(patientProfile)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
