#!/usr/bin/env node

/**
 * Production Calendar Setup Script
 * 
 * This script helps set up Google Calendar integration for production deployment
 * by encoding the service account key file to a base64 environment variable.
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Google Calendar Production Setup\n');

// Check if service account key file exists
const keyFilePath = path.join(__dirname, '../config/secrets/google-service-account-key.json');

if (!fs.existsSync(keyFilePath)) {
  console.error('❌ Service account key file not found at:', keyFilePath);
  console.error('\n📋 Setup Instructions:');
  console.error('1. Download your Google service account key file');
  console.error('2. Place it at: config/secrets/google-service-account-key.json');
  console.error('3. Run this script again');
  process.exit(1);
}

try {
  // Read and validate the service account key
  const keyData = fs.readFileSync(keyFilePath, 'utf8');
  const keyJson = JSON.parse(keyData);
  
  // Validate required fields
  const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
  const missingFields = requiredFields.filter(field => !keyJson[field]);
  
  if (missingFields.length > 0) {
    console.error('❌ Invalid service account key file. Missing fields:', missingFields.join(', '));
    process.exit(1);
  }
  
  if (keyJson.type !== 'service_account') {
    console.error('❌ Invalid service account key file. Expected type "service_account", got:', keyJson.type);
    process.exit(1);
  }
  
  console.log('✅ Service account key file validated');
  console.log('📋 Service Account Details:');
  console.log(`   Project ID: ${keyJson.project_id}`);
  console.log(`   Client Email: ${keyJson.client_email}`);
  console.log(`   Private Key ID: ${keyJson.private_key_id}\n`);
  
  // Encode to base64
  const base64Key = Buffer.from(keyData).toString('base64');
  
  console.log('✅ Service account key encoded successfully\n');
  
  // Display environment variable
  console.log('📋 PRODUCTION ENVIRONMENT VARIABLE:');
  console.log('=' .repeat(80));
  console.log('GOOGLE_SERVICE_ACCOUNT_KEY_BASE64=' + base64Key);
  console.log('=' .repeat(80));
  
  console.log('\n🚀 DEPLOYMENT INSTRUCTIONS:\n');
  
  // Vercel instructions
  console.log('📦 For Vercel deployment:');
  console.log('   1. Run: vercel env add GOOGLE_SERVICE_ACCOUNT_KEY_BASE64');
  console.log('   2. Paste the base64 string above when prompted');
  console.log('   3. Set environment: Production');
  console.log('   4. Also add: GOOGLE_CALENDAR_ID=primary');
  console.log('   5. Also add: TIMEZONE=Asia/Manila\n');
  
  // Netlify instructions
  console.log('🌐 For Netlify deployment:');
  console.log('   1. Go to Site settings > Environment variables');
  console.log('   2. Add GOOGLE_SERVICE_ACCOUNT_KEY_BASE64 with the base64 value');
  console.log('   3. Add GOOGLE_CALENDAR_ID=primary');
  console.log('   4. Add TIMEZONE=Asia/Manila\n');
  
  // Railway instructions
  console.log('🚂 For Railway deployment:');
  console.log('   1. Run: railway variables set GOOGLE_SERVICE_ACCOUNT_KEY_BASE64="<base64-value>"');
  console.log('   2. Run: railway variables set GOOGLE_CALENDAR_ID="primary"');
  console.log('   3. Run: railway variables set TIMEZONE="Asia/Manila"\n');
  
  // Docker instructions
  console.log('🐳 For Docker deployment:');
  console.log('   Add to your docker-compose.yml or Dockerfile:');
  console.log('   environment:');
  console.log('     - GOOGLE_SERVICE_ACCOUNT_KEY_BASE64=' + base64Key.substring(0, 50) + '...');
  console.log('     - GOOGLE_CALENDAR_ID=primary');
  console.log('     - TIMEZONE=Asia/Manila\n');
  
  console.log('⚠️  SECURITY REMINDERS:');
  console.log('   • Never commit the base64 string to version control');
  console.log('   • Use secure environment variable systems');
  console.log('   • Rotate service account keys every 90 days');
  console.log('   • Monitor API usage and quotas');
  console.log('   • Use different service accounts for different environments\n');
  
  console.log('✅ Setup complete! Deploy your application with the environment variables above.');
  
} catch (error) {
  if (error instanceof SyntaxError) {
    console.error('❌ Invalid JSON in service account key file');
  } else {
    console.error('❌ Error processing service account key:', error.message);
  }
  process.exit(1);
}
