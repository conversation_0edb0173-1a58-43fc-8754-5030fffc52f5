import {
  hasRole,
  isPatient,
  isDentist,
  isStaff,
  isAdmin,
  canAccessPatientData,
  canManageAppointments,
  canManageUsers,
  canManageInventory,
  canViewReports,
} from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

describe('Auth Utils', () => {
  describe('hasRole', () => {
    it('should return true if user has allowed role', () => {
      const result = hasRole(UserRole.ADMIN, [UserRole.ADMIN, UserRole.STAFF])
      expect(result).toBe(true)
    })

    it('should return false if user does not have allowed role', () => {
      const result = hasRole(UserRole.PATIENT, [UserRole.ADMIN, UserRole.STAFF])
      expect(result).toBe(false)
    })

    it('should handle single role in array', () => {
      const result = hasRole(UserRole.DENTIST, [UserRole.DENTIST])
      expect(result).toBe(true)
    })

    it('should handle empty allowed roles array', () => {
      const result = hasRole(UserRole.ADMIN, [])
      expect(result).toBe(false)
    })
  })

  describe('Role checking functions', () => {
    it('should correctly identify patient role', () => {
      expect(isPatient(UserRole.PATIENT)).toBe(true)
      expect(isPatient(UserRole.DENTIST)).toBe(false)
      expect(isPatient(UserRole.STAFF)).toBe(false)
      expect(isPatient(UserRole.ADMIN)).toBe(false)
    })

    it('should correctly identify dentist role', () => {
      expect(isDentist(UserRole.DENTIST)).toBe(true)
      expect(isDentist(UserRole.PATIENT)).toBe(false)
      expect(isDentist(UserRole.STAFF)).toBe(false)
      expect(isDentist(UserRole.ADMIN)).toBe(false)
    })

    it('should correctly identify staff role', () => {
      expect(isStaff(UserRole.STAFF)).toBe(true)
      expect(isStaff(UserRole.PATIENT)).toBe(false)
      expect(isStaff(UserRole.DENTIST)).toBe(false)
      expect(isStaff(UserRole.ADMIN)).toBe(false)
    })

    it('should correctly identify admin role', () => {
      expect(isAdmin(UserRole.ADMIN)).toBe(true)
      expect(isAdmin(UserRole.PATIENT)).toBe(false)
      expect(isAdmin(UserRole.DENTIST)).toBe(false)
      expect(isAdmin(UserRole.STAFF)).toBe(false)
    })
  })

  describe('canAccessPatientData', () => {
    it('should allow admin to access any patient data', () => {
      const result = canAccessPatientData(UserRole.ADMIN, 'patient-1', 'admin-user-id')
      expect(result).toBe(true)
    })

    it('should allow staff to access any patient data', () => {
      const result = canAccessPatientData(UserRole.STAFF, 'patient-1', 'staff-user-id')
      expect(result).toBe(true)
    })

    it('should allow dentist to access patient data', () => {
      const result = canAccessPatientData(UserRole.DENTIST, 'patient-1', 'dentist-user-id')
      expect(result).toBe(true)
    })

    it('should allow patient to access their own data', () => {
      const result = canAccessPatientData(UserRole.PATIENT, 'patient-1', 'patient-1')
      expect(result).toBe(true)
    })

    it('should not allow patient to access other patient data', () => {
      const result = canAccessPatientData(UserRole.PATIENT, 'patient-1', 'patient-2')
      expect(result).toBe(false)
    })

    it('should return false for patient without patientId', () => {
      const result = canAccessPatientData(UserRole.PATIENT, undefined, 'patient-1')
      expect(result).toBe(false)
    })

    it('should return false for patient without userId', () => {
      const result = canAccessPatientData(UserRole.PATIENT, 'patient-1', undefined)
      expect(result).toBe(false)
    })
  })

  describe('canManageAppointments', () => {
    it('should allow admin to manage appointments', () => {
      const result = canManageAppointments(UserRole.ADMIN)
      expect(result).toBe(true)
    })

    it('should allow staff to manage appointments', () => {
      const result = canManageAppointments(UserRole.STAFF)
      expect(result).toBe(true)
    })

    it('should allow dentist to manage appointments', () => {
      const result = canManageAppointments(UserRole.DENTIST)
      expect(result).toBe(true)
    })

    it('should not allow patient to manage appointments', () => {
      const result = canManageAppointments(UserRole.PATIENT)
      expect(result).toBe(false)
    })
  })

  describe('canManageUsers', () => {
    it('should allow admin to manage users', () => {
      const result = canManageUsers(UserRole.ADMIN)
      expect(result).toBe(true)
    })

    it('should not allow staff to manage users', () => {
      const result = canManageUsers(UserRole.STAFF)
      expect(result).toBe(false)
    })

    it('should not allow dentist to manage users', () => {
      const result = canManageUsers(UserRole.DENTIST)
      expect(result).toBe(false)
    })

    it('should not allow patient to manage users', () => {
      const result = canManageUsers(UserRole.PATIENT)
      expect(result).toBe(false)
    })
  })

  describe('canManageInventory', () => {
    it('should allow admin to manage inventory', () => {
      const result = canManageInventory(UserRole.ADMIN)
      expect(result).toBe(true)
    })

    it('should allow staff to manage inventory', () => {
      const result = canManageInventory(UserRole.STAFF)
      expect(result).toBe(true)
    })

    it('should not allow dentist to manage inventory', () => {
      const result = canManageInventory(UserRole.DENTIST)
      expect(result).toBe(false)
    })

    it('should not allow patient to manage inventory', () => {
      const result = canManageInventory(UserRole.PATIENT)
      expect(result).toBe(false)
    })
  })

  describe('canViewReports', () => {
    it('should allow admin to view reports', () => {
      const result = canViewReports(UserRole.ADMIN)
      expect(result).toBe(true)
    })

    it('should allow dentist to view reports', () => {
      const result = canViewReports(UserRole.DENTIST)
      expect(result).toBe(true)
    })

    it('should not allow staff to view reports', () => {
      const result = canViewReports(UserRole.STAFF)
      expect(result).toBe(false)
    })

    it('should not allow patient to view reports', () => {
      const result = canViewReports(UserRole.PATIENT)
      expect(result).toBe(false)
    })
  })
})
