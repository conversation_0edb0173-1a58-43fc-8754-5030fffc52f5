import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Award, 
  Users, 
  Clock, 
  Heart, 
  GraduationCap, 
  Shield,
  Star,
  CheckCircle
} from "lucide-react"

const stats = [
  {
    icon: Users,
    number: "5,000+",
    label: "Happy Patients",
    description: "Trusted by families across the community"
  },
  {
    icon: Clock,
    number: "15+",
    label: "Years Experience",
    description: "Providing exceptional dental care"
  },
  {
    icon: Award,
    number: "50+",
    label: "Awards Won",
    description: "Recognition for excellence in dentistry"
  },
  {
    icon: Star,
    number: "4.9/5",
    label: "Patient Rating",
    description: "Based on 500+ verified reviews"
  }
]

const credentials = [
  "Doctor of Dental Surgery (DDS) - Harvard School of Dental Medicine",
  "Board Certified in General Dentistry",
  "Member of American Dental Association (ADA)",
  "Certified in Advanced Cosmetic Dentistry",
  "Continuing Education in Latest Dental Technologies"
]

const values = [
  {
    icon: Heart,
    title: "Patient-Centered Care",
    description: "Your comfort and well-being are our top priorities. We listen to your concerns and tailor treatments to your needs."
  },
  {
    icon: Shield,
    title: "Advanced Technology",
    description: "We use the latest dental technology and techniques to provide the most effective and comfortable treatments."
  },
  {
    icon: GraduationCap,
    title: "Continuous Learning",
    description: "Our team stays current with the latest advances in dentistry through ongoing education and training."
  }
]

export function AboutSection() {
  return (
    <section id="about" className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Heart className="h-4 w-4" />
            <span>About Us</span>
          </div>
          
          <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-4">
            Meet Dr. Sarah Johnson
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            With over 15 years of experience, Dr. Johnson is dedicated to providing exceptional 
            dental care in a comfortable, welcoming environment.
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Doctor Image */}
          <div className="relative">
            <div className="bg-gradient-to-br from-blue-50 to-blue-25 rounded-2xl p-8">
              {/* Placeholder for doctor image */}
              <div className="aspect-[3/4] bg-white rounded-xl shadow-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">👩‍⚕️</span>
                  </div>
                  <p className="text-gray-500">Dr. Sarah Johnson</p>
                  <p className="text-sm text-gray-400 mt-2">Professional Photo Placeholder</p>
                </div>
              </div>
            </div>
            
            {/* Floating credential badge */}
            <div className="absolute -bottom-6 -right-6 bg-white rounded-lg shadow-lg p-4 border">
              <div className="flex items-center space-x-2">
                <Award className="h-6 w-6 text-blue-600" />
                <div>
                  <p className="font-semibold text-sm">ADA Certified</p>
                  <p className="text-xs text-gray-600">Board Certified Dentist</p>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-6">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Passionate About Your Oral Health
              </h3>
              <p className="text-gray-600 leading-relaxed mb-4">
                Dr. Sarah Johnson graduated summa cum laude from Harvard School of Dental Medicine 
                and has been serving the community for over 15 years. She believes in creating a 
                comfortable, anxiety-free environment where patients feel heard and cared for.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Her approach combines the latest dental technology with a gentle, personalized touch. 
                Dr. Johnson is committed to helping each patient achieve optimal oral health and a 
                confident smile.
              </p>
            </div>

            {/* Credentials */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Education & Credentials</h4>
              <ul className="space-y-2">
                {credentials.map((credential, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-gray-600 text-sm">{credential}</span>
                  </li>
                ))}
              </ul>
            </div>

            <Button size="lg" className="bg-primary hover:bg-primary/90">
              Schedule a Consultation
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent className="space-y-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <IconComponent className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <div className="text-2xl lg:text-3xl font-bold text-gray-900">
                      {stat.number}
                    </div>
                    <div className="text-sm font-semibold text-gray-700">
                      {stat.label}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {stat.description}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Values */}
        <div>
          <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 text-center mb-12">
            Our Core Values
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-3">
                    {value.title}
                  </h4>
                  <p className="text-gray-600 leading-relaxed">
                    {value.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
