import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { invoiceCreateSchema, paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole, PaymentStatus } from '@prisma/client'

// Generate invoice number
function generateInvoiceNumber(): string {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `INV-${year}${month}${day}-${random}`
}

// GET /api/invoices - List invoices with pagination and filtering
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const patientId = searchParams.get('patientId')
  const status = searchParams.get('status') as PaymentStatus | null
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')
  
  const skip = (page - 1) * limit
  
  // Build where clause based on user role and filters
  const where: Record<string, unknown> = {}
  
  // Role-based filtering
  if (user.role === UserRole.PATIENT) {
    // Patients can only see their own invoices
    const patientProfile = await prisma.patientProfile.findUnique({
      where: { userId: user.id },
    })
    if (!patientProfile) {
      return NextResponse.json({ invoices: [], pagination: { page, limit, total: 0, pages: 0 } })
    }
    where.patientId = patientProfile.id
  }
  
  // Additional filters
  if (patientId) {
    // Check if user can access this patient's data
    if (!canAccessPatientData(user.role, patientId, user.id)) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }
    where.patientId = patientId
  }
  if (status) where.status = status
  
  // Date range filter
  if (dateFrom || dateTo) {
    where.issuedAt = {}
    if (dateFrom) (where.issuedAt as any).gte = new Date(dateFrom)
    if (dateTo) (where.issuedAt as any).lte = new Date(dateTo)
  }
  
  // Search filter
  if (query) {
    where.OR = [
      { invoiceNumber: { contains: query, mode: 'insensitive' } },
      { notes: { contains: query, mode: 'insensitive' } },
      { patient: { user: { name: { contains: query, mode: 'insensitive' } } } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'issuedAt' ? { issuedAt: sortOrder } :
                  sortBy === 'dueDate' ? { dueDate: sortOrder } :
                  sortBy === 'totalAmount' ? { totalAmount: sortOrder } :
                  { createdAt: sortOrder as 'asc' | 'desc' }

  const [invoices, total] = await Promise.all([
    prisma.invoice.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
              },
            },
          },
        },
        items: {
          include: {
            treatment: {
              include: {
                service: { select: { name: true, category: true } },
                dentist: { 
                  include: { 
                    user: { select: { name: true } } 
                  } 
                },
              },
            },
          },
        },
        payments: {
          orderBy: { createdAt: 'desc' },
          take: 3, // Latest 3 payments
        },
        _count: {
          select: {
            payments: true,
          },
        },
      },
    }),
    prisma.invoice.count({ where }),
  ])
  
  return NextResponse.json({
    invoices,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/invoices - Create new invoice
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = invoiceCreateSchema.parse(body)
  
  // Check access permissions
  if (!canAccessPatientData(user.role, validatedData.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Verify patient exists
  const patient = await prisma.patientProfile.findUnique({
    where: { id: validatedData.patientId },
    include: { user: true },
  })
  
  if (!patient) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Calculate total amount
  const totalAmount = validatedData.items.reduce((sum, item) => {
    return sum + (item.quantity * item.unitPrice)
  }, 0)
  
  // Generate unique invoice number
  let invoiceNumber: string
  let isUnique = false
  let attempts = 0
  
  do {
    invoiceNumber = generateInvoiceNumber()
    const existing = await prisma.invoice.findUnique({
      where: { invoiceNumber },
    })
    isUnique = !existing
    attempts++
  } while (!isUnique && attempts < 10)
  
  if (!isUnique) {
    return NextResponse.json(
      { error: 'Failed to generate unique invoice number' },
      { status: 500 }
    )
  }
  
  // Create invoice with items
  const invoice = await prisma.invoice.create({
    data: {
      patientId: validatedData.patientId,
      invoiceNumber,
      totalAmount,
      dueDate: validatedData.dueDate,
      notes: validatedData.notes,
      items: {
        create: validatedData.items.map(item => ({
          treatmentId: item.treatmentId,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.quantity * item.unitPrice,
        })),
      },
    },
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
      },
      items: {
        include: {
          treatment: {
            include: {
              service: { select: { name: true, category: true } },
              dentist: { 
                include: { 
                  user: { select: { name: true } } 
                } 
              },
            },
          },
        },
      },
    },
  })
  
  // TODO: Send invoice notification to patient
  
  return NextResponse.json(invoice, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF])
