'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import {
  Calendar,
  Users,
  Clock,
  Stethoscope,
  CreditCard,
  BarChart3,
  Settings,
  Home,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  Pill,
  Activity,
  ClipboardList,
  LogOut
} from 'lucide-react'

interface SidebarItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  description?: string
  badge?: string
}

interface SidebarSection {
  title: string
  items: SidebarItem[]
}

const sidebarSections: SidebarSection[] = [
  {
    title: 'Overview',
    items: [
      {
        title: 'Dashboard',
        href: '/dentist/dashboard',
        icon: Home,
        description: 'Overview and quick actions'
      }
    ]
  },
  {
    title: 'Patient Management',
    items: [
      {
        title: 'Appointments',
        href: '/dentist/appointments',
        icon: Calendar,
        description: 'View and manage appointments'
      },
      {
        title: 'Patient Records',
        href: '/dentist/patients',
        icon: Users,
        description: 'Patient profiles and history'
      },
      {
        title: 'Schedule',
        href: '/dentist/schedule',
        icon: Clock,
        description: 'Manage your availability'
      }
    ]
  },
  {
    title: 'Clinical Tools',
    items: [
      {
        title: 'Treatment Plans',
        href: '/dentist/treatments',
        icon: Stethoscope,
        description: 'Create and manage treatment plans'
      },
      {
        title: 'Dental Charts',
        href: '/dentist/charts',
        icon: Activity,
        description: 'Interactive dental charting'
      },
      {
        title: 'Prescriptions',
        href: '/dentist/prescriptions',
        icon: Pill,
        description: 'Digital prescription management'
      },
      {
        title: 'Treatment Notes',
        href: '/dentist/notes',
        icon: ClipboardList,
        description: 'SOAP format treatment logging'
      }
    ]
  },
  {
    title: 'Business',
    items: [
      {
        title: 'Billing',
        href: '/dentist/billing',
        icon: CreditCard,
        description: 'Payment tracking and invoices'
      },
      {
        title: 'Reports',
        href: '/dentist/reports',
        icon: BarChart3,
        description: 'Analytics and performance'
      }
    ]
  },
  {
    title: 'Account',
    items: [
      {
        title: 'Profile Settings',
        href: '/dentist/settings',
        icon: Settings,
        description: 'Account and preferences'
      }
    ]
  }
]

interface DentistSidebarProps {
  className?: string
}

export function DentistSidebar({ className }: DentistSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const pathname = usePathname()
  const { user, logout } = useAuth()

  const isActiveLink = (href: string) => {
    if (href === '/dentist/dashboard') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-blue-100">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Stethoscope className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="font-semibold text-gray-900">Dentist Portal</h2>
                <p className="text-xs text-gray-600">Dr. {user?.name?.split(' ')[0] || 'Doctor'}</p>
              </div>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="hidden lg:flex h-8 w-8 p-0 hover:bg-blue-50"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-6">
          {sidebarSections.map((section) => (
            <div key={section.title} className="px-4">
              {!isCollapsed && (
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                  {section.title}
                </h3>
              )}
              <div className="space-y-1">
                {section.items.map((item) => {
                  const Icon = item.icon
                  const isActive = isActiveLink(item.href)
                  
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={() => setIsMobileOpen(false)}
                      className={cn(
                        'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 group',
                        isActive
                          ? 'bg-blue-100 text-blue-700 shadow-sm'
                          : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600',
                        isCollapsed && 'justify-center px-2'
                      )}
                    >
                      <Icon className={cn(
                        'flex-shrink-0',
                        isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-blue-500',
                        isCollapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3'
                      )} />
                      {!isCollapsed && (
                        <div className="flex-1 min-w-0">
                          <div className="truncate">{item.title}</div>
                          {item.description && (
                            <div className="text-xs text-gray-500 truncate">
                              {item.description}
                            </div>
                          )}
                        </div>
                      )}
                      {!isCollapsed && item.badge && (
                        <span className="ml-auto bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  )
                })}
              </div>
              {!isCollapsed && <Separator className="mt-4" />}
            </div>
          ))}
        </nav>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-blue-100">
        <Button
          variant="ghost"
          onClick={handleLogout}
          className={cn(
            'w-full justify-start text-gray-700 hover:text-red-600 hover:bg-red-50',
            isCollapsed && 'justify-center px-2'
          )}
        >
          <LogOut className={cn('flex-shrink-0', isCollapsed ? 'h-5 w-5' : 'h-4 w-4 mr-3')} />
          {!isCollapsed && 'Sign Out'}
        </Button>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile Sidebar */}
      <div className="lg:hidden">
        {/* Mobile Toggle Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMobileOpen(true)}
          className="fixed top-4 left-4 z-50 bg-white shadow-md hover:bg-blue-50"
        >
          <Menu className="h-5 w-5" />
        </Button>

        {/* Mobile Overlay */}
        {isMobileOpen && (
          <div className="fixed inset-0 z-50 lg:hidden">
            <div className="fixed inset-0 bg-black/20" onClick={() => setIsMobileOpen(false)} />
            <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl">
              <div className="flex items-center justify-between p-4 border-b">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <Stethoscope className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h2 className="font-semibold text-gray-900">Dentist Portal</h2>
                    <p className="text-xs text-gray-600">Dr. {user?.name?.split(' ')[0] || 'Doctor'}</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsMobileOpen(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="h-[calc(100%-80px)]">
                <SidebarContent />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Desktop Sidebar */}
      <div className={cn(
        'hidden lg:flex flex-col bg-white border-r border-blue-100 transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-80',
        className
      )}>
        <SidebarContent />
      </div>
    </>
  )
}
