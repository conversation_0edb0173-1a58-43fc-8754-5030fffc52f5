'use client'

import { useState } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertTriangle, X } from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'

interface CancelAppointmentModalProps {
  isOpen: boolean
  onClose: () => void
  appointment: AppointmentDetails | null
  onSuccess: () => void
}

export function CancelAppointmentModal({ 
  isOpen, 
  onClose, 
  appointment, 
  onSuccess 
}: CancelAppointmentModalProps) {
  const [cancelReason, setCancelReason] = useState('')
  const [isCancelling, setIsCancelling] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleCancel = async () => {
    if (!appointment) return

    try {
      setIsCancelling(true)
      setError(null)

      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'CANCELLED',
          cancelReason: cancelReason.trim() || undefined,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to cancel appointment')
      }

      onSuccess()
      onClose()
      setCancelReason('')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel appointment')
    } finally {
      setIsCancelling(false)
    }
  }

  const handleClose = () => {
    if (!isCancelling) {
      onClose()
      setCancelReason('')
      setError(null)
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    })
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Manila'
    })
  }

  if (!appointment) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center text-red-600">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Cancel Appointment
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to cancel this appointment? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Appointment Details */}
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <h4 className="font-medium text-gray-900">{appointment.service.name}</h4>
            <p className="text-sm text-gray-600">
              Dr. {appointment.dentist.user.name}
            </p>
            <p className="text-sm text-gray-600">
              {formatDate(new Date(appointment.scheduledAt))} at {formatTime(new Date(appointment.scheduledAt))}
            </p>
            <p className="text-sm text-gray-600">
              {appointment.branch.name}
            </p>
          </div>

          {/* Cancellation Policy Notice */}
          <Alert className="bg-yellow-50 border-yellow-200">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-yellow-800">
              <strong>Cancellation Policy:</strong> Please cancel at least 24 hours in advance to avoid any cancellation fees. 
              For urgent cancellations, please call our clinic directly.
            </AlertDescription>
          </Alert>

          {/* Cancel Reason */}
          <div className="space-y-2">
            <Label htmlFor="cancelReason">Reason for cancellation (optional)</Label>
            <Textarea
              id="cancelReason"
              placeholder="Please let us know why you're cancelling this appointment..."
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
              className="min-h-[80px] resize-none"
              disabled={isCancelling}
            />
            <p className="text-xs text-gray-500">
              This helps us improve our service and may be useful for rescheduling.
            </p>
          </div>

          {/* Error */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button 
              variant="outline" 
              onClick={handleClose} 
              disabled={isCancelling}
            >
              Keep Appointment
            </Button>
            <Button
              onClick={handleCancel}
              disabled={isCancelling}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {isCancelling ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cancelling...
                </>
              ) : (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Cancel Appointment
                </>
              )}
            </Button>
          </div>

          {/* Contact Information */}
          <div className="text-center text-sm text-gray-600 pt-4 border-t">
            <p>Need to speak with someone?</p>
            <p className="font-medium">Call us at: (02) 123-4567</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
