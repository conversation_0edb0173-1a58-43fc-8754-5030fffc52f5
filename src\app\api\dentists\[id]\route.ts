import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { dentistProfileUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/dentists/[id] - Get dentist profile by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const dentistProfile = await prisma.dentistProfile.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          image: true,
          status: true,
          createdAt: true,
        },
      },
      branches: {
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
              phone: true,
              email: true,
            },
          },
        },
      },
      schedules: {
        where: { isActive: true },
        orderBy: { dayOfWeek: 'asc' },
      },
      appointments: {
        where: {
          scheduledAt: {
            gte: new Date(),
          },
        },
        orderBy: { scheduledAt: 'asc' },
        take: 10,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                  phone: true,
                },
              },
            },
          },
          service: {
            select: {
              name: true,
              category: true,
              duration: true,
            },
          },
          branch: {
            select: {
              name: true,
            },
          },
        },
      },
      treatments: {
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          service: {
            select: {
              name: true,
              category: true,
            },
          },
        },
      },
      _count: {
        select: {
          appointments: true,
          treatments: true,
        },
      },
    },
  })
  
  if (!dentistProfile) {
    return NextResponse.json(
      { error: 'Dentist not found' },
      { status: 404 }
    )
  }
  
  // Dentists can only access their own profile unless user is admin/staff
  if (user.role === UserRole.DENTIST && dentistProfile.userId !== user.id) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  return NextResponse.json(dentistProfile)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/dentists/[id] - Update dentist profile
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  // Check if dentist exists
  const existingProfile = await prisma.dentistProfile.findUnique({
    where: { id },
    include: { user: true },
  })
  
  if (!existingProfile) {
    return NextResponse.json(
      { error: 'Dentist not found' },
      { status: 404 }
    )
  }
  
  // Dentists can only update their own profile unless user is admin/staff
  if (user.role === UserRole.DENTIST && existingProfile.userId !== user.id) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const validatedData = dentistProfileUpdateSchema.parse(body)
  
  // Check if license number is unique (if being updated)
  if (validatedData.licenseNumber && validatedData.licenseNumber !== existingProfile.licenseNumber) {
    const existingLicense = await prisma.dentistProfile.findUnique({
      where: { licenseNumber: validatedData.licenseNumber },
    })
    
    if (existingLicense) {
      return NextResponse.json(
        { error: 'License number already exists' },
        { status: 400 }
      )
    }
  }
  
  const updatedProfile = await prisma.dentistProfile.update({
    where: { id },
    data: validatedData,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          image: true,
        },
      },
      branches: {
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
            },
          },
        },
      },
      schedules: {
        where: { isActive: true },
        orderBy: { dayOfWeek: 'asc' },
      },
    },
  })
  
  return NextResponse.json(updatedProfile)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])

// DELETE /api/dentists/[id] - Delete dentist profile (Admin only)
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const dentistProfile = await prisma.dentistProfile.findUnique({
    where: { id },
    include: { user: true },
  })
  
  if (!dentistProfile) {
    return NextResponse.json(
      { error: 'Dentist not found' },
      { status: 404 }
    )
  }
  
  // Check if dentist has active appointments
  const activeAppointments = await prisma.appointment.count({
    where: {
      dentistId: id,
      scheduledAt: {
        gte: new Date(),
      },
      status: {
        in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'],
      },
    },
  })
  
  if (activeAppointments > 0) {
    return NextResponse.json(
      { error: 'Cannot delete dentist with active appointments' },
      { status: 400 }
    )
  }
  
  // Delete the entire user account (cascade will handle profile deletion)
  await prisma.user.delete({
    where: { id: dentistProfile.userId },
  })
  
  return NextResponse.json({ message: 'Dentist deleted successfully' })
}, [UserRole.ADMIN])
