"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Menu, X, Phone, MapPin, Clock, User, UserCog, LogOut, Calendar } from "lucide-react"
import { useAuth } from "@/hooks/useAuth"
import { UserRole } from "@prisma/client"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const { user, isAuthenticated, logout } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  // Navigation helper functions
  const handleBookAppointment = () => {
    if (!isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/appointments/book')
    } else {
      router.push('/appointments/book')
    }
  }

  const handleDashboardNavigation = () => {
    if (user?.role === UserRole.PATIENT) {
      router.push('/dashboard')
    } else if (user?.role === UserRole.ADMIN || user?.role === UserRole.STAFF) {
      router.push('/admin/dashboard')
    } else if (user?.role === UserRole.DENTIST) {
      router.push('/dentist/dashboard')
    } else {
      router.push('/dashboard')
    }
  }

  const isActivePage = (href: string) => {
    if (href === '/' || href === '#home') {
      return pathname === '/'
    }
    return pathname.startsWith(href.replace('#', '/'))
  }

  useEffect(() => {
    const controlNavbar = () => {
      const currentScrollY = window.scrollY

      if (currentScrollY < 10) {
        // Always show navbar at the top
        setIsVisible(true)
      } else if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down and past 100px - hide navbar
        setIsVisible(false)
      } else if (currentScrollY < lastScrollY) {
        // Scrolling up - show navbar
        setIsVisible(true)
      }

      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', controlNavbar)
    return () => window.removeEventListener('scroll', controlNavbar)
  }, [lastScrollY])

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 transition-transform duration-300 ease-in-out ${
      isVisible ? 'translate-y-0' : '-translate-y-full'
    }`}>
      {/* Top bar with contact info */}
      <div className="bg-gradient-to-r from-blue-50 to-green-50 py-2 border-b border-blue-100">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center md:justify-between items-center text-sm text-blue-700">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 hover:text-blue-800 transition-colors cursor-pointer">
                <Phone className="h-4 w-4" />
                <span className="font-medium">(*************</span>
              </div>
              <div className="flex items-center space-x-2 hover:text-blue-800 transition-colors cursor-pointer">
                <MapPin className="h-4 w-4" />
                <span className="font-medium">123 Dental St, City, State 12345</span>
              </div>
              <div className="flex items-center space-x-2 hover:text-blue-800 transition-colors">
                <Clock className="h-4 w-4" />
                <span className="font-medium">Mon-Fri: 8AM-6PM</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main navigation */}
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 cursor-pointer group">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-200 group-hover:scale-105">
              <span className="text-white font-bold text-xl">DC</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">Bright Smile Dental</h1>
              <p className="text-sm text-gray-600 font-medium">Your Trusted Dental Care</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className={`transition-all duration-200 cursor-pointer px-2 py-1 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isActivePage('/') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
              }`}
            >
              Home
            </Link>
            <a
              href="#services"
              className={`transition-all duration-200 cursor-pointer px-2 py-1 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isActivePage('#services') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
              }`}
            >
              Services
            </a>
            <a
              href="#about"
              className={`transition-all duration-200 cursor-pointer px-2 py-1 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isActivePage('#about') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
              }`}
            >
              About
            </a>
            <a
              href="#testimonials"
              className={`transition-all duration-200 cursor-pointer px-2 py-1 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isActivePage('#testimonials') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
              }`}
            >
              Testimonials
            </a>
            <a
              href="#contact"
              className={`transition-all duration-200 cursor-pointer px-2 py-1 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isActivePage('#contact') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
              }`}
            >
              Contact
            </a>
            {isAuthenticated && user?.role === UserRole.PATIENT && (
              <Link
                href="/dashboard"
                className={`transition-all duration-200 cursor-pointer px-2 py-1 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActivePage('/dashboard') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
                }`}
              >
                My Account
              </Link>
            )}
          </nav>

          {/* Login Buttons / User Menu */}
          <div className="hidden md:flex items-center space-x-3">
            {isAuthenticated ? (
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{user?.name}</p>
                    <p className="text-xs text-gray-600 capitalize">{user?.role?.toLowerCase()}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => logout()}
                  className="flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </Button>
                {user?.role === UserRole.PATIENT ? (
                  <Button
                    size="lg"
                    className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                    onClick={handleBookAppointment}
                  >
                    <Calendar className="h-4 w-4" />
                    <span>Book Appointment</span>
                  </Button>
                ) : (
                  <Button
                    size="lg"
                    className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                    onClick={handleDashboardNavigation}
                  >
                    <UserCog className="h-4 w-4" />
                    <span>Dashboard</span>
                  </Button>
                )}
              </div>
            ) : (
              <>
                <PatientLoginDialog />
                <AdminLoginDialog />
                <Button
                  size="lg"
                  className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                  onClick={handleBookAppointment}
                >
                  <Calendar className="h-4 w-4" />
                  <span>Book Appointment</span>
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2 rounded-md hover:bg-gray-100 transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 active:scale-95"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t bg-white">
            <nav className="flex flex-col space-y-2">
              <Link
                href="/"
                className={`transition-all duration-200 cursor-pointer px-3 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActivePage('/') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              <a
                href="#services"
                className={`transition-all duration-200 cursor-pointer px-3 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActivePage('#services') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Services
              </a>
              <a
                href="#about"
                className={`transition-all duration-200 cursor-pointer px-3 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActivePage('#about') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                About
              </a>
              <a
                href="#testimonials"
                className={`transition-all duration-200 cursor-pointer px-3 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActivePage('#testimonials') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Testimonials
              </a>
              <a
                href="#contact"
                className={`transition-all duration-200 cursor-pointer px-3 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActivePage('#contact') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                Contact
              </a>
              {isAuthenticated && user?.role === UserRole.PATIENT && (
                <Link
                  href="/dashboard"
                  className={`transition-all duration-200 cursor-pointer px-3 py-2 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    isActivePage('/dashboard') ? 'text-blue-600 bg-blue-50 font-medium' : 'text-gray-700 hover:text-blue-600'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  My Account
                </Link>
              )}
              <div className="flex flex-col space-y-2 pt-4">
                {isAuthenticated ? (
                  <>
                    <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="text-sm">
                        <p className="font-medium text-gray-900">{user?.name}</p>
                        <p className="text-xs text-gray-600 capitalize">{user?.role?.toLowerCase()}</p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => logout()}
                      className="flex items-center space-x-2"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Logout</span>
                    </Button>
                    {user?.role === UserRole.PATIENT ? (
                      <Button
                        size="lg"
                        className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                        onClick={() => {
                          handleBookAppointment()
                          setIsMenuOpen(false)
                        }}
                      >
                        <Calendar className="h-4 w-4" />
                        <span>Book Appointment</span>
                      </Button>
                    ) : (
                      <Button
                        size="lg"
                        className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                        onClick={() => {
                          handleDashboardNavigation()
                          setIsMenuOpen(false)
                        }}
                      >
                        <UserCog className="h-4 w-4" />
                        <span>Dashboard</span>
                      </Button>
                    )}
                  </>
                ) : (
                  <>
                    <PatientLoginDialog />
                    <AdminLoginDialog />
                    <Button
                      size="lg"
                      className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
                      onClick={() => {
                        handleBookAppointment()
                        setIsMenuOpen(false)
                      }}
                    >
                      <Calendar className="h-4 w-4" />
                      <span>Book Appointment</span>
                    </Button>
                  </>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

function PatientLoginDialog() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const { login, loginWithGoogle, isLoading } = useAuth()

  const handleGoogleSignIn = async () => {
    try {
      setError('')
      await loginWithGoogle()
      setIsOpen(false)
    } catch (err) {
      console.error('Google sign-in error:', err)
      setError('Failed to sign in with Google. Please try again.')
    }
  }

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setError('')
      await login(email, password)
      setIsOpen(false)
      setEmail('')
      setPassword('')
    } catch (err) {
      console.error('Email sign-in error:', err)
      setError('Invalid email or password. Please try again.')
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center space-x-2 shadow-sm hover:shadow-md">
          <User className="h-4 w-4" />
          <span>Patient Login</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center pb-2">
          <DialogTitle className="flex items-center justify-center space-x-2 text-xl">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-blue-600" />
            </div>
            <span>Welcome Back</span>
          </DialogTitle>
          <p className="text-sm text-gray-600 mt-2">Sign in to access your patient portal</p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {/* Google Sign In Button */}
          <Button
            variant="outline"
            className="w-full h-12 border-2 hover:bg-gray-50 relative"
            onClick={handleGoogleSignIn}
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span>Signing in...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span className="font-medium">Continue with Google</span>
              </div>
            )}
          </Button>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-gray-500 font-medium">Or continue with email</span>
            </div>
          </div>

          {/* Email/Password Form */}
          <form onSubmit={handleEmailSignIn} className="space-y-4">
            <div>
              <label htmlFor="patient-email" className="block text-sm font-semibold text-gray-700 mb-2">
                Email Address
              </label>
              <Input
                id="patient-email"
                type="email"
                placeholder="Enter your email address"
                className="transition-all duration-200"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <label htmlFor="patient-password" className="block text-sm font-semibold text-gray-700 mb-2">
                Password
              </label>
              <Input
                id="patient-password"
                type="password"
                placeholder="Enter your password"
                className="transition-all duration-200"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                <span className="text-sm text-gray-600">Remember me</span>
              </label>
              <a href="#" className="text-sm text-blue-600 hover:text-blue-700 font-medium hover:underline">
                Forgot password?
              </a>
            </div>

            <div className="flex flex-col space-y-3 pt-2">
              <Button
                type="submit"
                className="w-full h-11 font-semibold"
                disabled={isLoading}
              >
                {isLoading ? 'Signing In...' : 'Sign In'}
              </Button>
              <Button
                type="button"
                variant="outline"
                className="w-full h-11 font-semibold"
              >
                Create New Account
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function AdminLoginDialog() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const { login, isLoading } = useAuth()

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setError('')
      await login(email, password)
      setIsOpen(false)
      setEmail('')
      setPassword('')
    } catch (err) {
      console.error('Staff sign-in error:', err)
      setError('Invalid email or password. Please try again.')
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center space-x-2 shadow-sm hover:shadow-md">
          <UserCog className="h-4 w-4" />
          <span>Staff Login</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center pb-2">
          <DialogTitle className="flex items-center justify-center space-x-2 text-xl">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <UserCog className="h-4 w-4 text-green-600" />
            </div>
            <span>Staff Portal</span>
          </DialogTitle>
          <p className="text-sm text-gray-600 mt-2">Secure access for healthcare professionals</p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-green-800">Secure Staff Access</span>
            </div>
            <p className="text-xs text-green-700 mt-1">This portal is restricted to authorized healthcare staff only.</p>
          </div>

          <form onSubmit={handleSignIn} className="space-y-4">
            <div>
              <label htmlFor="admin-email" className="block text-sm font-semibold text-gray-700 mb-2">
                Staff Email Address
              </label>
              <Input
                id="admin-email"
                type="email"
                placeholder="Enter your staff email address"
                className="transition-all duration-200"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <label htmlFor="admin-password" className="block text-sm font-semibold text-gray-700 mb-2">
                Password
              </label>
              <Input
                id="admin-password"
                type="password"
                placeholder="Enter your secure password"
                className="transition-all duration-200"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input type="checkbox" className="rounded border-gray-300 text-green-600 focus:ring-green-500" />
                <span className="text-sm text-gray-600">Keep me signed in</span>
              </label>
              <a href="#" className="text-sm text-green-600 hover:text-green-700 font-medium hover:underline">
                Need help?
              </a>
            </div>

            <Button
              type="submit"
              className="w-full h-11 font-semibold bg-green-600 hover:bg-green-700 focus:ring-green-500"
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Signing in...</span>
                </div>
              ) : (
                'Access Staff Portal'
              )}
            </Button>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
