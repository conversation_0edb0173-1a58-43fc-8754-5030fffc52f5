import { google } from 'googleapis'
import { AppointmentDetails } from '@/types/appointment'
import { logGoogleCalendarStatus } from './google-calendar-test'

// Google Calendar service configuration
const SCOPES = ['https://www.googleapis.com/auth/calendar']

// Check if Google Calendar is properly configured
function isGoogleCalendarConfigured(): boolean {
  return !!(
    process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE ||
    (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET)
  )
}

interface CalendarEvent {
  id?: string
  summary: string
  description?: string
  start: {
    dateTime: string
    timeZone: string
  }
  end: {
    dateTime: string
    timeZone: string
  }
  location?: string
  attendees?: Array<{
    email: string
    displayName?: string
  }>
  reminders?: {
    useDefault: boolean
    overrides?: Array<{
      method: 'email' | 'popup'
      minutes: number
    }>
  }
}

class GoogleCalendarService {
  private auth: any
  private calendar: any

  constructor() {
    // Log configuration status for debugging
    logGoogleCalendarStatus()
    this.initializeAuth()
  }

  private initializeAuth() {
    try {
      // Try different authentication methods in order of preference
      if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY_BASE64) {
        // Production: Use base64 encoded service account key from environment variable
        console.log('🔐 Using base64 encoded service account key from environment variable')
        const keyData = JSON.parse(Buffer.from(process.env.GOOGLE_SERVICE_ACCOUNT_KEY_BASE64, 'base64').toString())

        this.auth = new google.auth.GoogleAuth({
          credentials: keyData,
          scopes: SCOPES,
        })
      } else if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE) {
        // Development: Use service account key file
        console.log('🔐 Using service account key file:', process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE)
        this.auth = new google.auth.GoogleAuth({
          keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE,
          scopes: SCOPES,
        })
      } else if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
        // Fallback: Use OAuth2 with client credentials (limited functionality)
        console.warn('⚠️ Using OAuth2 fallback - limited calendar functionality')
        this.auth = new google.auth.OAuth2(
          process.env.GOOGLE_CLIENT_ID,
          process.env.GOOGLE_CLIENT_SECRET,
          'http://localhost:3000/api/auth/callback/google'
        )

        console.warn('Google Calendar: Using OAuth2 without stored tokens. Calendar integration may not work without proper token management.')
      } else {
        throw new Error('No Google Calendar authentication credentials found')
      }

      this.calendar = google.calendar({ version: 'v3', auth: this.auth })
      console.log('✅ Google Calendar authentication initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Google Calendar auth:', error)
      // Don't throw error to prevent breaking appointment creation
    }
  }

  // Create a calendar event for an appointment
  async createAppointmentEvent(appointment: AppointmentDetails): Promise<string | null> {
    try {
      if (!isGoogleCalendarConfigured()) {
        console.warn('Google Calendar not configured - skipping calendar event creation')
        return null
      }

      if (!this.calendar) {
        throw new Error('Google Calendar not initialized')
      }

      const event = this.createEventFromAppointment(appointment)
      console.log('Creating calendar event:', {
        summary: event.summary,
        start: event.start.dateTime,
        end: event.end.dateTime
      })

      const response = await this.calendar.events.insert({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        resource: event,
        sendUpdates: 'all',
      })

      console.log('Calendar event created successfully:', response.data.id)
      return response.data.id || null
    } catch (error) {
      console.error('Failed to create calendar event:', error)
      if (error instanceof Error) {
        console.error('Error details:', error.message)
      }
      return null
    }
  }

  // Update an existing calendar event
  async updateAppointmentEvent(eventId: string, appointment: AppointmentDetails): Promise<boolean> {
    try {
      if (!this.calendar) {
        throw new Error('Google Calendar not initialized')
      }

      const event = this.createEventFromAppointment(appointment)
      
      await this.calendar.events.update({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        eventId,
        resource: event,
        sendUpdates: 'all',
      })

      return true
    } catch (error) {
      console.error('Failed to update calendar event:', error)
      return false
    }
  }

  // Delete a calendar event
  async deleteAppointmentEvent(eventId: string): Promise<boolean> {
    try {
      if (!this.calendar) {
        throw new Error('Google Calendar not initialized')
      }

      await this.calendar.events.delete({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        eventId,
        sendUpdates: 'all',
      })

      return true
    } catch (error) {
      console.error('Failed to delete calendar event:', error)
      return false
    }
  }

  // Create a calendar event object from appointment data
  private createEventFromAppointment(appointment: AppointmentDetails): CalendarEvent {
    const startTime = new Date(appointment.scheduledAt)
    const endTime = new Date(startTime.getTime() + appointment.duration * 60000)
    
    const timeZone = process.env.TIMEZONE || 'Asia/Manila'

    return {
      summary: `${appointment.service.name} - ${appointment.patient.user.name}`,
      description: this.createEventDescription(appointment),
      start: {
        dateTime: startTime.toISOString(),
        timeZone,
      },
      end: {
        dateTime: endTime.toISOString(),
        timeZone,
      },
      location: `${appointment.branch.name}, ${appointment.branch.address}`,
      // Note: Service accounts cannot invite attendees without Domain-Wide Delegation
      // The patient email is included in the description instead
      reminders: {
        useDefault: false,
        overrides: [
          { method: 'email', minutes: 24 * 60 }, // 24 hours before
          { method: 'popup', minutes: 60 }, // 1 hour before
        ],
      },
    }
  }

  // Create event description with appointment details
  private createEventDescription(appointment: AppointmentDetails): string {
    let description = `Dental Appointment Details:\n\n`
    description += `Patient: ${appointment.patient.user.name}\n`
    description += `Patient Email: ${appointment.patient.user.email}\n`
    description += `Service: ${appointment.service.name}\n`
    description += `Dentist: Dr. ${appointment.dentist.user.name}\n`
    description += `Duration: ${appointment.duration} minutes\n`
    description += `Location: ${appointment.branch.name}\n`
    description += `Address: ${appointment.branch.address}\n\n`

    if (appointment.symptoms) {
      description += `Symptoms/Concerns: ${appointment.symptoms}\n\n`
    }

    if (appointment.notes) {
      description += `Additional Notes: ${appointment.notes}\n\n`
    }

    description += `Appointment ID: ${appointment.id}\n`
    description += `Status: ${appointment.status}\n\n`
    description += `Please arrive 15 minutes early for check-in.`

    return description
  }

  // Get calendar events for a specific date range
  async getEvents(startDate: Date, endDate: Date): Promise<any[]> {
    try {
      if (!this.calendar) {
        throw new Error('Google Calendar not initialized')
      }

      const response = await this.calendar.events.list({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
      })

      return response.data.items || []
    } catch (error) {
      console.error('Failed to get calendar events:', error)
      return []
    }
  }

  // Check if a time slot conflicts with existing events
  async checkTimeSlotConflict(startTime: Date, endTime: Date): Promise<boolean> {
    try {
      const events = await this.getEvents(startTime, endTime)
      return events.length > 0
    } catch (error) {
      console.error('Failed to check time slot conflict:', error)
      return false
    }
  }
}

// Singleton instance
let googleCalendarService: GoogleCalendarService | null = null

export function getGoogleCalendarService(): GoogleCalendarService {
  if (!googleCalendarService) {
    googleCalendarService = new GoogleCalendarService()
  }
  return googleCalendarService
}

// Utility functions for calendar integration
export async function createCalendarEventForAppointment(appointment: AppointmentDetails): Promise<string | null> {
  try {
    const service = getGoogleCalendarService()
    const result = await service.createAppointmentEvent(appointment)

    if (result) {
      console.log(`✅ Google Calendar event created successfully: ${result}`)
    } else {
      console.warn('⚠️ Google Calendar event creation returned null - check configuration')
    }

    return result
  } catch (error) {
    console.error('❌ Failed to create Google Calendar event:', error)
    return null
  }
}

export async function updateCalendarEventForAppointment(eventId: string, appointment: AppointmentDetails): Promise<boolean> {
  const service = getGoogleCalendarService()
  return await service.updateAppointmentEvent(eventId, appointment)
}

export async function deleteCalendarEventForAppointment(eventId: string): Promise<boolean> {
  const service = getGoogleCalendarService()
  return await service.deleteAppointmentEvent(eventId)
}

// Hook for client-side calendar integration
export function useGoogleCalendar() {
  const addToCalendar = (appointment: AppointmentDetails) => {
    const startTime = new Date(appointment.scheduledAt)
    const endTime = new Date(startTime.getTime() + appointment.duration * 60000)
    
    const event = {
      title: `${appointment.service.name} - Bright Smile Dental`,
      start: startTime.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
      end: endTime.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z',
      description: `Dental appointment with Dr. ${appointment.dentist.user.name} at ${appointment.branch.name}`,
      location: `${appointment.branch.name}, ${appointment.branch.address}`,
    }

    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(event.title)}&dates=${event.start}/${event.end}&details=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}`
    
    window.open(googleCalendarUrl, '_blank')
  }

  return { addToCalendar }
}
