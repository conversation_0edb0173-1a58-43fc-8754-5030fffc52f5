'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { AppointmentList } from '@/components/appointments/appointment-list'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Calendar, Clock, FileText, Plus } from 'lucide-react'
import { UserRole } from '@prisma/client'

export default function AppointmentHistoryPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [error] = useState<string | null>(null)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/appointments/history')
    } else if (isAuthenticated && user?.role !== UserRole.PATIENT) {
      router.push('/')
    }
  }, [isAuthenticated, isLoading, user, router])

  const handleViewDetails = (appointmentId: string) => {
    router.push(`/appointments/${appointmentId}`)
  }

  const handleBookNew = () => {
    router.push('/appointments/book')
  }

  const handleGoBack = () => {
    router.push('/dashboard')
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your appointment history...</p>
        </div>
      </div>
    )
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-gray-900">Authentication Required</CardTitle>
                <CardDescription>
                  Please sign in to view your appointment history
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button 
                  onClick={() => router.push('/auth/signin?callbackUrl=/appointments/history')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Sign In to Continue
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              onClick={handleGoBack}
              variant="outline"
              size="sm"
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Appointment History</h1>
              <p className="text-gray-600 mt-1">
                View your past dental appointments and treatments
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={() => router.push('/appointments/upcoming')}
                variant="outline"
                size="sm"
                className="flex items-center"
              >
                <Clock className="h-4 w-4 mr-2" />
                Upcoming Appointments
              </Button>
              <Button
                onClick={handleBookNew}
                className="bg-blue-600 hover:bg-blue-700 flex items-center"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Book New Appointment
              </Button>
            </div>
          </div>
        </div>

        {/* Welcome message */}
        <Card className="bg-white/70 backdrop-blur-sm border-blue-200 mb-8">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mr-4">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Your Dental Journey</p>
                <p className="text-sm text-gray-600">
                  Review your completed appointments and track your dental health progress.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Appointment History List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-blue-600" />
              Past Appointments
            </CardTitle>
            <CardDescription>
              Your completed, cancelled, and no-show appointments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AppointmentList
              onViewDetails={handleViewDetails}
              onBookNew={handleBookNew}
              showActions={false}
              statusFilter="COMPLETED,CANCELLED,NO_SHOW"
              sortBy="scheduledAt"
              sortOrder="desc"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
