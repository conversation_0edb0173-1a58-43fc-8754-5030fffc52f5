import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canManageInventory } from '@/lib/auth-utils'
import { inventoryItemUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/inventory/[id] - Get inventory item by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const item = await prisma.inventoryItem.findUnique({
    where: { id },
    include: {
      branch: {
        select: {
          id: true,
          name: true,
          address: true,
        },
      },
      usageLogs: {
        orderBy: { usedAt: 'desc' },
        take: 10,
        include: {
          patient: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      },
      stockLogs: {
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
    },
  })
  
  if (!item) {
    return NextResponse.json(
      { error: 'Inventory item not found' },
      { status: 404 }
    )
  }
  
  // Calculate stock status
  const stockStatus = {
    isLowStock: item.currentStock <= item.minStock,
    isOutOfStock: item.currentStock === 0,
    isExpired: item.expiryDate ? new Date(item.expiryDate) <= new Date() : false,
    daysUntilExpiry: item.expiryDate 
      ? Math.ceil((new Date(item.expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
      : null,
  }
  
  return NextResponse.json({
    ...item,
    stockStatus,
  })
}, [UserRole.ADMIN, UserRole.STAFF])

// PUT /api/inventory/[id] - Update inventory item
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  if (!canManageInventory(user.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }
  
  const existingItem = await prisma.inventoryItem.findUnique({
    where: { id },
  })
  
  if (!existingItem) {
    return NextResponse.json(
      { error: 'Inventory item not found' },
      { status: 404 }
    )
  }
  
  const validatedData = inventoryItemUpdateSchema.parse(body)
  
  // If SKU is being updated, check for conflicts
  if (validatedData.sku && validatedData.sku !== existingItem.sku) {
    const existingWithSku = await prisma.inventoryItem.findUnique({
      where: { sku: validatedData.sku },
    })
    
    if (existingWithSku) {
      return NextResponse.json(
        { error: 'SKU already exists' },
        { status: 400 }
      )
    }
  }
  
  const updatedItem = await prisma.inventoryItem.update({
    where: { id },
    data: validatedData,
    include: {
      branch: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })
  
  return NextResponse.json(updatedItem)
}, [UserRole.ADMIN, UserRole.STAFF])

// DELETE /api/inventory/[id] - Delete inventory item
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  if (!canManageInventory(user.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }
  
  const item = await prisma.inventoryItem.findUnique({
    where: { id },
    include: {
      usageLogs: true,
      stockLogs: true,
    },
  })
  
  if (!item) {
    return NextResponse.json(
      { error: 'Inventory item not found' },
      { status: 404 }
    )
  }
  
  // Check if item has usage history
  if (item.usageLogs.length > 0) {
    return NextResponse.json(
      { error: 'Cannot delete item with usage history. Consider marking as inactive instead.' },
      { status: 400 }
    )
  }
  
  await prisma.inventoryItem.delete({
    where: { id },
  })
  
  return NextResponse.json({ message: 'Inventory item deleted successfully' })
}, [UserRole.ADMIN])
