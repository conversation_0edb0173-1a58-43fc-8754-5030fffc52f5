'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Calendar, ExternalLink, Download, Smartphone, Monitor } from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'
import { generateCalendarUrls, openCalendarUrl, openCalendarApp, downloadICSFile } from '@/lib/calendar-utils'

interface AddToCalendarButtonProps {
  appointment: AppointmentDetails
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  className?: string
}

export function AddToCalendarButton({
  appointment,
  variant = 'outline',
  size = 'sm',
  className
}: AddToCalendarButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false)

  // Generate all calendar URLs using the utility
  const calendarUrls = generateCalendarUrls(appointment)

  // Calendar action handlers using the utility functions
  const addToGoogleCalendar = () => openCalendarUrl(calendarUrls.google)
  const addToOutlookCalendar = () => openCalendarUrl(calendarUrls.outlook)
  const addToOutlookDesktop = () => openCalendarApp(calendarUrls.outlookDesktop)
  const addToAppleCalendar = () => downloadICSFile(appointment)
  const addToYahooCalendar = () => openCalendarUrl(calendarUrls.yahoo)

  const handleDownloadICS = async () => {
    setIsGenerating(true)
    try {
      downloadICSFile(appointment)
    } catch (error) {
      console.error('Failed to generate ICS file:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <Calendar className="h-4 w-4 mr-2" />
          Add to Calendar
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuItem onClick={addToGoogleCalendar}>
          <ExternalLink className="h-4 w-4 mr-2" />
          Google Calendar
        </DropdownMenuItem>
        <DropdownMenuItem onClick={addToOutlookCalendar}>
          <ExternalLink className="h-4 w-4 mr-2" />
          Outlook (Web)
        </DropdownMenuItem>
        <DropdownMenuItem onClick={addToOutlookDesktop}>
          <Monitor className="h-4 w-4 mr-2" />
          Outlook (Desktop)
        </DropdownMenuItem>
        <DropdownMenuItem onClick={addToAppleCalendar}>
          <Smartphone className="h-4 w-4 mr-2" />
          Apple Calendar
        </DropdownMenuItem>
        <DropdownMenuItem onClick={addToYahooCalendar}>
          <ExternalLink className="h-4 w-4 mr-2" />
          Yahoo Calendar
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDownloadICS} disabled={isGenerating}>
          <Download className="h-4 w-4 mr-2" />
          {isGenerating ? 'Generating...' : 'Download .ics file'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
