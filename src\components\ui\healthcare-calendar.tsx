'use client'

import * as React from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface HealthcareCalendarProps {
  selected?: Date
  onSelect?: (date: Date | undefined) => void
  disabled?: (date: Date) => boolean
  className?: string
  mode?: 'single'
}

const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
]

export function HealthcareCalendar({
  selected,
  onSelect,
  disabled,
  className,
  mode: _mode = 'single'
}: HealthcareCalendarProps) {
  const [currentDate, setCurrentDate] = React.useState(() => {
    return selected || new Date()
  })

  const today = new Date()
  const currentMonth = currentDate.getMonth()
  const currentYear = currentDate.getFullYear()

  // Get first day of the month and number of days
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1)
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
  const firstDayWeekday = firstDayOfMonth.getDay()
  const daysInMonth = lastDayOfMonth.getDate()

  // Get previous month's last days to fill the grid
  const prevMonth = new Date(currentYear, currentMonth - 1, 0)
  const daysInPrevMonth = prevMonth.getDate()

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const handleDateClick = (day: number, isCurrentMonth: boolean = true) => {
    if (!isCurrentMonth) return
    
    const clickedDate = new Date(currentYear, currentMonth, day)
    
    // Check if date is disabled
    if (disabled && disabled(clickedDate)) return
    
    onSelect?.(clickedDate)
  }

  const isDateSelected = (day: number) => {
    if (!selected) return false
    const date = new Date(currentYear, currentMonth, day)
    return (
      date.getDate() === selected.getDate() &&
      date.getMonth() === selected.getMonth() &&
      date.getFullYear() === selected.getFullYear()
    )
  }

  const isToday = (day: number) => {
    return (
      day === today.getDate() &&
      currentMonth === today.getMonth() &&
      currentYear === today.getFullYear()
    )
  }

  const isDateDisabled = (day: number) => {
    if (!disabled) return false
    const date = new Date(currentYear, currentMonth, day)
    return disabled(date)
  }

  // Generate calendar grid
  const calendarDays = []
  
  // Previous month's trailing days
  for (let i = firstDayWeekday - 1; i >= 0; i--) {
    const day = daysInPrevMonth - i
    calendarDays.push({
      day,
      isCurrentMonth: false,
      isPrevMonth: true
    })
  }
  
  // Current month's days
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: true,
      isPrevMonth: false
    })
  }
  
  // Next month's leading days to complete the grid
  const remainingCells = 42 - calendarDays.length // 6 rows × 7 days
  for (let day = 1; day <= remainingCells; day++) {
    calendarDays.push({
      day,
      isCurrentMonth: false,
      isPrevMonth: false
    })
  }

  return (
    <div className={cn('bg-white rounded-xl border-2 border-blue-100 shadow-sm p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigateMonth('prev')}
          className="h-9 w-9 p-0 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <h2 className="text-lg font-semibold text-gray-900">
          {MONTHS[currentMonth]} {currentYear}
        </h2>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigateMonth('next')}
          className="h-9 w-9 p-0 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Days of week header */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {DAYS_OF_WEEK.map((day) => (
          <div
            key={day}
            className="h-10 flex items-center justify-center text-sm font-medium text-gray-600"
          >
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((dateInfo, index) => {
          const { day, isCurrentMonth } = dateInfo
          const selected = isCurrentMonth && isDateSelected(day)
          const today = isCurrentMonth && isToday(day)
          const disabled = isCurrentMonth && isDateDisabled(day)
          
          return (
            <button
              key={index}
              onClick={() => handleDateClick(day, isCurrentMonth)}
              disabled={disabled || !isCurrentMonth}
              className={cn(
                'h-10 w-10 rounded-lg text-sm font-medium transition-all duration-200 relative',
                'hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
                {
                  // Current month days
                  'text-gray-900': isCurrentMonth && !disabled && !selected && !today,
                  
                  // Selected date
                  'bg-blue-600 text-white hover:bg-blue-700 shadow-lg scale-105': selected,
                  
                  // Today (not selected)
                  'bg-green-100 text-green-900 font-bold border-2 border-green-300': today && !selected,
                  
                  // Disabled dates
                  'text-gray-300 cursor-not-allowed line-through': disabled,
                  
                  // Other month days
                  'text-gray-400 cursor-default': !isCurrentMonth,
                }
              )}
            >
              {day}
              
              {/* Selected indicator */}
              {selected && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full flex items-center justify-center">
                  <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                </div>
              )}
              
              {/* Today indicator (when not selected) */}
              {today && !selected && (
                <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-green-600 rounded-full"></div>
              )}
            </button>
          )
        })}
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 mt-6 pt-4 border-t border-gray-100">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-100 border-2 border-green-300 rounded"></div>
          <span className="text-xs text-gray-600">Today</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-600 rounded"></div>
          <span className="text-xs text-gray-600">Selected</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-200 rounded line-through"></div>
          <span className="text-xs text-gray-600">Unavailable</span>
        </div>
      </div>
    </div>
  )
}
