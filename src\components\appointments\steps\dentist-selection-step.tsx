'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loader2, User, Award, CheckCircle } from 'lucide-react'
import { DentistProfile } from '@/types/appointment'

interface DentistSelectionStepProps {
  branchId: string
  selectedDentistId?: string
  onSelect: (dentistId: string) => void
  onNext: () => void
}

export function DentistSelectionStep({ branchId, selectedDentistId, onSelect, onNext }: DentistSelectionStepProps) {
  const [dentists, setDentists] = useState<DentistProfile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchDentists = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/dentists?branchId=${branchId}&isAvailable=true`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch dentists')
      }
      
      const data = await response.json()
      setDentists(data.dentists || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dentists')
    } finally {
      setIsLoading(false)
    }
  }, [branchId])

  useEffect(() => {
    fetchDentists()
  }, [fetchDentists])

  const handleDentistSelect = (dentistId: string) => {
    onSelect(dentistId)
  }

  const handleNext = () => {
    if (selectedDentistId) {
      onNext()
    }
  }

  const formatConsultationFee = (fee?: number) => {
    if (!fee) return 'Consultation fee varies'
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(fee)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading dentists...</span>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (dentists.length === 0) {
    return (
      <Alert>
        <AlertDescription>No dentists are currently available for this branch. Please try another branch.</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 mb-4">
        Please select your preferred dentist for your appointment.
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {dentists.map((dentist) => (
          <Card
            key={dentist.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedDentistId === dentist.id
                ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                : 'hover:border-gray-400'
            }`}
            onClick={() => handleDentistSelect(dentist.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start space-x-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={dentist.user.image} alt={dentist.user.name} />
                  <AvatarFallback className="bg-blue-100 text-blue-600">
                    {getInitials(dentist.user.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <CardTitle className="text-lg text-gray-900 flex items-center">
                    Dr. {dentist.user.name}
                    {selectedDentistId === dentist.id && (
                      <CheckCircle className="h-5 w-5 text-blue-600 ml-2" />
                    )}
                  </CardTitle>
                  {dentist.specialization && (
                    <CardDescription className="mt-1">
                      {dentist.specialization}
                    </CardDescription>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {dentist.yearsExperience && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Award className="h-4 w-4 mr-2 text-gray-400" />
                    <span>{dentist.yearsExperience} years of experience</span>
                  </div>
                )}
                
                <div className="flex items-center text-sm text-gray-600">
                  <User className="h-4 w-4 mr-2 text-gray-400" />
                  <span>License: {dentist.licenseNumber}</span>
                </div>

                {dentist.bio && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {dentist.bio}
                  </p>
                )}

                <div className="flex items-center justify-between pt-2">
                  <span className="text-sm text-gray-600">
                    {formatConsultationFee(dentist.consultationFee)}
                  </span>
                  <Badge variant="success" className="bg-green-100 text-green-800">
                    Available
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedDentistId && (
        <div className="flex justify-end pt-4">
          <Button onClick={handleNext} className="bg-blue-600 hover:bg-blue-700">
            Continue to Date & Time Selection
          </Button>
        </div>
      )}
    </div>
  )
}
