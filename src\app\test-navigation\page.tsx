'use client'

import { useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  ExternalLink,
  Home,
  Calendar,
  User,
  Settings,
  Shield
} from 'lucide-react'

interface NavigationTest {
  name: string
  path: string
  description: string
  requiresAuth: boolean
  allowedRoles?: string[]
  testStatus: 'pending' | 'success' | 'error'
  errorMessage?: string
}

export default function NavigationTestPage() {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  
  const [tests, setTests] = useState<NavigationTest[]>([
    {
      name: 'Home Page',
      path: '/',
      description: 'Main landing page with services and information',
      requiresAuth: false,
      testStatus: 'pending'
    },
    {
      name: 'Book Appointment (Unauthenticated)',
      path: '/appointments/book',
      description: 'Should redirect to sign-in page when not authenticated',
      requiresAuth: false,
      testStatus: 'pending'
    },
    {
      name: 'Sign In Page',
      path: '/auth/signin',
      description: 'Authentication page with Google Sign-in',
      requiresAuth: false,
      testStatus: 'pending'
    },
    {
      name: 'Patient Dashboard',
      path: '/dashboard',
      description: 'Patient dashboard with appointments and quick actions',
      requiresAuth: true,
      allowedRoles: ['PATIENT'],
      testStatus: 'pending'
    },
    {
      name: 'Book Appointment (Authenticated)',
      path: '/appointments/book',
      description: 'Appointment booking form for authenticated patients',
      requiresAuth: true,
      allowedRoles: ['PATIENT'],
      testStatus: 'pending'
    },
    {
      name: 'Admin Dashboard',
      path: '/admin/dashboard',
      description: 'Administrative dashboard for clinic management',
      requiresAuth: true,
      allowedRoles: ['ADMIN', 'STAFF'],
      testStatus: 'pending'
    },
    {
      name: 'Dentist Dashboard',
      path: '/dentist/dashboard',
      description: 'Dentist dashboard for patient and appointment management',
      requiresAuth: true,
      allowedRoles: ['DENTIST'],
      testStatus: 'pending'
    },
    {
      name: 'Invalid Route (404)',
      path: '/invalid-route-test',
      description: 'Should show 404 page with proper error handling',
      requiresAuth: false,
      testStatus: 'pending'
    }
  ])

  const testNavigation = async (test: NavigationTest, index: number) => {
    try {
      // Update test status to pending
      setTests(prev => prev.map((t, i) => 
        i === index ? { ...t, testStatus: 'pending' as const } : t
      ))

      // Simulate navigation test
      await new Promise(resolve => setTimeout(resolve, 500))

      // Check authentication requirements
      if (test.requiresAuth && !isAuthenticated) {
        setTests(prev => prev.map((t, i) => 
          i === index ? { 
            ...t, 
            testStatus: 'error' as const, 
            errorMessage: 'Requires authentication but user is not logged in'
          } : t
        ))
        return
      }

      // Check role requirements
      if (test.allowedRoles && user && !test.allowedRoles.includes(user.role)) {
        setTests(prev => prev.map((t, i) =>
          i === index ? {
            ...t,
            testStatus: 'error' as const,
            errorMessage: `User role '${user.role}' not allowed. Required: ${test.allowedRoles?.join(', ') || 'Unknown'}`
          } : t
        ))
        return
      }

      // Mark as success
      setTests(prev => prev.map((t, i) => 
        i === index ? { ...t, testStatus: 'success' as const, errorMessage: undefined } : t
      ))

    } catch (error) {
      setTests(prev => prev.map((t, i) => 
        i === index ? { 
          ...t, 
          testStatus: 'error' as const, 
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        } : t
      ))
    }
  }

  const navigateToPath = (path: string) => {
    router.push(path)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Navigation Testing Dashboard
          </h1>
          <p className="text-gray-600">
            Test all navigation links and routes in the dental clinic management system
          </p>
        </div>

        {/* Current User Status */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Current User Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-700">Authentication</p>
                <Badge className={isAuthenticated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                  {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">User Role</p>
                <Badge variant="outline">
                  {user?.role || 'None'}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Current Path</p>
                <Badge variant="outline">
                  {pathname}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Tests */}
        <div className="grid gap-4">
          {tests.map((test, index) => (
            <Card key={test.path} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(test.testStatus)}
                    <div>
                      <CardTitle className="text-lg">{test.name}</CardTitle>
                      <CardDescription>{test.description}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(test.testStatus)}>
                      {test.testStatus}
                    </Badge>
                    {test.requiresAuth && (
                      <Badge variant="outline" className="text-blue-600 border-blue-200">
                        <Shield className="h-3 w-3 mr-1" />
                        Auth Required
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-700">Path: {test.path}</p>
                    {test.allowedRoles && (
                      <p className="text-sm text-gray-600">
                        Allowed Roles: {test.allowedRoles.join(', ')}
                      </p>
                    )}
                    {test.errorMessage && (
                      <p className="text-sm text-red-600">{test.errorMessage}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => testNavigation(test, index)}
                    >
                      Test Route
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigateToPath(test.path)}
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Navigate
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Quick Navigation</CardTitle>
            <CardDescription>
              Quick links to test common navigation scenarios
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button variant="outline" onClick={() => router.push('/')}>
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
              <Button variant="outline" onClick={() => router.push('/appointments/book')}>
                <Calendar className="h-4 w-4 mr-2" />
                Book Appointment
              </Button>
              <Button variant="outline" onClick={() => router.push('/dashboard')}>
                <User className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button variant="outline" onClick={() => router.push('/auth/signin')}>
                <Settings className="h-4 w-4 mr-2" />
                Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
