import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { userCreateSchema, paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/users - List users with pagination and search
export const GET = withAuth(async (req: NextRequest) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const role = searchParams.get('role') as UserRole | null
  const status = searchParams.get('status')
  
  const skip = (page - 1) * limit
  
  const where: Record<string, unknown> = {
    ...(query && {
      OR: [
        { name: { contains: query, mode: 'insensitive' as const } },
        { email: { contains: query, mode: 'insensitive' as const } },
      ],
    }),
    ...(role && { role }),
    ...(status && {
      status: status // Status is already validated by the API
    }),
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'name' ? { name: sortOrder } :
                  sortBy === 'email' ? { email: sortOrder } :
                  sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'updatedAt' ? { updatedAt: sortOrder } :
                  { createdAt: sortOrder as 'asc' | 'desc' }

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      select: {
        id: true,
        email: true,
        name: true,
        image: true,
        phone: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        patientProfile: {
          select: {
            id: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        dentistProfile: {
          select: {
            id: true,
            licenseNumber: true,
            specialization: true,
          },
        },
        staffProfile: {
          select: {
            id: true,
            position: true,
            department: true,
          },
        },
      },
    }),
    prisma.user.count({ where }),
  ])
  
  return NextResponse.json({
    users,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF])

// POST /api/users - Create a new user
export const POST = withAuth(async (req: NextRequest) => {
  const body = await req.json()
  const validatedData = userCreateSchema.parse(body)
  
  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: validatedData.email },
  })
  
  if (existingUser) {
    return NextResponse.json(
      { error: 'User with this email already exists' },
      { status: 400 }
    )
  }
  
  // Create user
  const user = await prisma.user.create({
    data: {
      email: validatedData.email,
      name: validatedData.name,
      phone: validatedData.phone,
      role: validatedData.role,
    },
    select: {
      id: true,
      email: true,
      name: true,
      phone: true,
      role: true,
      status: true,
      createdAt: true,
    },
  })
  
  // Create role-specific profile
  if (validatedData.role === UserRole.PATIENT) {
    await prisma.patientProfile.create({
      data: { userId: user.id },
    })
  } else if (validatedData.role === UserRole.DENTIST) {
    // Note: Dentist profile requires additional data, so we just create a placeholder
    // The actual profile should be completed through a separate endpoint
  } else if (validatedData.role === UserRole.STAFF) {
    // Note: Staff profile requires additional data, so we just create a placeholder
    // The actual profile should be completed through a separate endpoint
  }
  
  return NextResponse.json(user, { status: 201 })
}, [UserRole.ADMIN])
