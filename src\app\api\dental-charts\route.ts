import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { UserRole, ToothCondition } from '@prisma/client'
import { z } from 'zod'

const dentalChartCreateSchema = z.object({
  patientId: z.string().cuid(),
})



// GET /api/dental-charts - Get dental chart by patient ID
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  const patientId = searchParams.get('patientId')
  
  if (!patientId) {
    return NextResponse.json(
      { error: 'patientId is required' },
      { status: 400 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Verify patient exists
  const patient = await prisma.patientProfile.findUnique({
    where: { id: patientId },
    include: { user: { select: { name: true, email: true } } },
  })
  
  if (!patient) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Get or create dental chart
  let dentalChart = await prisma.dentalChart.findUnique({
    where: { patientId },
    include: {
      teeth: {
        orderBy: { toothNumber: 'asc' },
      },
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  })
  
  if (!dentalChart) {
    // Create dental chart with default teeth records
    dentalChart = await prisma.dentalChart.create({
      data: {
        patientId,
        teeth: {
          create: Array.from({ length: 32 }, (_, i) => ({
            toothNumber: i + 1,
            condition: ToothCondition.HEALTHY,
          })),
        },
      },
      include: {
        teeth: {
          orderBy: { toothNumber: 'asc' },
        },
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    })
  }
  
  return NextResponse.json(dentalChart)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/dental-charts - Create dental chart
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = dentalChartCreateSchema.parse(body)
  
  // Check access permissions
  if (!canAccessPatientData(user.role, validatedData.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Verify patient exists
  const patient = await prisma.patientProfile.findUnique({
    where: { id: validatedData.patientId },
  })
  
  if (!patient) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check if dental chart already exists
  const existingChart = await prisma.dentalChart.findUnique({
    where: { patientId: validatedData.patientId },
  })
  
  if (existingChart) {
    return NextResponse.json(
      { error: 'Dental chart already exists for this patient' },
      { status: 400 }
    )
  }
  
  // Create dental chart with default teeth records
  const dentalChart = await prisma.dentalChart.create({
    data: {
      patientId: validatedData.patientId,
      teeth: {
        create: Array.from({ length: 32 }, (_, i) => ({
          toothNumber: i + 1,
          condition: ToothCondition.HEALTHY,
        })),
      },
    },
    include: {
      teeth: {
        orderBy: { toothNumber: 'asc' },
      },
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  })
  
  return NextResponse.json(dentalChart, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])
