import { prisma } from './prisma'
import { NextRequest } from 'next/server'

export interface AuditLogData {
  userId?: string
  action: string
  resource: string
  resourceId?: string
  oldValues?: Record<string, unknown>
  newValues?: Record<string, unknown>
  ipAddress?: string
  userAgent?: string
}

export async function createAuditLog(data: AuditLogData): Promise<void> {
  try {
    await prisma.auditLog.create({
      data: {
        userId: data.userId,
        action: data.action,
        resource: data.resource,
        resourceId: data.resourceId,
        oldValues: data.oldValues as any,
        newValues: data.newValues as any,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
      },
    })
  } catch (error) {
    console.error('Failed to create audit log:', error)
    // Don't throw error to avoid breaking the main operation
  }
}

export function getRequestInfo(request: NextRequest) {
  return {
    ipAddress: (request as { ip?: string }).ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
  }
}

// Audit actions
export const AUDIT_ACTIONS = {
  // User actions
  USER_CREATED: 'user.created',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted',
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  
  // Patient actions
  PATIENT_CREATED: 'patient.created',
  PATIENT_UPDATED: 'patient.updated',
  PATIENT_DELETED: 'patient.deleted',
  
  // Appointment actions
  APPOINTMENT_CREATED: 'appointment.created',
  APPOINTMENT_UPDATED: 'appointment.updated',
  APPOINTMENT_CANCELLED: 'appointment.cancelled',
  APPOINTMENT_COMPLETED: 'appointment.completed',
  
  // Treatment actions
  TREATMENT_CREATED: 'treatment.created',
  TREATMENT_UPDATED: 'treatment.updated',
  TREATMENT_COMPLETED: 'treatment.completed',
  TREATMENT_DELETED: 'treatment.deleted',
  
  // Billing actions
  INVOICE_CREATED: 'invoice.created',
  INVOICE_UPDATED: 'invoice.updated',
  INVOICE_DELETED: 'invoice.deleted',
  PAYMENT_CREATED: 'payment.created',
  PAYMENT_PROCESSED: 'payment.processed',
  PAYMENT_FAILED: 'payment.failed',
  
  // Inventory actions
  INVENTORY_CREATED: 'inventory.created',
  INVENTORY_UPDATED: 'inventory.updated',
  INVENTORY_DELETED: 'inventory.deleted',
  STOCK_MOVEMENT: 'inventory.stock_movement',
  
  // File actions
  FILE_UPLOADED: 'file.uploaded',
  FILE_DOWNLOADED: 'file.downloaded',
  FILE_DELETED: 'file.deleted',
  
  // System actions
  SYSTEM_BACKUP: 'system.backup',
  SYSTEM_RESTORE: 'system.restore',
  SYSTEM_MAINTENANCE: 'system.maintenance',
  
  // Security actions
  UNAUTHORIZED_ACCESS: 'security.unauthorized_access',
  RATE_LIMIT_EXCEEDED: 'security.rate_limit_exceeded',
  SUSPICIOUS_ACTIVITY: 'security.suspicious_activity',
} as const

// Resource types
export const AUDIT_RESOURCES = {
  USER: 'user',
  PATIENT: 'patient',
  DENTIST: 'dentist',
  STAFF: 'staff',
  APPOINTMENT: 'appointment',
  TREATMENT: 'treatment',
  INVOICE: 'invoice',
  PAYMENT: 'payment',
  INVENTORY: 'inventory',
  FILE: 'file',
  NOTIFICATION: 'notification',
  SYSTEM: 'system',
} as const

// Helper functions for common audit scenarios
export async function auditUserAction(
  userId: string,
  action: string,
  resource: string,
  resourceId?: string,
  oldValues?: Record<string, unknown>,
  newValues?: Record<string, unknown>,
  request?: NextRequest
) {
  const requestInfo = request ? getRequestInfo(request) : {}
  
  await createAuditLog({
    userId,
    action,
    resource,
    resourceId,
    oldValues,
    newValues,
    ...requestInfo,
  })
}

export async function auditSecurityEvent(
  action: string,
  details: Record<string, unknown>,
  request?: NextRequest
) {
  const requestInfo = request ? getRequestInfo(request) : {}
  
  await createAuditLog({
    action,
    resource: AUDIT_RESOURCES.SYSTEM,
    newValues: details,
    ...requestInfo,
  })
}

// Middleware helper to automatically audit API calls
export function withAudit<T extends unknown[]>(
  handler: (...args: T) => Promise<Response>,
  action: string,
  resource: string
) {
  return async (...args: T): Promise<Response> => {
    const request = args[0] as NextRequest
    const userId = request.headers.get('x-user-id')
    const requestInfo = getRequestInfo(request)
    
    try {
      const response = await handler(...args)
      
      // Log successful operations
      if (response.status < 400) {
        await createAuditLog({
          userId: userId || undefined,
          action,
          resource,
          ...requestInfo,
        })
      }
      
      return response
    } catch (error) {
      // Log failed operations
      await createAuditLog({
        userId: userId || undefined,
        action: `${action}.failed`,
        resource,
        newValues: { error: error instanceof Error ? error.message : 'Unknown error' },
        ...requestInfo,
      })
      
      throw error
    }
  }
}

// Function to clean up old audit logs (should be run periodically)
export async function cleanupAuditLogs(retentionDays: number = 90) {
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - retentionDays)
  
  try {
    const result = await prisma.auditLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    })
    
    console.log(`Cleaned up ${result.count} audit log entries older than ${retentionDays} days`)
    return result.count
  } catch (error) {
    console.error('Failed to cleanup audit logs:', error)
    throw error
  }
}
