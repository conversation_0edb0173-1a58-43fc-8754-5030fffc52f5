'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'

import {
  Calendar,
  Clock,
  User,
  Phone,
  MapPin,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  Filter,
  MoreVertical,

  Check,
  X,
  Eye,
  ChevronLeft,
  ChevronRight,
  RefreshCw
} from 'lucide-react'

import { AppointmentDetails } from '@/types/appointment'
import { AppointmentStatus, AppointmentType } from '@prisma/client'
import { useAppointments, AppointmentFilters } from '@/hooks/useAppointments'
import { formatAppointmentDate, formatAppointmentTime, formatDuration, formatPrice } from '@/lib/appointment-utils'
import { AppointmentStatusDialog } from './appointment-status-dialog'
import { AppointmentListSkeleton, FiltersSkeleton } from './appointment-skeleton'

interface DentistAppointmentListProps {
  dentistId: string
  onViewDetails?: (appointmentId: string) => void
  onUpdateStatus?: (appointmentId: string, status: AppointmentStatus, reason?: string) => void
}

export function DentistAppointmentList({
  dentistId,
  onViewDetails,
  onUpdateStatus
}: DentistAppointmentListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<AppointmentStatus | 'ALL'>('ALL')
  const [selectedType, setSelectedType] = useState<AppointmentType | 'ALL'>('ALL')
  const [dateFilter, setDateFilter] = useState<'TODAY' | 'UPCOMING' | 'PAST' | 'ALL'>('ALL')
  const [statusDialogAppointment, setStatusDialogAppointment] = useState<AppointmentDetails | null>(null)
  const [updatingAppointmentId, setUpdatingAppointmentId] = useState<string | null>(null)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  // Auto-dismiss error message after 5 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        setErrorMessage(null)
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [errorMessage])

  // Build filters for the hook
  const filters: AppointmentFilters = {
    ...(selectedStatus !== 'ALL' && { status: selectedStatus }),
    ...(selectedType !== 'ALL' && { type: selectedType }),
    ...(searchQuery && { patientName: searchQuery }),
    ...(dateFilter === 'TODAY' && {
      dateFrom: new Date().toISOString().split('T')[0],
      dateTo: new Date().toISOString().split('T')[0]
    }),
    ...(dateFilter === 'UPCOMING' && {
      dateFrom: new Date().toISOString().split('T')[0]
    }),
    ...(dateFilter === 'PAST' && {
      dateTo: new Date().toISOString().split('T')[0]
    })
  }

  const {
    appointments,
    loading,
    error,
    totalCount,
    currentPage,
    totalPages,
    hasNextPage,
    hasPrevPage,
    updateFilters,
    clearFilters,
    loadNextPage,
    loadPrevPage,
    updateAppointmentStatus
  } = useAppointments({
    dentistId,
    initialFilters: filters,
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Handle filter changes
  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    updateFilters({ patientName: value || undefined })
  }

  const handleStatusChange = (status: string) => {
    const newStatus = status === 'ALL' ? undefined : status as AppointmentStatus
    setSelectedStatus(status as AppointmentStatus | 'ALL')
    updateFilters({ status: newStatus })
  }

  const handleTypeChange = (type: string) => {
    const newType = type === 'ALL' ? undefined : type as AppointmentType
    setSelectedType(type as AppointmentType | 'ALL')
    updateFilters({ type: newType })
  }

  const handleDateFilterChange = (filter: string) => {
    setDateFilter(filter as 'TODAY' | 'UPCOMING' | 'PAST' | 'ALL')
    const today = new Date().toISOString().split('T')[0]

    switch (filter) {
      case 'TODAY':
        updateFilters({ dateFrom: today, dateTo: today })
        break
      case 'UPCOMING':
        updateFilters({ dateFrom: today, dateTo: undefined })
        break
      case 'PAST':
        updateFilters({ dateFrom: undefined, dateTo: today })
        break
      default:
        updateFilters({ dateFrom: undefined, dateTo: undefined })
    }
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setSelectedStatus('ALL')
    setSelectedType('ALL')
    setDateFilter('ALL')
    clearFilters()
  }

  // Status management
  const handleStatusUpdate = async (appointmentId: string, status: AppointmentStatus, reason?: string, newDate?: Date) => {
    try {
      setUpdatingAppointmentId(appointmentId)
      setErrorMessage(null) // Clear any previous errors
      await updateAppointmentStatus(appointmentId, status, reason, newDate)
      onUpdateStatus?.(appointmentId, status, reason)
    } catch (error) {
      console.error('Failed to update appointment status:', error)
      setErrorMessage(error instanceof Error ? error.message : 'Failed to update appointment status')
      throw error // Re-throw to let dialog handle the error
    } finally {
      setUpdatingAppointmentId(null)
    }
  }

  const handleOpenStatusDialog = (appointment: AppointmentDetails) => {
    setStatusDialogAppointment(appointment)
  }

  const handleCloseStatusDialog = () => {
    setStatusDialogAppointment(null)
  }

  // Group appointments by date
  const groupAppointmentsByDate = (appointments: AppointmentDetails[]) => {
    const groups: Record<string, AppointmentDetails[]> = {}

    appointments.forEach(appointment => {
      const date = new Date(appointment.scheduledAt).toDateString()
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(appointment)
    })

    return groups
  }

  const appointmentGroups = groupAppointmentsByDate(appointments)

  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertCircle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          {error}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {errorMessage && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{errorMessage}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setErrorMessage(null)}
              className="h-auto p-1 text-red-600 hover:text-red-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State for Filters */}
      {loading && !appointments.length ? (
        <FiltersSkeleton />
      ) : (
        /* Filters and Search */
        <Card className="border-blue-100/50 bg-gradient-to-r from-blue-50/30 to-white">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Filter className="h-5 w-5 text-blue-600" />
              Filter Appointments
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.location.reload()}
              disabled={loading}
              className="text-blue-600 hover:bg-blue-50"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Mobile-first responsive layout */}
          <div className="space-y-4">
            {/* Search - Full width on mobile */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by patient name..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10 border-blue-200 focus:border-blue-400 focus:ring-blue-400"
              />
            </div>

            {/* Filters - Responsive grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">


            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={handleStatusChange}>
              <SelectTrigger className="border-blue-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Statuses</SelectItem>
                <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="NO_SHOW">No Show</SelectItem>
              </SelectContent>
            </Select>

            {/* Type Filter */}
            <Select value={selectedType} onValueChange={handleTypeChange}>
              <SelectTrigger className="border-blue-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Types</SelectItem>
                <SelectItem value="CONSULTATION">Consultation</SelectItem>
                <SelectItem value="TREATMENT">Treatment</SelectItem>
                <SelectItem value="FOLLOW_UP">Follow Up</SelectItem>
                <SelectItem value="EMERGENCY">Emergency</SelectItem>
              </SelectContent>
            </Select>

            {/* Date Filter */}
            <Select value={dateFilter} onValueChange={handleDateFilterChange}>
              <SelectTrigger className="border-blue-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Dates</SelectItem>
                <SelectItem value="TODAY">Today</SelectItem>
                <SelectItem value="UPCOMING">Upcoming</SelectItem>
                <SelectItem value="PAST">Past</SelectItem>
              </SelectContent>
            </Select>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
            <p className="text-sm text-gray-600">
              Showing {appointments.length} of {totalCount} appointments
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearFilters}
              className="text-blue-600 border-blue-200 hover:bg-blue-50 w-full sm:w-auto"
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>
      )}

      {/* Loading State */}
      {loading && appointments.length === 0 && (
        <AppointmentListSkeleton count={4} />
      )}

      {/* Empty State */}
      {!loading && appointments.length === 0 && (
        <Card className="p-12 text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No appointments found
          </h3>
          <p className="text-gray-600 mb-4">
            {Object.keys(filters).length > 0
              ? "Try adjusting your filters to see more appointments."
              : "You don't have any appointments scheduled yet."
            }
          </p>
        </Card>
      )}

      {/* Appointments List */}
      {!loading && appointments.length > 0 && (
        <div className="space-y-6">
          {Object.entries(appointmentGroups).map(([date, dateAppointments]) => (
            <div key={date} className="space-y-4">
              <div className="flex items-center justify-between border-b border-gray-200 pb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  {formatAppointmentDate(new Date(date))}
                </h3>
                <Badge variant="outline" className="text-xs">
                  {dateAppointments.length} appointment{dateAppointments.length !== 1 ? 's' : ''}
                </Badge>
              </div>

              <div className="grid gap-4">
                {dateAppointments.map((appointment) => (
                  <AppointmentCard
                    key={appointment.id}
                    appointment={appointment}
                    onViewDetails={onViewDetails}
                    onStatusUpdate={handleOpenStatusDialog}
                    isUpdating={updatingAppointmentId === appointment.id}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0 w-full">
              <p className="text-sm text-gray-600 order-2 sm:order-1">
                Page {currentPage} of {totalPages}
              </p>
              <div className="flex space-x-2 w-full sm:w-auto order-1 sm:order-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadPrevPage}
                  disabled={!hasPrevPage || loading}
                  className="border-blue-200 text-blue-600 hover:bg-blue-50 flex-1 sm:flex-none"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">Previous</span>
                  <span className="sm:hidden">Prev</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadNextPage}
                  disabled={!hasNextPage || loading}
                  className="border-blue-200 text-blue-600 hover:bg-blue-50 flex-1 sm:flex-none"
                >
                  <span className="hidden sm:inline">Next</span>
                  <span className="sm:hidden">Next</span>
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Status Management Dialog */}
      <AppointmentStatusDialog
        appointment={statusDialogAppointment}
        isOpen={!!statusDialogAppointment}
        onClose={handleCloseStatusDialog}
        onStatusUpdate={handleStatusUpdate}
      />
    </div>
  )
}

// Helper functions
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

const getStatusDisplay = (status: AppointmentStatus) => {
  switch (status) {
    case 'SCHEDULED':
      return {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: <Clock className="h-3 w-3" />,
        label: 'Scheduled'
      }
    case 'CONFIRMED':
      return {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: <CheckCircle className="h-3 w-3" />,
        label: 'Confirmed'
      }
    case 'IN_PROGRESS':
      return {
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: <AlertCircle className="h-3 w-3" />,
        label: 'In Progress'
      }
    case 'COMPLETED':
      return {
        color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
        icon: <Check className="h-3 w-3" />,
        label: 'Completed'
      }
    case 'CANCELLED':
      return {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: <XCircle className="h-3 w-3" />,
        label: 'Cancelled'
      }
    case 'NO_SHOW':
      return {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: <X className="h-3 w-3" />,
        label: 'No Show'
      }
    default:
      return {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: <AlertCircle className="h-3 w-3" />,
        label: status
      }
  }
}

// Individual Appointment Card Component
interface AppointmentCardProps {
  appointment: AppointmentDetails
  onViewDetails?: (appointmentId: string) => void
  onStatusUpdate?: (appointment: AppointmentDetails) => void
  isUpdating?: boolean
}

function AppointmentCard({ appointment, onViewDetails, onStatusUpdate, isUpdating = false }: AppointmentCardProps) {
  const statusDisplay = getStatusDisplay(appointment.status)

  const handleStatusUpdate = () => {
    onStatusUpdate?.(appointment)
  }

  return (
    <Card className={`hover:shadow-md transition-shadow duration-200 border-l-4 border-l-blue-500 ${isUpdating ? 'opacity-75' : ''} relative`}>
      {isUpdating && (
        <div className="absolute inset-0 bg-white/50 rounded-lg flex items-center justify-center z-10">
          <div className="flex items-center space-x-2 text-blue-600">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
            <span className="text-sm font-medium">Updating...</span>
          </div>
        </div>
      )}
      <CardContent className="p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between space-y-4 lg:space-y-0">
          {/* Left side - Patient info and appointment details */}
          <div className="flex-1 space-y-4">
            {/* Patient Info */}
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10 sm:h-12 sm:w-12 border-2 border-blue-100">
                <AvatarFallback className="bg-blue-50 text-blue-700 font-medium">
                  {getInitials(appointment.patient.user.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-gray-900 truncate">{appointment.patient.user.name}</h4>
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0 text-sm text-gray-600">
                  {appointment.patient.user.phone && (
                    <div className="flex items-center space-x-1">
                      <Phone className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{appointment.patient.user.phone}</span>
                    </div>
                  )}
                  {appointment.patient.user.email && (
                    <div className="flex items-center space-x-1">
                      <User className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{appointment.patient.user.email}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Appointment Details */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Time:</span>
                  <span>{formatAppointmentTime(appointment.scheduledAt)}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Duration:</span>
                  <span>{formatDuration(appointment.duration)}</span>
                </div>
                {appointment.branch && (
                  <div className="flex items-center space-x-2 text-sm">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Branch:</span>
                    <span>{appointment.branch.name}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <span className="font-medium">Service:</span>
                  <span>{appointment.service.name}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <span className="font-medium">Price:</span>
                  <span className="text-green-600 font-semibold">
                    {formatPrice(appointment.service.price)}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <span className="font-medium">Type:</span>
                  <Badge variant="outline" className="text-xs">
                    {appointment.type.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Notes */}
            {appointment.notes && (
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Notes:</span> {appointment.notes}
                </p>
              </div>
            )}
          </div>

          {/* Right side - Status and actions */}
          <div className="flex flex-col lg:items-end space-y-3 lg:ml-6">
            {/* Status Badge */}
            <div className="flex justify-start lg:justify-end">
              <Badge className={`${statusDisplay.color} flex items-center space-x-1`}>
                {statusDisplay.icon}
                <span>{statusDisplay.label}</span>
              </Badge>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full lg:w-auto">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails?.(appointment.id)}
                disabled={isUpdating}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 w-full sm:w-auto disabled:opacity-50"
              >
                <Eye className="h-3 w-3 mr-1" />
                View
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleStatusUpdate}
                disabled={isUpdating}
                className="border-blue-200 text-blue-600 hover:bg-blue-50 w-full sm:w-auto disabled:opacity-50"
              >
                {isUpdating ? (
                  <>
                    <div className="h-3 w-3 mr-1 animate-spin rounded-full border border-blue-600 border-t-transparent" />
                    Updating...
                  </>
                ) : (
                  <>
                    <MoreVertical className="h-3 w-3 mr-1" />
                    Manage Status
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}