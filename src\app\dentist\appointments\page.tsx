'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Loader2, Calendar, Clock, Plus, TrendingUp, CheckCircle, AlertCircle } from 'lucide-react'
import { UserRole, AppointmentStatus } from '@prisma/client'
import { DentistAppointmentList } from '@/components/dentist/appointment-list'
import { useAppointments } from '@/hooks/useAppointments'

export default function DentistAppointments() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [dentistProfileId, setDentistProfileId] = useState<string | null>(null)

  // Get dentist profile ID
  useEffect(() => {
    const fetchDentistProfile = async () => {
      if (user?.id) {
        try {
          const response = await fetch(`/api/dentists/profile?userId=${user.id}`)
          if (response.ok) {
            const profile = await response.json()
            setDentistProfileId(profile.id)
          } else {
            console.error('Failed to fetch dentist profile:', response.status, response.statusText)
            // Handle specific error cases
            if (response.status === 404) {
              console.error('Dentist profile not found for user:', user.id)
              // You might want to redirect to profile setup or show an error message
            }
            // Set a placeholder or error state to prevent infinite loading
            setDentistProfileId('error')
          }
        } catch (error) {
          console.error('Failed to fetch dentist profile:', error)
          // Set error state to prevent infinite loading
          setDentistProfileId('error')
        }
      }
    }

    if (isAuthenticated && user?.role === UserRole.DENTIST) {
      fetchDentistProfile()
    }
  }, [user, isAuthenticated])

  // Get appointment statistics
  const {
    appointments,
    loading: statsLoading,
    getTodaysAppointments,
    getUpcomingAppointments,
    getAppointmentsByStatus
  } = useAppointments({
    dentistId: dentistProfileId || undefined,
    autoRefresh: true,
    refreshInterval: 30000
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/appointments')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  const handleViewDetails = (appointmentId: string) => {
    router.push(`/dentist/appointments/${appointmentId}`)
  }

  const handleNewAppointment = () => {
    router.push('/appointments/book')
  }

  const handleStatusUpdate = (appointmentId: string, status: AppointmentStatus, reason?: string) => {
    // Handle status update notifications or additional logic here
    console.log(`Appointment ${appointmentId} updated to ${status}`, reason)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  if (!dentistProfileId) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Setting up your profile...
            </h3>
            <p className="text-gray-600">
              Please wait while we load your dentist profile.
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (dentistProfileId === 'error') {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center max-w-md">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Profile Setup Required
            </h3>
            <p className="text-gray-600 mb-4">
              We couldn&apos;t find your dentist profile. This usually happens when:
            </p>
            <ul className="text-sm text-gray-500 mb-6 text-left space-y-1">
              <li>• Your account hasn&apos;t been set up as a dentist yet</li>
              <li>• Your profile is still being processed by the administrator</li>
              <li>• There was a temporary database issue</li>
            </ul>
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => router.push('/dashboard')}
                className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Go to Dashboard
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-4">
              If this problem persists, please contact your system administrator.
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Calculate statistics
  const todaysAppointments = getTodaysAppointments()
  const upcomingAppointments = getUpcomingAppointments()
  const completedAppointments = getAppointmentsByStatus('COMPLETED')
  const confirmedAppointments = getAppointmentsByStatus('CONFIRMED')

  // Calculate this week's appointments
  const startOfWeek = new Date()
  startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay())
  startOfWeek.setHours(0, 0, 0, 0)

  const endOfWeek = new Date(startOfWeek)
  endOfWeek.setDate(endOfWeek.getDate() + 6)
  endOfWeek.setHours(23, 59, 59, 999)

  const thisWeekAppointments = appointments.filter(appointment => {
    const appointmentDate = new Date(appointment.scheduledAt)
    return appointmentDate >= startOfWeek && appointmentDate <= endOfWeek
  })

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Appointments</h1>
          <p className="text-gray-600 mt-1">
            Manage your patient appointments and schedule
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            size="sm"
            onClick={handleNewAppointment}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Appointment
          </Button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="group hover:shadow-lg hover:shadow-blue-100/50 transition-all duration-300 hover:-translate-y-1 border-blue-100/50 bg-gradient-to-br from-blue-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors">
              Today&apos;s Appointments
            </CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
              <Calendar className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-blue-600">
                {statsLoading ? '...' : todaysAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-blue-200 text-blue-700">
                Today
              </Badge>
            </div>
            <p className="text-xs text-blue-600">
              {confirmedAppointments.filter(apt =>
                new Date(apt.scheduledAt).toDateString() === new Date().toDateString()
              ).length} confirmed
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-green-100/50 transition-all duration-300 hover:-translate-y-1 border-green-100/50 bg-gradient-to-br from-green-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-green-700 transition-colors">
              This Week
            </CardTitle>
            <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
              <Clock className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-green-600">
                {statsLoading ? '...' : thisWeekAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-green-200 text-green-700">
                7 days
              </Badge>
            </div>
            <p className="text-xs text-green-600">Scheduled appointments</p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-purple-100/50 transition-all duration-300 hover:-translate-y-1 border-purple-100/50 bg-gradient-to-br from-purple-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-purple-700 transition-colors">
              Upcoming
            </CardTitle>
            <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
              <TrendingUp className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-purple-600">
                {statsLoading ? '...' : upcomingAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-purple-200 text-purple-700">
                Future
              </Badge>
            </div>
            <p className="text-xs text-purple-600">Future appointments</p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-emerald-100/50 transition-all duration-300 hover:-translate-y-1 border-emerald-100/50 bg-gradient-to-br from-emerald-50/50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-gray-700 group-hover:text-emerald-700 transition-colors">
              Completed
            </CardTitle>
            <div className="p-2 bg-emerald-100 rounded-lg group-hover:bg-emerald-200 transition-colors">
              <CheckCircle className="h-4 w-4 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-baseline gap-2">
              <div className="text-3xl font-bold text-emerald-600">
                {statsLoading ? '...' : completedAppointments.length}
              </div>
              <Badge variant="outline" className="text-xs border-emerald-200 text-emerald-700">
                Done
              </Badge>
            </div>
            <p className="text-xs text-emerald-600">Completed treatments</p>
          </CardContent>
        </Card>
      </div>

      {/* Appointment List */}
      <DentistAppointmentList
        dentistId={dentistProfileId}
        onViewDetails={handleViewDetails}
        onUpdateStatus={handleStatusUpdate}
      />
    </div>
  )
}
