import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { UserRole, ToothCondition } from '@prisma/client'
import { z } from 'zod'

const toothRecordUpdateSchema = z.object({
  toothNumber: z.number().min(1).max(32),
  condition: z.nativeEnum(ToothCondition),
  notes: z.string().optional(),
})

const bulkToothUpdateSchema = z.object({
  updates: z.array(toothRecordUpdateSchema),
})

// PUT /api/dental-charts/[id]/teeth - Update tooth records
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  // Check if it's a single tooth update or bulk update
  const isBulkUpdate = Array.isArray(body.updates)
  
  const dentalChart = await prisma.dentalChart.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
      teeth: true,
    },
  })
  
  if (!dentalChart) {
    return NextResponse.json(
      { error: 'Dental chart not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, dentalChart.patientId, user.id)) {
    // Also allow dentist access for treatment purposes
    if (user.role !== UserRole.DENTIST) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }
  }
  
  if (isBulkUpdate) {
    // Handle bulk update
    const validatedData = bulkToothUpdateSchema.parse(body)
    
    const updatePromises = validatedData.updates.map(async (update) => {
      return prisma.toothRecord.upsert({
        where: {
          dentalChartId_toothNumber: {
            dentalChartId: id,
            toothNumber: update.toothNumber,
          },
        },
        update: {
          condition: update.condition,
          notes: update.notes,
          lastUpdated: new Date(),
        },
        create: {
          dentalChartId: id,
          toothNumber: update.toothNumber,
          condition: update.condition,
          notes: update.notes,
        },
      })
    })
    
    const updatedTeeth = await Promise.all(updatePromises)
    
    // Update dental chart timestamp
    await prisma.dentalChart.update({
      where: { id },
      data: { updatedAt: new Date() },
    })
    
    return NextResponse.json({
      message: 'Tooth records updated successfully',
      updatedTeeth,
    })
  } else {
    // Handle single tooth update
    const validatedData = toothRecordUpdateSchema.parse(body)
    
    const updatedTooth = await prisma.toothRecord.upsert({
      where: {
        dentalChartId_toothNumber: {
          dentalChartId: id,
          toothNumber: validatedData.toothNumber,
        },
      },
      update: {
        condition: validatedData.condition,
        notes: validatedData.notes,
        lastUpdated: new Date(),
      },
      create: {
        dentalChartId: id,
        toothNumber: validatedData.toothNumber,
        condition: validatedData.condition,
        notes: validatedData.notes,
      },
    })
    
    // Update dental chart timestamp
    await prisma.dentalChart.update({
      where: { id },
      data: { updatedAt: new Date() },
    })
    
    return NextResponse.json(updatedTooth)
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])

// GET /api/dental-charts/[id]/teeth - Get all teeth for a dental chart
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  const dentalChart = await prisma.dentalChart.findUnique({
    where: { id },
    include: {
      patient: { include: { user: true } },
    },
  })
  
  if (!dentalChart) {
    return NextResponse.json(
      { error: 'Dental chart not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, dentalChart.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const teeth = await prisma.toothRecord.findMany({
    where: { dentalChartId: id },
    orderBy: { toothNumber: 'asc' },
  })
  
  return NextResponse.json({
    dentalChartId: id,
    patientId: dentalChart.patientId,
    patient: {
      name: dentalChart.patient.user.name,
      email: dentalChart.patient.user.email,
    },
    teeth,
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
