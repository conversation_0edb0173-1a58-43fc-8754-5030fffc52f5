'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { AppointmentList } from '@/components/appointments/appointment-list'
import { RescheduleAppointmentModal } from '@/components/appointments/modals/reschedule-appointment-modal'
import { CancelAppointmentModal } from '@/components/appointments/modals/cancel-appointment-modal'
import {
  Calendar,
  Clock,
  FileText,
  CreditCard,
  Plus,
  Bell,
  Settings,
  Loader2,
  User,
  Shield,
  BellRing,
  Lock
} from 'lucide-react'
import { AppointmentDetails, DashboardStats } from '@/types/appointment'
import { UserRole } from '@prisma/client'

export default function PatientDashboard() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoadingStats, setIsLoadingStats] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Modal states
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentDetails | null>(null)
  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false)
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dashboard')
    } else if (isAuthenticated && user?.role !== UserRole.PATIENT) {
      router.push('/')
    } else if (isAuthenticated) {
      fetchDashboardStats()
    }
  }, [isAuthenticated, isLoading, user, router])

  const fetchDashboardStats = async () => {
    try {
      setIsLoadingStats(true)

      // Get current date in ISO format for filtering future appointments
      const now = new Date().toISOString()

      // Fetch upcoming appointments (future appointments with active statuses)
      const appointmentsRes = await fetch(`/api/appointments?limit=5&status=SCHEDULED,CONFIRMED,PENDING&dateFrom=${now}&sortBy=scheduledAt&sortOrder=asc`)
      const appointmentsData = await appointmentsRes.json()

      // Fetch completed appointments count
      const completedRes = await fetch('/api/appointments?status=COMPLETED&limit=1')
      const completedData = await completedRes.json()

      // Calculate stats
      const upcomingAppointments = appointmentsData.pagination?.totalCount || 0
      const completedAppointments = completedData.pagination?.totalCount || 0
      const nextAppointment = appointmentsData.appointments?.[0] || undefined

      // TODO: Calculate total spent from invoices/payments
      const totalSpent = 0

      setStats({
        upcomingAppointments,
        completedAppointments,
        totalSpent,
        nextAppointment,
      })
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      setError('Failed to load dashboard data')
    } finally {
      setIsLoadingStats(false)
    }
  }

  const handleBookAppointment = () => {
    router.push('/appointments/book')
  }

  const handleReschedule = (appointment: AppointmentDetails) => {
    setSelectedAppointment(appointment)
    setIsRescheduleModalOpen(true)
  }

  const handleCancel = (appointment: AppointmentDetails) => {
    setSelectedAppointment(appointment)
    setIsCancelModalOpen(true)
  }

  const handleViewDetails = (appointmentId: string) => {
    router.push(`/appointments/${appointmentId}`)
  }

  const handleModalSuccess = () => {
    fetchDashboardStats()
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(price)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
    })
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  if (isLoading || isLoadingStats) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600">Loading your dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.PATIENT) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome back, {user?.name?.split(' ')[0] || 'Patient'}!
              </h1>
              <p className="text-gray-600 mt-1">
                Manage your appointments and dental health
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem onClick={() => router.push('/settings/profile')}>
                    <User className="h-4 w-4 mr-2" />
                    Profile Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => router.push('/settings/account')}>
                    <Shield className="h-4 w-4 mr-2" />
                    Account Settings
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => router.push('/settings/notifications')}>
                    <BellRing className="h-4 w-4 mr-2" />
                    Notification Preferences
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => router.push('/settings/privacy')}>
                    <Lock className="h-4 w-4 mr-2" />
                    Privacy Settings
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming Appointments</CardTitle>
              <Calendar className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats?.upcomingAppointments || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Visits</CardTitle>
              <FileText className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats?.completedAppointments || 0}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <CreditCard className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {formatPrice(stats?.totalSpent || 0)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quick Action</CardTitle>
              <Plus className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <Button 
                onClick={handleBookAppointment}
                className="w-full bg-blue-600 hover:bg-blue-700"
                size="sm"
              >
                Book Appointment
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Quick Navigation */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/appointments/upcoming')}>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Upcoming Appointments</h3>
                  <p className="text-sm text-gray-600">View and manage scheduled visits</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/appointments/history')}>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Appointment History</h3>
                  <p className="text-sm text-gray-600">Review past treatments</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/appointments/book')}>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Plus className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Book New Appointment</h3>
                  <p className="text-sm text-gray-600">Schedule your next visit</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Next Appointment */}
        {stats?.nextAppointment && (
          <Card className="mb-8 border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-lg text-blue-900 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Your Next Appointment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h4 className="font-medium text-blue-900">
                    {stats.nextAppointment.service.name}
                  </h4>
                  <p className="text-blue-700">
                    Dr. {stats.nextAppointment.dentist.user.name}
                  </p>
                  <p className="text-blue-600 text-sm">
                    {formatDate(new Date(stats.nextAppointment.scheduledAt))} at {formatTime(new Date(stats.nextAppointment.scheduledAt))}
                  </p>
                  <p className="text-blue-600 text-sm">
                    {stats.nextAppointment.branch.name}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleReschedule(stats.nextAppointment!)}
                    className="border-blue-300 text-blue-700 hover:bg-blue-100"
                  >
                    Reschedule
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleViewDetails(stats.nextAppointment!.id)}
                    className="border-blue-300 text-blue-700 hover:bg-blue-100"
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Appointments List */}
        <AppointmentList
          onReschedule={handleReschedule}
          onCancel={handleCancel}
          onViewDetails={handleViewDetails}
          onBookNew={handleBookAppointment}
          showActions={true}
        />

        {/* Modals */}
        <RescheduleAppointmentModal
          isOpen={isRescheduleModalOpen}
          onClose={() => setIsRescheduleModalOpen(false)}
          appointment={selectedAppointment}
          onSuccess={handleModalSuccess}
        />

        <CancelAppointmentModal
          isOpen={isCancelModalOpen}
          onClose={() => setIsCancelModalOpen(false)}
          appointment={selectedAppointment}
          onSuccess={handleModalSuccess}
        />
      </div>
    </div>
  )
}
