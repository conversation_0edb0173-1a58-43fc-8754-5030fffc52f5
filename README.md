# Dental Clinic Management System

A comprehensive dental clinic management system built with Next.js, TypeScript, Prisma, and PostgreSQL. This system provides complete functionality for managing patients, appointments, treatments, billing, inventory, and more.

## 🚀 Features

### Core Functionality
- **Multi-role Authentication**: Patients, Dentists, Staff, and Admins with role-based access control
- **Patient Management**: Complete patient profiles with medical history and dental charts
- **Appointment Scheduling**: Advanced booking system with availability checking and conflict resolution
- **Treatment Records**: Comprehensive treatment tracking with SOAP notes and prescriptions
- **Billing & Payments**: Invoice generation and multi-gateway payment processing
- **Inventory Management**: Stock tracking with low-stock alerts and usage logging
- **File Management**: Secure file upload/download for patient documents and X-rays
- **Notifications**: Multi-channel notifications (email, SMS, in-app) for appointments and reminders
- **Analytics & Reporting**: Comprehensive dashboard and detailed reports
- **Audit Logging**: Complete audit trail for security and compliance

### Technical Features
- **RESTful API**: Well-structured API endpoints with proper HTTP status codes
- **Database**: Normalized PostgreSQL schema with Prisma ORM
- **Authentication**: NextAuth.js with JWT tokens and Google OAuth for patients
- **Security**: Rate limiting, input validation, XSS protection, and audit logging
- **Testing**: Comprehensive unit and integration tests
- **Documentation**: Complete API documentation and setup guides

## 🛠️ Technology Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL (Neon.tech recommended)
- **Authentication**: NextAuth.js with JWT and Google OAuth
- **File Storage**: UploadThing (configurable)
- **Payments**: Stripe, PayMongo, GCash, PayMaya integration ready
- **Communications**: Twilio (SMS), SendGrid (Email)
- **Testing**: Jest, Supertest
- **Deployment**: Vercel ready

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL database (local or cloud)
- npm or yarn package manager

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd dental-clinic
npm install
```

### 2. Environment Setup

Copy the environment template:
```bash
cp .env.example .env
```

Update `.env` with your configuration:
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/dental_clinic"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Google OAuth (for patient login)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# JWT
JWT_SECRET="your-jwt-secret-here"

# Optional: Email & SMS
SENDGRID_API_KEY="your-sendgrid-api-key"
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"

# Optional: Payment Gateways
STRIPE_SECRET_KEY="your-stripe-secret-key"
PAYMONGO_SECRET_KEY="your-paymongo-secret-key"
```

### 3. Database Setup

Generate Prisma client:
```bash
npm run db:generate
```

Run database migrations:
```bash
npm run db:migrate
```

Seed the database with initial data:
```bash
npm run db:seed
```

### 4. Start Development Server

```bash
npm run dev
```

Visit `http://localhost:3000` to access the application.

## 🔐 Default Login Credentials

After running the database seed script:

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

⚠️ **Important**: Change the default password immediately after first login!

## 📚 API Documentation

The API provides comprehensive endpoints for all system functionality. See [API_DOCUMENTATION.md](./API_DOCUMENTATION.md) for complete details.

### Base URL
```
http://localhost:3000/api
```

### Authentication
Most endpoints require authentication via JWT tokens:
```
Authorization: Bearer <jwt_token>
```

### Key Endpoints
- `GET /api/users` - User management
- `GET /api/patients` - Patient management
- `GET /api/appointments` - Appointment scheduling
- `GET /api/treatments` - Treatment records
- `GET /api/invoices` - Billing and invoices
- `GET /api/payments` - Payment processing
- `GET /api/inventory` - Inventory management
- `GET /api/analytics/dashboard` - Analytics and reporting

## 🧪 Testing

Run the test suite:
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Type checking
npm run typecheck

# Linting
npm run lint
```

## 🏗️ Project Structure

```
dental-clinic/
├── src/
│   ├── app/
│   │   ├── api/           # API routes
│   │   ├── auth/          # Authentication pages
│   │   └── ...            # Other pages
│   ├── lib/
│   │   ├── auth.ts        # NextAuth configuration
│   │   ├── prisma.ts      # Prisma client
│   │   ├── validations.ts # Zod schemas
│   │   └── ...            # Utility functions
│   ├── generated/
│   │   └── prisma/        # Generated Prisma client
│   └── __tests__/         # Test files
├── prisma/
│   ├── schema.prisma      # Database schema
│   └── migrations/        # Database migrations
├── scripts/
│   └── setup-database.ts  # Database seeding script
└── ...
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run typecheck` - Type checking
- `npm run db:generate` - Generate Prisma client
- `npm run db:migrate` - Run database migrations
- `npm run db:push` - Push schema to database
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with initial data

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on push

### Environment Variables for Production

Ensure all required environment variables are set:
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - Strong secret for NextAuth.js
- `NEXTAUTH_URL` - Your production URL
- `JWT_SECRET` - Strong secret for JWT tokens
- OAuth and payment gateway credentials as needed

## 🔒 Security Features

- **Authentication**: Multi-provider authentication with NextAuth.js
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: API rate limiting to prevent abuse
- **Input Validation**: Comprehensive input validation with Zod
- **SQL Injection Protection**: Prisma ORM prevents SQL injection
- **XSS Protection**: Input sanitization and output encoding
- **Audit Logging**: Complete audit trail for all actions
- **HTTPS**: Enforced in production

## 📊 Database Schema

The system uses a normalized PostgreSQL schema with the following key entities:

- **Users & Profiles**: Multi-role user system with specific profiles
- **Appointments**: Scheduling with conflict detection
- **Treatments**: Medical records with SOAP notes
- **Billing**: Invoices and payments with multiple gateways
- **Inventory**: Stock management with usage tracking
- **Files**: Secure file storage and management
- **Notifications**: Multi-channel notification system
- **Audit Logs**: Security and compliance tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
1. Check the API documentation
2. Review the test files for usage examples
3. Open an issue on GitHub
4. Contact the development team

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality
  - User management and authentication
  - Appointment scheduling
  - Treatment records
  - Billing and payments
  - Inventory management
  - Analytics and reporting
  - Security and audit logging

---

Built with ❤️ for dental professionals to streamline their practice management.
