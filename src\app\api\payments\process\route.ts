import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole, PaymentStatus, PaymentMethod } from '@prisma/client'
import { z } from 'zod'

const paymentProcessSchema = z.object({
  paymentId: z.string().cuid(),
  paymentMethodData: z.object({
    // Stripe
    stripePaymentIntentId: z.string().optional(),
    stripePaymentMethodId: z.string().optional(),
    
    // PayMongo
    paymongoSourceId: z.string().optional(),
    paymongoPaymentId: z.string().optional(),
    
    // GCash/PayMaya
    referenceNumber: z.string().optional(),
    
    // General
    transactionId: z.string().optional(),
    receiptUrl: z.string().optional(),
  }).optional(),
})

// POST /api/payments/process - Process payment with external gateway
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = paymentProcessSchema.parse(body)
  
  // Get payment details
  const payment = await prisma.payment.findUnique({
    where: { id: validatedData.paymentId },
    include: {
      invoice: {
        include: {
          patient: { include: { user: true } },
        },
      },
    },
  })
  
  if (!payment) {
    return NextResponse.json(
      { error: 'Payment not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  const canProcess = 
    user.role === UserRole.ADMIN ||
    user.role === UserRole.STAFF ||
    (user.role === UserRole.PATIENT && payment.invoice.patient.userId === user.id)
  
  if (!canProcess) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Check if payment is already processed
  if (payment.status === PaymentStatus.PAID) {
    return NextResponse.json(
      { error: 'Payment already processed' },
      { status: 400 }
    )
  }
  
  try {
    let processedPayment
    
    switch (payment.method) {
      case PaymentMethod.STRIPE:
        processedPayment = await processStripePayment(payment, validatedData.paymentMethodData || {})
        break

      case PaymentMethod.PAYMONGO:
        processedPayment = await processPayMongoPayment(payment, validatedData.paymentMethodData || {})
        break

      case PaymentMethod.GCASH:
        processedPayment = await processGCashPayment(payment, validatedData.paymentMethodData || {})
        break

      case PaymentMethod.PAYMAYA:
        processedPayment = await processPayMayaPayment(payment, validatedData.paymentMethodData || {})
        break
        
      case PaymentMethod.CASH:
        // Cash payments are processed immediately when created
        processedPayment = await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: PaymentStatus.PAID,
            processedAt: new Date(),
            transactionId: validatedData.paymentMethodData?.transactionId,
          },
        })
        break
        
      default:
        return NextResponse.json(
          { error: 'Unsupported payment method' },
          { status: 400 }
        )
    }
    
    // Update invoice payment status
    await updateInvoicePaymentStatus(payment.invoiceId)
    
    return NextResponse.json({
      success: true,
      payment: processedPayment,
    })
    
  } catch (error) {
    console.error('Payment processing error:', error)
    
    // Update payment status to failed
    await prisma.payment.update({
      where: { id: payment.id },
      data: {
        status: PaymentStatus.CANCELLED,
      },
    })
    
    return NextResponse.json(
      { error: 'Payment processing failed' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.PATIENT])

// Helper functions for different payment processors
async function processStripePayment(payment: { id: string }, methodData: Record<string, unknown>) {
  // TODO: Implement Stripe payment processing
  // This would involve:
  // 1. Confirming the payment intent
  // 2. Handling webhooks
  // 3. Updating payment status
  
  return await prisma.payment.update({
    where: { id: payment.id },
    data: {
      status: PaymentStatus.PAID,
      processedAt: new Date(),
      transactionId: methodData?.stripePaymentIntentId as string || null,
    },
  })
}

async function processPayMongoPayment(payment: { id: string }, methodData: Record<string, unknown>) {
  // TODO: Implement PayMongo payment processing
  // This would involve:
  // 1. Creating payment source
  // 2. Processing payment
  // 3. Handling webhooks
  
  return await prisma.payment.update({
    where: { id: payment.id },
    data: {
      status: PaymentStatus.PAID,
      processedAt: new Date(),
      transactionId: methodData?.paymongoPaymentId as string || null,
    },
  })
}

async function processGCashPayment(payment: { id: string }, methodData: Record<string, unknown>) {
  // TODO: Implement GCash payment processing
  // This would typically involve:
  // 1. Creating payment request
  // 2. Redirecting to GCash
  // 3. Handling callback
  
  return await prisma.payment.update({
    where: { id: payment.id },
    data: {
      status: PaymentStatus.PAID,
      processedAt: new Date(),
      transactionId: methodData?.referenceNumber as string || null,
    },
  })
}

async function processPayMayaPayment(payment: { id: string }, methodData: Record<string, unknown>) {
  // TODO: Implement PayMaya payment processing
  // Similar to GCash implementation
  
  return await prisma.payment.update({
    where: { id: payment.id },
    data: {
      status: PaymentStatus.PAID,
      processedAt: new Date(),
      transactionId: methodData?.referenceNumber as string || null,
    },
  })
}

async function updateInvoicePaymentStatus(invoiceId: string) {
  const invoice = await prisma.invoice.findUnique({
    where: { id: invoiceId },
    include: { payments: true },
  })

  if (!invoice) return

  const totalPaid = invoice.payments.reduce((sum, payment) => {
    return payment.status === PaymentStatus.PAID ? sum + Number(payment.amount) : sum
  }, 0)

  const remainingBalance = Number(invoice.totalAmount) - totalPaid

  let status: PaymentStatus = PaymentStatus.PENDING
  if (remainingBalance <= 0) {
    status = PaymentStatus.PAID
  } else if (totalPaid > 0) {
    status = PaymentStatus.PARTIAL
  }

  await prisma.invoice.update({
    where: { id: invoiceId },
    data: {
      status,
      paidAmount: totalPaid,
      paidAt: status === PaymentStatus.PAID ? new Date() : null,
    },
  })
}
