'use client'

import { useState, useEffect, useCallback } from 'react'
import { AppointmentCard } from './appointment-card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Search, Filter, Calendar, Plus } from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'

interface AppointmentListProps {
  onReschedule?: (appointment: AppointmentDetails) => void
  onCancel?: (appointment: AppointmentDetails) => void
  onViewDetails?: (appointmentId: string) => void
  onBookNew?: () => void
  showActions?: boolean
  patientId?: string
  statusFilter?: string
  sortBy?: string
  sortOrder?: string
}

export function AppointmentList({
  onReschedule,
  onCancel,
  onViewDetails,
  onBookNew,
  showActions = true,
  patientId,
  statusFilter: initialStatusFilter,
  sortBy: initialSortBy,
  sortOrder: initialSortOrder
}: AppointmentListProps) {
  const [appointments, setAppointments] = useState<AppointmentDetails[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>(initialStatusFilter || 'all')
  const [sortBy, setSortBy] = useState<string>(initialSortBy || 'scheduledAt')
  const [sortOrder, setSortOrder] = useState<string>(initialSortOrder || 'desc')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  const fetchAppointments = useCallback(async (reset = false) => {
    try {
      setIsLoading(true)
      setError(null)

      const currentPage = reset ? 1 : page
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        sortBy,
        sortOrder,
      })

      if (searchQuery) {
        params.append('query', searchQuery)
      }

      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      if (patientId) {
        params.append('patientId', patientId)
      }

      const response = await fetch(`/api/appointments?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch appointments')
      }

      const data = await response.json()

      if (reset) {
        setAppointments(data.appointments || [])
        setPage(1)
      } else {
        setAppointments(prev => [...prev, ...(data.appointments || [])])
      }

      setHasMore(data.pagination?.page < data.pagination?.totalPages)

      if (reset) {
        setPage(2)
      } else {
        setPage(prev => prev + 1)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load appointments')
    } finally {
      setIsLoading(false)
    }
  }, [statusFilter, sortBy, sortOrder, patientId, searchQuery, page])

  useEffect(() => {
    fetchAppointments(true)
  }, [statusFilter, sortBy, sortOrder, patientId]) // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchQuery !== '') {
        fetchAppointments(true)
      }
    }, 500)

    return () => clearTimeout(delayedSearch)
  }, [searchQuery]) // eslint-disable-line react-hooks/exhaustive-deps

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      fetchAppointments(false)
    }
  }

  const handleRefresh = () => {
    setSearchQuery('')
    setStatusFilter('all')
    fetchAppointments(true)
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        appointment.service.name.toLowerCase().includes(query) ||
        appointment.dentist.user.name.toLowerCase().includes(query) ||
        appointment.branch.name.toLowerCase().includes(query)
      )
    }
    return true
  })

  const groupAppointmentsByDate = (appointments: AppointmentDetails[]) => {
    const groups: Record<string, AppointmentDetails[]> = {}
    
    appointments.forEach(appointment => {
      const date = new Date(appointment.scheduledAt).toDateString()
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(appointment)
    })
    
    return groups
  }

  const appointmentGroups = groupAppointmentsByDate(filteredAppointments)

  if (isLoading && appointments.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading appointments...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Your Appointments</h2>
          <p className="text-gray-600">Manage your upcoming and past appointments</p>
        </div>
        {onBookNew && (
          <Button onClick={onBookNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Book New Appointment
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search appointments..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Appointments</SelectItem>
            <SelectItem value="SCHEDULED">Scheduled</SelectItem>
            <SelectItem value="CONFIRMED">Confirmed</SelectItem>
            <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
            <SelectItem value="COMPLETED">Completed</SelectItem>
            <SelectItem value="CANCELLED">Cancelled</SelectItem>
          </SelectContent>
        </Select>

        <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value: string) => {
          const [newSortBy, newSortOrder] = value.split('-')
          setSortBy(newSortBy)
          setSortOrder(newSortOrder as 'asc' | 'desc')
        }}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="scheduledAt-desc">Newest First</SelectItem>
            <SelectItem value="scheduledAt-asc">Oldest First</SelectItem>
            <SelectItem value="createdAt-desc">Recently Booked</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>
            {error}
            <Button variant="outline" size="sm" onClick={handleRefresh} className="ml-2">
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Empty State */}
      {!isLoading && filteredAppointments.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No appointments found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || statusFilter !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'You haven\'t booked any appointments yet'
            }
          </p>
          {onBookNew && !searchQuery && statusFilter === 'all' && (
            <Button onClick={onBookNew} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Book Your First Appointment
            </Button>
          )}
        </div>
      )}

      {/* Appointments List */}
      {Object.entries(appointmentGroups).map(([date, dateAppointments]) => (
        <div key={date} className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
            {new Date(date).toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              timeZone: 'Asia/Manila'
            })}
          </h3>
          <div className="grid gap-4">
            {dateAppointments.map((appointment) => (
              <AppointmentCard
                key={appointment.id}
                appointment={appointment}
                onReschedule={onReschedule}
                onCancel={onCancel}
                onViewDetails={onViewDetails}
                showActions={showActions}
              />
            ))}
          </div>
        </div>
      ))}

      {/* Load More */}
      {hasMore && filteredAppointments.length > 0 && (
        <div className="text-center pt-6">
          <Button
            variant="outline"
            onClick={handleLoadMore}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              'Load More Appointments'
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
