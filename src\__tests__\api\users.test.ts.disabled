import { NextRequest } from 'next/server'
import { GET, POST } from '@/app/api/users/route'
import { prisma } from '@/lib/prisma'
import { getToken } from 'next-auth/jwt'
import { UserRole } from '@/generated/prisma'

// Mock the dependencies
jest.mock('@/lib/prisma')
jest.mock('next-auth/jwt')

const mockPrisma = prisma as jest.Mocked<typeof prisma>
const mockGetToken = getToken as jest.MockedFunction<typeof getToken>

describe('/api/users', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/users', () => {
    it('should return users list for admin', async () => {
      // Mock authentication
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      // Mock database response
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          name: 'User One',
          role: UserRole.PATIENT,
          status: 'ACTIVE',
          createdAt: new Date(),
          updatedAt: new Date(),
          patientProfile: null,
          dentistProfile: null,
          staffProfile: null,
        },
      ]

      mockPrisma.user.findMany.mockResolvedValue(mockUsers)
      mockPrisma.user.count.mockResolvedValue(1)

      // Create request
      const request = new NextRequest('http://localhost:3000/api/users')
      request.headers.set('x-user-id', 'admin-id')
      request.headers.set('x-user-role', UserRole.ADMIN)

      // Call the API
      const response = await GET(request)
      const data = await response.json()

      // Assertions
      expect(response.status).toBe(200)
      expect(data.users).toHaveLength(1)
      expect(data.users[0].email).toBe('<EMAIL>')
      expect(data.pagination.total).toBe(1)
    })

    it('should return 403 for non-admin users', async () => {
      // Mock authentication as patient
      mockGetToken.mockResolvedValue({
        sub: 'patient-id',
        role: UserRole.PATIENT,
      } as any)

      const request = new NextRequest('http://localhost:3000/api/users')
      request.headers.set('x-user-id', 'patient-id')
      request.headers.set('x-user-role', UserRole.PATIENT)

      const response = await GET(request)

      expect(response.status).toBe(403)
    })

    it('should handle pagination parameters', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      mockPrisma.user.findMany.mockResolvedValue([])
      mockPrisma.user.count.mockResolvedValue(0)

      const request = new NextRequest('http://localhost:3000/api/users?page=2&limit=5')
      request.headers.set('x-user-id', 'admin-id')
      request.headers.set('x-user-role', UserRole.ADMIN)

      await GET(request)

      expect(mockPrisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 5, // (page 2 - 1) * limit 5
          take: 5,
        })
      )
    })
  })

  describe('POST /api/users', () => {
    it('should create a new user for admin', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      // Mock that user doesn't exist
      mockPrisma.user.findUnique.mockResolvedValue(null)

      // Mock user creation
      const mockCreatedUser = {
        id: 'new-user-id',
        email: '<EMAIL>',
        name: 'New User',
        role: UserRole.PATIENT,
        status: 'ACTIVE',
        createdAt: new Date(),
      }

      mockPrisma.user.create.mockResolvedValue(mockCreatedUser as any)
      mockPrisma.patientProfile.create.mockResolvedValue({} as any)

      const requestBody = {
        email: '<EMAIL>',
        name: 'New User',
        role: UserRole.PATIENT,
      }

      const request = new NextRequest('http://localhost:3000/api/users', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'admin-id',
          'x-user-role': UserRole.ADMIN,
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.email).toBe('<EMAIL>')
      expect(mockPrisma.user.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            email: '<EMAIL>',
            name: 'New User',
            role: UserRole.PATIENT,
          }),
        })
      )
    })

    it('should return 400 if user already exists', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      // Mock that user already exists
      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'existing-user',
        email: '<EMAIL>',
      } as any)

      const requestBody = {
        email: '<EMAIL>',
        name: 'Existing User',
        role: UserRole.PATIENT,
      }

      const request = new NextRequest('http://localhost:3000/api/users', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'admin-id',
          'x-user-role': UserRole.ADMIN,
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('User with this email already exists')
    })

    it('should validate request body', async () => {
      mockGetToken.mockResolvedValue({
        sub: 'admin-id',
        role: UserRole.ADMIN,
      } as any)

      const invalidRequestBody = {
        email: 'invalid-email', // Invalid email format
        name: '', // Empty name
        role: 'INVALID_ROLE', // Invalid role
      }

      const request = new NextRequest('http://localhost:3000/api/users', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': 'admin-id',
          'x-user-role': UserRole.ADMIN,
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation failed')
      expect(data.details).toBeDefined()
    })
  })
})
