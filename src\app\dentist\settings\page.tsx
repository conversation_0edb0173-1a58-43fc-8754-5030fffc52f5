'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { UserRole } from '@prisma/client'
import { 
  Settings, 
  User,
  Bell,
  Shield,
  Calendar,
  CreditCard,
  Palette,
  Save,
  Loader2
} from 'lucide-react'

export default function DentistSettings() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/dentist/settings')
    } else if (isAuthenticated && user?.role !== UserRole.DENTIST) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, user, router])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (!isAuthenticated || user?.role !== UserRole.DENTIST) {
    return null
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
            <p className="text-gray-600 mt-1">
              Manage your account preferences and profile information
            </p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" size="sm">
              Reset to Default
            </Button>
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Profile Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2 text-blue-600" />
              Profile Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <User className="h-8 w-8 text-gray-400 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 mb-2">Profile Management</h4>
              <p className="text-sm text-gray-600 mb-4">
                Update your personal information, credentials, and professional details.
              </p>
              <Button variant="outline" size="sm">Configure Profile</Button>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2 text-green-600" />
              Notifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Bell className="h-8 w-8 text-gray-400 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 mb-2">Notification Preferences</h4>
              <p className="text-sm text-gray-600 mb-4">
                Manage email, SMS, and in-app notification settings.
              </p>
              <Button variant="outline" size="sm">Manage Notifications</Button>
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-purple-600" />
              Security & Privacy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Shield className="h-8 w-8 text-gray-400 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 mb-2">Security Settings</h4>
              <p className="text-sm text-gray-600 mb-4">
                Password, two-factor authentication, and privacy controls.
              </p>
              <Button variant="outline" size="sm">Security Settings</Button>
            </div>
          </CardContent>
        </Card>

        {/* Schedule Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-orange-600" />
              Schedule Preferences
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 mb-2">Schedule Configuration</h4>
              <p className="text-sm text-gray-600 mb-4">
                Default working hours, appointment durations, and availability.
              </p>
              <Button variant="outline" size="sm">Configure Schedule</Button>
            </div>
          </CardContent>
        </Card>

        {/* Billing Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2 text-red-600" />
              Billing Preferences
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <CreditCard className="h-8 w-8 text-gray-400 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 mb-2">Billing Configuration</h4>
              <p className="text-sm text-gray-600 mb-4">
                Payment methods, invoice templates, and billing preferences.
              </p>
              <Button variant="outline" size="sm">Billing Settings</Button>
            </div>
          </CardContent>
        </Card>

        {/* Interface Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="h-5 w-5 mr-2 text-pink-600" />
              Interface & Display
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Palette className="h-8 w-8 text-gray-400 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 mb-2">Display Preferences</h4>
              <p className="text-sm text-gray-600 mb-4">
                Theme, language, timezone, and interface customization.
              </p>
              <Button variant="outline" size="sm">Display Settings</Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Settings Placeholder */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Settings Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Settings System Coming Soon
            </h3>
            <p className="text-gray-600 mb-6">
              This feature will provide comprehensive settings and preference management.
            </p>
            <div className="space-y-2 text-sm text-gray-500">
              <p>• Personal profile and credential management</p>
              <p>• Notification and communication preferences</p>
              <p>• Security settings and two-factor authentication</p>
              <p>• Schedule and availability configuration</p>
              <p>• Billing and payment preferences</p>
              <p>• Interface customization and themes</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
