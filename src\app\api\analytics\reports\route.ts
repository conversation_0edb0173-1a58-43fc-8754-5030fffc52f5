import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canViewReports } from '@/lib/auth-utils'
import { UserRole, PaymentStatus } from '@prisma/client'
import { z } from 'zod'

const reportQuerySchema = z.object({
  type: z.enum(['revenue', 'appointments', 'patients', 'treatments', 'dentist-performance']),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)),
  branchId: z.string().cuid().optional(),
  dentistId: z.string().cuid().optional(),
  groupBy: z.enum(['day', 'week', 'month']).optional().default('day'),
})

// GET /api/analytics/reports - Generate detailed reports
export const GET = withAuth(async (req: NextRequest, user) => {
  if (!canViewReports(user.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }
  
  const { searchParams } = new URL(req.url)
  
  try {
    const validatedQuery = reportQuerySchema.parse({
      type: searchParams.get('type'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      branchId: searchParams.get('branchId') || undefined,
      dentistId: searchParams.get('dentistId') || undefined,
      groupBy: searchParams.get('groupBy') || 'day',
    })
    
    const { type, startDate, endDate, branchId, dentistId, groupBy } = validatedQuery
    
    let reportData
    
    switch (type) {
      case 'revenue':
        reportData = await generateRevenueReport(startDate, endDate, branchId)
        break
      case 'appointments':
        reportData = await generateAppointmentsReport(startDate, endDate, branchId, dentistId)
        break
      case 'patients':
        reportData = await generatePatientsReport(startDate, endDate)
        break
      case 'treatments':
        reportData = await generateTreatmentsReport(startDate, endDate, branchId, dentistId)
        break
      case 'dentist-performance':
        reportData = await generateDentistPerformanceReport(startDate, endDate, branchId, dentistId)
        break
      default:
        return NextResponse.json(
          { error: 'Invalid report type' },
          { status: 400 }
        )
    }
    
    return NextResponse.json({
      type,
      period: { startDate, endDate },
      filters: { branchId, dentistId },
      groupBy,
      data: reportData,
      generatedAt: new Date(),
    })
  } catch (error) {
    console.error('Report generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate report' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.DENTIST])

// Revenue report
async function generateRevenueReport(startDate: Date, endDate: Date, branchId?: string) {
  const payments = await prisma.payment.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: PaymentStatus.PAID,
      ...(branchId && {
        invoice: {
          patient: {
            appointments: {
              some: { branchId },
            },
          },
        },
      }),
    },
    include: {
      invoice: {
        include: {
          patient: {
            include: {
              user: { select: { name: true } },
            },
          },
        },
      },
    },
    orderBy: { createdAt: 'asc' },
  })
  
  // Group payments by date
  const groupedData = payments.reduce((acc, payment) => {
    const date = payment.createdAt.toISOString().split('T')[0]
    if (!acc[date]) {
      acc[date] = {
        revenue: 0,
        paymentCount: 0,
        payments: [],
      }
    }
    acc[date].revenue += Number(payment.amount)
    acc[date].paymentCount += 1
    acc[date].payments.push(payment)
    return acc
  }, {} as Record<string, { revenue: number; paymentCount: number; payments: typeof payments }>)
  
  return {
    summary: {
      totalRevenue: payments.reduce((sum, p) => sum + Number(p.amount), 0),
      totalPayments: payments.length,
      averagePayment: payments.length > 0 ? payments.reduce((sum, p) => sum + Number(p.amount), 0) / payments.length : 0,
    },
    timeline: Object.values(groupedData),
  }
}

// Appointments report
async function generateAppointmentsReport(startDate: Date, endDate: Date, branchId?: string, dentistId?: string) {
  const appointments = await prisma.appointment.findMany({
    where: {
      scheduledAt: {
        gte: startDate,
        lte: endDate,
      },
      ...(branchId && { branchId }),
      ...(dentistId && { dentistId }),
    },
    include: {
      patient: {
        include: {
          user: { select: { name: true } },
        },
      },
      dentist: {
        include: {
          user: { select: { name: true } },
        },
      },
      service: { select: { name: true, category: true } },
    },
    orderBy: { scheduledAt: 'asc' },
  })
  
  // Group by status
  const byStatus = appointments.reduce((acc, appointment) => {
    if (!acc[appointment.status]) {
      acc[appointment.status] = 0
    }
    acc[appointment.status] += 1
    return acc
  }, {} as Record<string, number>)
  
  // Group by service category
  const byCategory = appointments.reduce((acc, appointment) => {
    const category = appointment.service.category
    if (!acc[category]) {
      acc[category] = 0
    }
    acc[category] += 1
    return acc
  }, {} as Record<string, number>)
  
  return {
    summary: {
      totalAppointments: appointments.length,
      byStatus,
      byCategory,
    },
    appointments: appointments.slice(0, 100), // Limit for performance
  }
}

// Patients report
async function generatePatientsReport(startDate: Date, endDate: Date) {
  const patients = await prisma.patientProfile.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      user: {
        select: {
          name: true,
          email: true,
          createdAt: true,
        },
      },
      _count: {
        select: {
          appointments: true,
          treatments: true,
          invoices: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  })
  
  return {
    summary: {
      newPatients: patients.length,
      averageAppointmentsPerPatient: patients.length > 0 
        ? patients.reduce((sum, p) => sum + p._count.appointments, 0) / patients.length 
        : 0,
    },
    patients: patients.slice(0, 100),
  }
}

// Treatments report
async function generateTreatmentsReport(startDate: Date, endDate: Date, branchId?: string, dentistId?: string) {
  const treatments = await prisma.treatment.findMany({
    where: {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      ...(dentistId && { dentistId }),
    },
    include: {
      service: { select: { name: true, category: true } },
      patient: {
        include: {
          user: { select: { name: true } },
        },
      },
      dentist: {
        include: {
          user: { select: { name: true } },
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  })
  
  // Group by status
  const byStatus = treatments.reduce((acc, treatment) => {
    if (!acc[treatment.status]) {
      acc[treatment.status] = 0
    }
    acc[treatment.status] += 1
    return acc
  }, {} as Record<string, number>)
  
  return {
    summary: {
      totalTreatments: treatments.length,
      totalRevenue: treatments.reduce((sum, t) => sum + Number(t.cost), 0),
      byStatus,
    },
    treatments: treatments.slice(0, 100),
  }
}

// Dentist performance report
async function generateDentistPerformanceReport(startDate: Date, endDate: Date, branchId?: string, dentistId?: string) {
  const dentists = await prisma.dentistProfile.findMany({
    where: {
      ...(dentistId && { id: dentistId }),
    },
    include: {
      user: { select: { name: true } },
      appointments: {
        where: {
          scheduledAt: {
            gte: startDate,
            lte: endDate,
          },
          ...(branchId && { branchId }),
        },
      },
      treatments: {
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      },
    },
  })
  
  const performance = dentists.map(dentist => ({
    dentist: {
      id: dentist.id,
      name: dentist.user.name,
      specialization: dentist.specialization,
    },
    metrics: {
      totalAppointments: dentist.appointments.length,
      totalTreatments: dentist.treatments.length,
      totalRevenue: dentist.treatments.reduce((sum, t) => sum + Number(t.cost), 0),
      averageRevenuePerTreatment: dentist.treatments.length > 0 
        ? dentist.treatments.reduce((sum, t) => sum + Number(t.cost), 0) / dentist.treatments.length 
        : 0,
    },
  }))
  
  return {
    summary: {
      totalDentists: dentists.length,
      totalAppointments: performance.reduce((sum, d) => sum + d.metrics.totalAppointments, 0),
      totalRevenue: performance.reduce((sum, d) => sum + d.metrics.totalRevenue, 0),
    },
    performance,
  }
}
