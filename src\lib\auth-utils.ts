import { getServerSession } from 'next-auth'
import { authOptions } from './auth'
import { UserRole } from '@prisma/client'
import { NextRequest } from 'next/server'

export async function getSession() {
  return await getServerSession(authOptions)
}

export async function getCurrentUser() {
  const session = await getSession()
  return session?.user
}

export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Authentication required')
  }
  return user
}

export async function requireRole(allowedRoles: UserRole[]) {
  const user = await requireAuth()
  if (!allowedRoles.includes(user.role)) {
    throw new Error('Insufficient permissions')
  }
  return user
}

export function hasRole(userRole: UserRole, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(userRole)
}

export function isPatient(role: UserRole): boolean {
  return role === UserRole.PATIENT
}

export function isDentist(role: UserRole): boolean {
  return role === UserRole.DENTIST
}

export function isStaff(role: UserRole): boolean {
  return role === UserRole.STAFF
}

export function isAdmin(role: UserRole): boolean {
  return role === UserRole.ADMIN
}

export function canAccessPatientData(userRole: UserRole, patientId?: string, userId?: string): boolean {
  // Admins and staff can access all patient data
  if (userRole === UserRole.ADMIN || userRole === UserRole.STAFF) {
    return true
  }
  
  // Dentists can access patient data (with additional checks in business logic)
  if (userRole === UserRole.DENTIST) {
    return true
  }
  
  // Patients can only access their own data
  if (userRole === UserRole.PATIENT && patientId && userId) {
    return patientId === userId
  }
  
  return false
}

export function canManageAppointments(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN || userRole === UserRole.STAFF || userRole === UserRole.DENTIST
}

export function canManageUsers(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN
}

export function canManageInventory(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN || userRole === UserRole.STAFF
}

export function canViewReports(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN || userRole === UserRole.DENTIST
}

// Middleware helper for API routes
export function withAuth<T extends unknown[]>(
  handler: (req: NextRequest, user: { id: string; role: UserRole; email: string }, ...args: T) => Promise<Response>,
  allowedRoles?: UserRole[]
) {
  return async (req: NextRequest, ...args: T): Promise<Response> => {
    try {
      const user = await requireAuth()

      if (allowedRoles && !hasRole(user.role, allowedRoles)) {
        return new Response(
          JSON.stringify({ error: 'Insufficient permissions' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        )
      }

      return await handler(req, user, ...args)
    } catch {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }
  }
}
