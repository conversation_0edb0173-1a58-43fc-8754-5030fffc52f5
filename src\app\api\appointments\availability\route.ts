import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole, AppointmentStatus } from '@prisma/client'

// GET /api/appointments/availability - Check dentist availability
export const GET = withAuth(async (req: NextRequest) => {
  const { searchParams } = new URL(req.url)
  
  const dentistId = searchParams.get('dentistId')
  const date = searchParams.get('date') // YYYY-MM-DD format
  const duration = Number(searchParams.get('duration')) || 60 // minutes
  
  if (!dentistId || !date) {
    return NextResponse.json(
      { error: 'dentistId and date are required' },
      { status: 400 }
    )
  }
  
  // Verify dentist exists and is available
  const dentist = await prisma.dentistProfile.findUnique({
    where: { id: dentistId },
    include: {
      schedules: true,
      user: { select: { name: true } },
    },
  })
  
  if (!dentist || !dentist.isAvailable) {
    return NextResponse.json(
      { error: 'Dentist not found or not available' },
      { status: 404 }
    )
  }
  
  const targetDate = new Date(date)
  const dayOfWeek = targetDate.getDay() // 0 = Sunday, 1 = Monday, etc.
  
  // Get dentist's schedule for the day
  const schedule = dentist.schedules.find(s => s.dayOfWeek === dayOfWeek && s.isActive)
  
  if (!schedule) {
    return NextResponse.json({
      available: false,
      message: 'Dentist is not scheduled to work on this day',
      timeSlots: [],
    })
  }
  
  // Parse working hours
  const [startHour, startMinute] = schedule.startTime.split(':').map(Number)
  const [endHour, endMinute] = schedule.endTime.split(':').map(Number)
  
  const workStart = new Date(targetDate)
  workStart.setHours(startHour, startMinute, 0, 0)
  
  const workEnd = new Date(targetDate)
  workEnd.setHours(endHour, endMinute, 0, 0)
  
  // Get existing appointments for the day
  const dayStart = new Date(targetDate)
  dayStart.setHours(0, 0, 0, 0)
  
  const dayEnd = new Date(targetDate)
  dayEnd.setHours(23, 59, 59, 999)
  
  const existingAppointments = await prisma.appointment.findMany({
    where: {
      dentistId,
      scheduledAt: {
        gte: dayStart,
        lte: dayEnd,
      },
      status: {
        in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS],
      },
    },
    orderBy: { scheduledAt: 'asc' },
  })
  
  // Generate available time slots
  const timeSlots: Array<{
    startTime: string
    endTime: string
    available: boolean
  }> = []
  
  const slotDuration = 30 // 30-minute slots
  let currentTime = new Date(workStart)
  
  while (currentTime < workEnd) {
    const slotEnd = new Date(currentTime.getTime() + duration * 60000)
    
    // Check if this slot conflicts with any existing appointment
    const hasConflict = existingAppointments.some(appointment => {
      const appointmentStart = new Date(appointment.scheduledAt)
      const appointmentEnd = new Date(appointmentStart.getTime() + appointment.duration * 60000)
      
      return (
        (currentTime >= appointmentStart && currentTime < appointmentEnd) ||
        (slotEnd > appointmentStart && slotEnd <= appointmentEnd) ||
        (currentTime <= appointmentStart && slotEnd >= appointmentEnd)
      )
    })
    
    // Check if slot fits within working hours
    const fitsInWorkingHours = slotEnd <= workEnd
    
    timeSlots.push({
      startTime: currentTime.toTimeString().slice(0, 5), // HH:MM format
      endTime: slotEnd.toTimeString().slice(0, 5),
      available: !hasConflict && fitsInWorkingHours,
    })
    
    // Move to next slot
    currentTime = new Date(currentTime.getTime() + slotDuration * 60000)
  }
  
  return NextResponse.json({
    available: timeSlots.some(slot => slot.available),
    dentist: {
      id: dentist.id,
      name: dentist.user.name,
      specialization: dentist.specialization,
    },
    date,
    workingHours: {
      start: schedule.startTime,
      end: schedule.endTime,
    },
    timeSlots,
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/appointments/availability/bulk - Check availability for multiple dentists
export const POST = withAuth(async (req: NextRequest) => {
  const body = await req.json()
  const { dentistIds, date, duration = 60 } = body
  
  if (!dentistIds || !Array.isArray(dentistIds) || !date) {
    return NextResponse.json(
      { error: 'dentistIds (array) and date are required' },
      { status: 400 }
    )
  }
  
  const availabilityPromises = dentistIds.map(async (dentistId: string) => {
    try {
      // Reuse the logic from GET endpoint
      const dentist = await prisma.dentistProfile.findUnique({
        where: { id: dentistId },
        include: {
          schedules: true,
          user: { select: { name: true } },
        },
      })
      
      if (!dentist || !dentist.isAvailable) {
        return {
          dentistId,
          available: false,
          message: 'Dentist not found or not available',
        }
      }
      
      const targetDate = new Date(date)
      const dayOfWeek = targetDate.getDay()
      
      const schedule = dentist.schedules.find(s => s.dayOfWeek === dayOfWeek && s.isActive)
      
      if (!schedule) {
        return {
          dentistId,
          available: false,
          message: 'Dentist is not scheduled to work on this day',
        }
      }
      
      // Quick availability check - just see if there are any free slots
      const dayStart = new Date(targetDate)
      dayStart.setHours(0, 0, 0, 0)
      
      const dayEnd = new Date(targetDate)
      dayEnd.setHours(23, 59, 59, 999)
      
      const appointmentCount = await prisma.appointment.count({
        where: {
          dentistId,
          scheduledAt: {
            gte: dayStart,
            lte: dayEnd,
          },
          status: {
            in: [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS],
          },
        },
      })
      
      // Simple heuristic: if less than 8 appointments, likely has availability
      const hasAvailability = appointmentCount < 8
      
      return {
        dentistId,
        dentist: {
          name: dentist.user.name,
          specialization: dentist.specialization,
        },
        available: hasAvailability,
        appointmentCount,
        workingHours: {
          start: schedule.startTime,
          end: schedule.endTime,
        },
      }
    } catch {
      return {
        dentistId,
        available: false,
        error: 'Error checking availability',
      }
    }
  })
  
  const results = await Promise.all(availabilityPromises)
  
  return NextResponse.json({
    date,
    duration,
    dentists: results,
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
