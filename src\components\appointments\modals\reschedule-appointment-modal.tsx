'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { HealthcareCalendar } from '@/components/ui/healthcare-calendar'
import { Loader2, Calendar as CalendarIcon, Clock, CheckCircle } from 'lucide-react'
import { AppointmentDetails, AvailabilityResponse } from '@/types/appointment'

interface RescheduleAppointmentModalProps {
  isOpen: boolean
  onClose: () => void
  appointment: AppointmentDetails | null
  onSuccess: () => void
}

export function RescheduleAppointmentModal({ 
  isOpen, 
  onClose, 
  appointment, 
  onSuccess 
}: RescheduleAppointmentModalProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>()
  const [selectedTime, setSelectedTime] = useState<string | undefined>()
  const [availability, setAvailability] = useState<AvailabilityResponse | null>(null)
  const [isLoadingAvailability, setIsLoadingAvailability] = useState(false)
  const [isRescheduling, setIsRescheduling] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchAvailability = useCallback(async (date: Date) => {
    if (!appointment) return

    try {
      setIsLoadingAvailability(true)
      setError(null)
      
      const dateString = date.toISOString().split('T')[0]
      const response = await fetch(
        `/api/appointments/availability?dentistId=${appointment.dentist.id}&date=${dateString}&duration=${appointment.duration}`
      )
      
      if (!response.ok) {
        throw new Error('Failed to fetch availability')
      }
      
      const data = await response.json()
      setAvailability(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load availability')
      setAvailability(null)
    } finally {
      setIsLoadingAvailability(false)
    }
  }, [appointment])

  useEffect(() => {
    if (selectedDate && appointment) {
      fetchAvailability(selectedDate)
    }
  }, [selectedDate, appointment, fetchAvailability])

  useEffect(() => {
    if (!isOpen) {
      // Reset state when modal closes
      setSelectedDate(undefined)
      setSelectedTime(undefined)
      setAvailability(null)
      setError(null)
    }
  }, [isOpen])

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date)
    setSelectedTime(undefined)
  }

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time)
  }

  const handleReschedule = async () => {
    if (!appointment || !selectedDate || !selectedTime) return

    try {
      setIsRescheduling(true)
      setError(null)

      const [hours, minutes] = selectedTime.split(':').map(Number)
      const newDateTime = new Date(selectedDate)
      newDateTime.setHours(hours, minutes, 0, 0)

      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scheduledAt: newDateTime.toISOString(),
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to reschedule appointment')
      }

      onSuccess()
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reschedule appointment')
    } finally {
      setIsRescheduling(false)
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    })
  }

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':').map(Number)
    const date = new Date()
    date.setHours(hours, minutes)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Manila'
    })
  }

  // Disable past dates and Sundays
  const isDateDisabled = (date: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return date < today || date.getDay() === 0
  }

  if (!appointment) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">Reschedule Appointment</DialogTitle>
          <DialogDescription>
            Select a new date and time for your {appointment.service.name} appointment with Dr. {appointment.dentist.user.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Appointment Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Current Appointment</h4>
            <p className="text-sm text-gray-600">
              {formatDate(new Date(appointment.scheduledAt))} at {formatTime(new Date(appointment.scheduledAt).toTimeString().slice(0, 5))}
            </p>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-6 lg:grid-cols-2">
            {/* Calendar */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Select New Date</h4>
              <HealthcareCalendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={isDateDisabled}
                className="w-full"
              />
            </div>

            {/* Time Slots */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Select New Time</h4>
              {!selectedDate ? (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Please select a date first</p>
                </div>
              ) : isLoadingAvailability ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                  <span className="ml-2 text-gray-600">Loading availability...</span>
                </div>
              ) : !availability?.available ? (
                <Alert>
                  <AlertDescription>
                    No available time slots for this date. Please select another date.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    Working hours: {formatTime(availability.workingHours.start)} - {formatTime(availability.workingHours.end)}
                  </div>
                  <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto">
                    {availability.timeSlots
                      .filter(slot => slot.available)
                      .map((slot) => (
                        <Button
                          key={slot.startTime}
                          variant={selectedTime === slot.startTime ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleTimeSelect(slot.startTime)}
                          className={`justify-center ${
                            selectedTime === slot.startTime
                              ? 'bg-blue-600 hover:bg-blue-700'
                              : 'hover:bg-blue-50 hover:border-blue-200'
                          }`}
                        >
                          {formatTime(slot.startTime)}
                          {selectedTime === slot.startTime && (
                            <CheckCircle className="h-4 w-4 ml-1" />
                          )}
                        </Button>
                      ))}
                  </div>
                  {availability.timeSlots.filter(slot => slot.available).length === 0 && (
                    <Alert>
                      <AlertDescription>
                        No available time slots for this date. Please select another date.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* New Appointment Summary */}
          {selectedDate && selectedTime && (
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">New Appointment Time</h4>
              <p className="text-blue-800">
                {formatDate(selectedDate)} at {formatTime(selectedTime)}
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={onClose} disabled={isRescheduling}>
              Cancel
            </Button>
            <Button
              onClick={handleReschedule}
              disabled={!selectedDate || !selectedTime || isRescheduling}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRescheduling ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Rescheduling...
                </>
              ) : (
                <>
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Reschedule Appointment
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
