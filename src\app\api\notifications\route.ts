import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole, NotificationType, NotificationChannel } from '@prisma/client'
import { z } from 'zod'

const notificationCreateSchema = z.object({
  userId: z.string().cuid().optional(),
  appointmentId: z.string().cuid().optional(),
  type: z.nativeEnum(NotificationType),
  channel: z.nativeEnum(NotificationChannel),
  title: z.string().min(1),
  message: z.string().min(1),
  scheduledFor: z.string().transform((str) => new Date(str)).optional(),
  metadata: z.record(z.any()).optional(),
})

// GET /api/notifications - List notifications
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 20,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const type = searchParams.get('type') as NotificationType | null
  const channel = searchParams.get('channel') as NotificationChannel | null
  const isRead = searchParams.get('isRead')
  const userId = searchParams.get('userId')
  
  const skip = (page - 1) * limit
  
  // Build where clause based on user role
  const where: Record<string, unknown> = {}
  
  // Role-based filtering
  if (user.role === UserRole.PATIENT) {
    // Patients can only see their own notifications
    where.userId = user.id
  } else if (userId) {
    // Admin/Staff can filter by specific user
    where.userId = userId
  }
  
  // Additional filters
  if (type) where.type = type
  if (channel) where.channel = channel
  if (isRead !== null) where.isRead = isRead === 'true'
  
  // Search filter
  if (query) {
    where.OR = [
      { title: { contains: query, mode: 'insensitive' } },
      { message: { contains: query, mode: 'insensitive' } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'sentAt' ? { sentAt: sortOrder } :
                  sortBy === 'type' ? { type: sortOrder } :
                  { createdAt: sortOrder as 'asc' | 'desc' }

  const [notifications, total] = await Promise.all([
    prisma.notification.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        appointment: {
          select: {
            id: true,
            scheduledAt: true,
            service: { select: { name: true } },
            patient: {
              include: {
                user: { select: { name: true } },
              },
            },
          },
        },
      },
    }),
    prisma.notification.count({ where }),
  ])
  
  return NextResponse.json({
    notifications,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/notifications - Create notification
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = notificationCreateSchema.parse(body)
  
  // Only admin and staff can create notifications for other users
  if (validatedData.userId && validatedData.userId !== user.id) {
    if (user.role !== UserRole.ADMIN && user.role !== UserRole.STAFF) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }
  }
  
  // Verify user exists if specified
  if (validatedData.userId) {
    const targetUser = await prisma.user.findUnique({
      where: { id: validatedData.userId },
    })
    
    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
  }
  
  // Verify appointment exists if specified
  if (validatedData.appointmentId) {
    const appointment = await prisma.appointment.findUnique({
      where: { id: validatedData.appointmentId },
    })
    
    if (!appointment) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      )
    }
  }
  
  // Create notification
  const notification = await prisma.notification.create({
    data: {
      ...validatedData,
      sentAt: validatedData.scheduledFor ? undefined : new Date(),
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
        },
      },
      appointment: {
        select: {
          id: true,
          scheduledAt: true,
          service: { select: { name: true } },
        },
      },
    },
  })
  
  // Send notification immediately if not scheduled
  if (!validatedData.scheduledFor && notification.userId) {
    await sendNotification({
      id: notification.id,
      channel: notification.channel,
      userId: notification.userId,
      title: notification.title,
      message: notification.message,
    })
  }
  
  return NextResponse.json(notification, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])

// Helper function to send notifications
async function sendNotification(notification: { id: string; channel: string; userId: string; title: string; message: string }) {
  try {
    switch (notification.channel) {
      case NotificationChannel.EMAIL:
        await sendEmailNotification(notification)
        break
      case NotificationChannel.SMS:
        await sendSMSNotification(notification)
        break
      case NotificationChannel.PUSH:
        await sendPushNotification(notification)
        break
      case NotificationChannel.IN_APP:
        // In-app notifications are already stored in database
        break
    }
    
    // Update notification as sent
    await prisma.notification.update({
      where: { id: notification.id },
      data: { sentAt: new Date() },
    })
  } catch (error) {
    console.error('Failed to send notification:', error)
  }
}

async function sendEmailNotification(notification: { title: string; message: string }) {
  // TODO: Implement SendGrid email sending
  console.log('Sending email notification:', notification.title)
}

async function sendSMSNotification(notification: { title: string; message: string }) {
  // TODO: Implement Twilio SMS sending
  console.log('Sending SMS notification:', notification.title)
}

async function sendPushNotification(notification: { title: string; message: string }) {
  // TODO: Implement push notification sending
  console.log('Sending push notification:', notification.title)
}
