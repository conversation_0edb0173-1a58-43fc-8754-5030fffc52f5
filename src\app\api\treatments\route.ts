import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { treatmentCreateSchema, paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole, TreatmentStatus } from '@prisma/client'

// GET /api/treatments - List treatments with pagination and filtering
export const GET = withAuth(async (req: NextRequest, user) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 10,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const patientId = searchParams.get('patientId')
  const dentistId = searchParams.get('dentistId')
  const status = searchParams.get('status') as TreatmentStatus | null
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')
  
  const skip = (page - 1) * limit
  
  // Build where clause based on user role and filters
  const where: Record<string, unknown> = {}
  
  // Role-based filtering
  if (user.role === UserRole.PATIENT) {
    // Patients can only see their own treatments
    const patientProfile = await prisma.patientProfile.findUnique({
      where: { userId: user.id },
    })
    if (!patientProfile) {
      return NextResponse.json({ treatments: [], pagination: { page, limit, total: 0, pages: 0 } })
    }
    where.patientId = patientProfile.id
  } else if (user.role === UserRole.DENTIST) {
    // Dentists can see their own treatments
    const dentistProfile = await prisma.dentistProfile.findUnique({
      where: { userId: user.id },
    })
    if (!dentistProfile) {
      return NextResponse.json({ treatments: [], pagination: { page, limit, total: 0, pages: 0 } })
    }
    where.dentistId = dentistProfile.id
  }
  
  // Additional filters
  if (patientId) {
    // Check if user can access this patient's data
    if (!canAccessPatientData(user.role, patientId, user.id)) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }
    where.patientId = patientId
  }
  if (dentistId) where.dentistId = dentistId
  if (status) where.status = status
  
  // Date range filter
  if (dateFrom || dateTo) {
    where.createdAt = {}
    if (dateFrom) (where.createdAt as any).gte = new Date(dateFrom)
    if (dateTo) (where.createdAt as any).lte = new Date(dateTo)
  }
  
  // Search filter
  if (query) {
    where.OR = [
      { diagnosis: { contains: query, mode: 'insensitive' } },
      { procedure: { contains: query, mode: 'insensitive' } },
      { notes: { contains: query, mode: 'insensitive' } },
      { patient: { user: { name: { contains: query, mode: 'insensitive' } } } },
      { service: { name: { contains: query, mode: 'insensitive' } } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'startedAt' ? { startedAt: sortOrder } :
                  sortBy === 'completedAt' ? { completedAt: sortOrder } :
                  sortBy === 'status' ? { status: sortOrder } :
                  { createdAt: sortOrder as 'asc' | 'desc' }

  const [treatments, total] = await Promise.all([
    prisma.treatment.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        patient: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        dentist: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        service: {
          select: {
            id: true,
            name: true,
            category: true,
            price: true,
          },
        },
        appointment: {
          select: {
            id: true,
            scheduledAt: true,
            status: true,
          },
        },
        prescriptions: true,
        files: {
          select: {
            id: true,
            fileName: true,
            fileType: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            prescriptions: true,
            files: true,
          },
        },
      },
    }),
    prisma.treatment.count({ where }),
  ])
  
  return NextResponse.json({
    treatments,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/treatments - Create new treatment record
export const POST = withAuth(async (req: NextRequest, user) => {
  const body = await req.json()
  const validatedData = treatmentCreateSchema.parse(body)
  
  // Verify patient exists
  const patient = await prisma.patientProfile.findUnique({
    where: { id: validatedData.patientId },
    include: { user: true },
  })
  
  if (!patient) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check if user can access this patient's data
  if (!canAccessPatientData(user.role, validatedData.patientId, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  // Verify dentist exists
  const dentist = await prisma.dentistProfile.findUnique({
    where: { id: validatedData.dentistId },
    include: { user: true },
  })
  
  if (!dentist) {
    return NextResponse.json(
      { error: 'Dentist not found' },
      { status: 404 }
    )
  }
  
  // Dentists can only create treatments for themselves
  if (user.role === UserRole.DENTIST && dentist.userId !== user.id) {
    return NextResponse.json(
      { error: 'You can only create treatments for yourself' },
      { status: 403 }
    )
  }
  
  // Verify service exists
  const service = await prisma.service.findUnique({
    where: { id: validatedData.serviceId },
  })
  
  if (!service) {
    return NextResponse.json(
      { error: 'Service not found' },
      { status: 404 }
    )
  }
  
  // If appointment is specified, verify it exists and belongs to the patient/dentist
  if (validatedData.appointmentId) {
    const appointment = await prisma.appointment.findUnique({
      where: { id: validatedData.appointmentId },
    })
    
    if (!appointment || 
        appointment.patientId !== validatedData.patientId || 
        appointment.dentistId !== validatedData.dentistId) {
      return NextResponse.json(
        { error: 'Invalid appointment' },
        { status: 400 }
      )
    }
  }
  
  // Create treatment
  const treatment = await prisma.treatment.create({
    data: {
      ...validatedData,
      startedAt: new Date(),
    },
    include: {
      patient: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
      dentist: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      service: {
        select: {
          id: true,
          name: true,
          category: true,
          price: true,
        },
      },
      appointment: {
        select: {
          id: true,
          scheduledAt: true,
          status: true,
        },
      },
    },
  })
  
  return NextResponse.json(treatment, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])
