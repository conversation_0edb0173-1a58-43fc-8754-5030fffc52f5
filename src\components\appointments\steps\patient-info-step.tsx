'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileText, AlertCircle } from 'lucide-react'

interface PatientInfoStepProps {
  notes?: string
  symptoms?: string
  onUpdate: (notes?: string, symptoms?: string) => void
  onNext: () => void
}

export function PatientInfoStep({ notes, symptoms, onUpdate, onNext }: PatientInfoStepProps) {
  const [formData, setFormData] = useState({
    notes: notes || '',
    symptoms: symptoms || '',
  })

  const handleInputChange = (field: string, value: string) => {
    const updatedData = { ...formData, [field]: value }
    setFormData(updatedData)
    onUpdate(updatedData.notes, updatedData.symptoms)
  }

  const handleNext = () => {
    onNext()
  }

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 mb-4">
        Please provide any additional information that might help us prepare for your appointment.
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Symptoms */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
              Symptoms or Concerns
            </CardTitle>
            <CardDescription>
              Describe any symptoms, pain, or specific concerns you&apos;d like to discuss
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="symptoms">Current symptoms or concerns</Label>
              <Textarea
                id="symptoms"
                placeholder="Please describe any pain, discomfort, or specific dental concerns you're experiencing..."
                value={formData.symptoms}
                onChange={(e) => handleInputChange('symptoms', e.target.value)}
                className="min-h-[120px] resize-none"
              />
              <p className="text-xs text-gray-500">
                This information helps our dentist prepare for your visit and provide better care.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Additional Notes */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <FileText className="h-5 w-5 mr-2 text-blue-500" />
              Additional Notes
            </CardTitle>
            <CardDescription>
              Any other information you&apos;d like us to know before your appointment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="notes">Additional notes or requests</Label>
              <Textarea
                id="notes"
                placeholder="Any special requests, preferences, or additional information you'd like to share..."
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                className="min-h-[120px] resize-none"
              />
              <p className="text-xs text-gray-500">
                Optional: Include any preferences, accessibility needs, or special requests.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information Notice */}
      <Alert className="bg-blue-50 border-blue-200">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Please note:</strong> This information is optional but helps us provide better care.
          If you&apos;re experiencing severe pain or a dental emergency, please call our clinic directly
          for immediate assistance.
        </AlertDescription>
      </Alert>

      {/* Example Symptoms */}
      <Card className="bg-gray-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-sm text-gray-700">Common symptoms to mention:</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
            <ul className="space-y-1">
              <li>• Tooth pain or sensitivity</li>
              <li>• Gum bleeding or swelling</li>
              <li>• Jaw pain or clicking</li>
              <li>• Bad breath or taste</li>
            </ul>
            <ul className="space-y-1">
              <li>• Loose or broken teeth</li>
              <li>• Difficulty chewing</li>
              <li>• Mouth sores or lesions</li>
              <li>• Teeth grinding or clenching</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      {(formData.symptoms || formData.notes) && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="pt-6">
            <h4 className="font-medium text-gray-900 mb-2">Information Summary</h4>
            {formData.symptoms && (
              <div className="mb-2">
                <p className="text-sm font-medium text-gray-700">Symptoms/Concerns:</p>
                <p className="text-sm text-gray-600">{formData.symptoms}</p>
              </div>
            )}
            {formData.notes && (
              <div>
                <p className="text-sm font-medium text-gray-700">Additional Notes:</p>
                <p className="text-sm text-gray-600">{formData.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <div className="flex justify-end pt-4">
        <Button onClick={handleNext} className="bg-blue-600 hover:bg-blue-700">
          Continue to Confirmation
        </Button>
      </div>
    </div>
  )
}
