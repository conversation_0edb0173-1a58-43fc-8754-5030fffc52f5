'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, MapPin, Phone, Mail, CheckCircle } from 'lucide-react'
import { Branch } from '@/types/appointment'

interface BranchSelectionStepProps {
  selectedBranchId?: string
  onSelect: (branchId: string) => void
  onNext: () => void
}

export function BranchSelectionStep({ selectedBranchId, onSelect, onNext }: BranchSelectionStepProps) {
  const [branches, setBranches] = useState<Branch[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchBranches = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/branches?isActive=true')
      
      if (!response.ok) {
        throw new Error('Failed to fetch branches')
      }
      
      const data = await response.json()
      setBranches(data.branches || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load branches')
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchBranches()
  }, [fetchBranches])

  const handleBranchSelect = (branchId: string) => {
    onSelect(branchId)
  }

  const handleNext = () => {
    if (selectedBranchId) {
      onNext()
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading branches...</span>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (branches.length === 0) {
    return (
      <Alert>
        <AlertDescription>No branches are currently available. Please try again later.</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 mb-4">
        Please select your preferred clinic location for your appointment.
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {branches.map((branch) => (
          <Card
            key={branch.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedBranchId === branch.id
                ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200'
                : 'hover:border-gray-400'
            }`}
            onClick={() => handleBranchSelect(branch.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg text-gray-900 flex items-center">
                    {branch.name}
                    {selectedBranchId === branch.id && (
                      <CheckCircle className="h-5 w-5 text-blue-600 ml-2" />
                    )}
                  </CardTitle>
                  {branch.description && (
                    <CardDescription className="mt-1">
                      {branch.description}
                    </CardDescription>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div className="flex items-start text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mt-0.5 mr-2 text-gray-400 flex-shrink-0" />
                  <span>{branch.address}</span>
                </div>
                
                {branch.phone && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="h-4 w-4 mr-2 text-gray-400" />
                    <span>{branch.phone}</span>
                  </div>
                )}
                
                {branch.email && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="h-4 w-4 mr-2 text-gray-400" />
                    <span>{branch.email}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedBranchId && (
        <div className="flex justify-end pt-4">
          <Button onClick={handleNext} className="bg-blue-600 hover:bg-blue-700">
            Continue to Service Selection
          </Button>
        </div>
      )}
    </div>
  )
}
