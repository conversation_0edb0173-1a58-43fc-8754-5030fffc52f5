import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

const searchSchema = z.object({
  q: z.string().min(1, 'Search query is required'),
  limit: z.number().min(1).max(50).default(10),
})

// GET /api/patients/search - Search patients by name, email, or phone
export const GET = withAuth(async (req: NextRequest) => {
  const { searchParams } = new URL(req.url)
  
  try {
    const { q, limit } = searchSchema.parse({
      q: searchParams.get('q'),
      limit: Number(searchParams.get('limit')) || 10,
    })

    const patients = await prisma.patientProfile.findMany({
      where: {
        user: {
          role: UserRole.PATIENT,
          status: 'ACTIVE',
          OR: [
            { name: { contains: q, mode: 'insensitive' } },
            { email: { contains: q, mode: 'insensitive' } },
            { phone: { contains: q, mode: 'insensitive' } },
          ],
        },
      },
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            phone: true,
            status: true,
          },
        },
        appointments: {
          orderBy: { scheduledAt: 'desc' },
          take: 1,
          select: {
            scheduledAt: true,
          },
        },
        _count: {
          select: {
            appointments: true,
          },
        },
      },
      orderBy: [
        { user: { name: 'asc' } },
        { user: { email: 'asc' } },
      ],
    })

    const searchResults = patients.map(patient => ({
      id: patient.id,
      name: patient.user.name || 'Unknown',
      email: patient.user.email,
      phone: patient.user.phone,
      lastVisit: patient.appointments[0]?.scheduledAt || null,
      totalAppointments: patient._count.appointments,
      status: patient.user.status,
    }))

    return NextResponse.json({
      results: searchResults,
      total: searchResults.length,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid search parameters', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error searching patients:', error)
    return NextResponse.json(
      { error: 'Failed to search patients' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])
