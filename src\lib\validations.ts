import { z } from 'zod'
import { UserRole, AppointmentStatus, AppointmentType, PaymentMethod } from '@prisma/client'

// User validation schemas
export const userCreateSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().optional(),
  role: z.nativeEnum(UserRole),
})

export const userUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().optional(),
  image: z.string().url().optional(),
})

// Patient profile validation schemas
export const patientProfileCreateSchema = z.object({
  dateOfBirth: z.string().transform((str) => new Date(str)).optional(),
  gender: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyPhone: z.string().optional(),
  insuranceProvider: z.string().optional(),
  insuranceNumber: z.string().optional(),
  medicalHistory: z.string().optional(),
  allergies: z.string().optional(),
  medications: z.string().optional(),
})

export const patientProfileUpdateSchema = patientProfileCreateSchema.partial()

// Dentist profile validation schemas
export const dentistProfileCreateSchema = z.object({
  licenseNumber: z.string().min(1, 'License number is required'),
  specialization: z.string().optional(),
  yearsExperience: z.number().min(0).optional(),
  education: z.string().optional(),
  certifications: z.string().optional(),
  bio: z.string().optional(),
  consultationFee: z.number().min(0).optional(),
  isAvailable: z.boolean().default(true),
})

export const dentistProfileUpdateSchema = dentistProfileCreateSchema.partial()

// Staff profile validation schemas
export const staffProfileCreateSchema = z.object({
  position: z.string().min(1, 'Position is required'),
  department: z.string().optional(),
  hireDate: z.string().transform((str) => new Date(str)),
  salary: z.number().min(0).optional(),
  permissions: z.array(z.string()).default([]),
})

export const staffProfileUpdateSchema = staffProfileCreateSchema.partial()

// Appointment validation schemas
export const appointmentCreateSchema = z.object({
  patientId: z.string().cuid(),
  dentistId: z.string().cuid(),
  branchId: z.string().cuid(),
  serviceId: z.string().cuid(),
  scheduledAt: z.string().transform((str) => new Date(str)),
  duration: z.number().min(15).max(480), // 15 minutes to 8 hours
  type: z.nativeEnum(AppointmentType).default(AppointmentType.CONSULTATION),
  notes: z.string().optional(),
  symptoms: z.string().optional(),
})

export const appointmentUpdateSchema = z.object({
  scheduledAt: z.string().transform((str) => new Date(str)).optional(),
  duration: z.number().min(15).max(480).optional(),
  status: z.nativeEnum(AppointmentStatus).optional(),
  type: z.nativeEnum(AppointmentType).optional(),
  notes: z.string().optional(),
  symptoms: z.string().optional(),
  cancelReason: z.string().optional(),
})

// Treatment validation schemas
export const treatmentCreateSchema = z.object({
  appointmentId: z.string().cuid().optional(),
  patientId: z.string().cuid(),
  dentistId: z.string().cuid(),
  serviceId: z.string().cuid(),
  diagnosis: z.string().optional(),
  procedure: z.string().optional(),
  notes: z.string().optional(),
  cost: z.number().min(0),
})

export const treatmentUpdateSchema = treatmentCreateSchema.partial().extend({
  status: z.enum(['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
})

// Service validation schemas
export const serviceCreateSchema = z.object({
  name: z.string().min(1, 'Service name is required'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  duration: z.number().min(15), // minimum 15 minutes
  price: z.number().min(0),
  isActive: z.boolean().default(true),
})

export const serviceUpdateSchema = serviceCreateSchema.partial()

// Branch validation schemas
export const branchCreateSchema = z.object({
  name: z.string().min(1, 'Branch name is required'),
  address: z.string().min(1, 'Address is required'),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
})

export const branchUpdateSchema = branchCreateSchema.partial()

// Invoice validation schemas
export const invoiceCreateSchema = z.object({
  patientId: z.string().cuid(),
  items: z.array(z.object({
    treatmentId: z.string().cuid().optional(),
    description: z.string().min(1),
    quantity: z.number().min(1).default(1),
    unitPrice: z.number().min(0),
  })),
  dueDate: z.string().transform((str) => new Date(str)),
  notes: z.string().optional(),
})

// Payment validation schemas
export const paymentCreateSchema = z.object({
  invoiceId: z.string().cuid(),
  amount: z.number().min(0),
  method: z.nativeEnum(PaymentMethod),
  transactionId: z.string().optional(),
  notes: z.string().optional(),
})

// Inventory validation schemas
export const inventoryItemCreateSchema = z.object({
  branchId: z.string().cuid(),
  name: z.string().min(1, 'Item name is required'),
  sku: z.string().min(1, 'SKU is required'),
  category: z.string().min(1, 'Category is required'),
  description: z.string().optional(),
  supplier: z.string().optional(),
  unitPrice: z.number().min(0).optional(),
  currentStock: z.number().min(0).default(0),
  minStock: z.number().min(0).default(0),
  maxStock: z.number().min(0).optional(),
  unit: z.string().min(1, 'Unit is required'),
  expiryDate: z.string().transform((str) => new Date(str)).optional(),
  isActive: z.boolean().default(true),
})

export const inventoryItemUpdateSchema = inventoryItemCreateSchema.partial()

// Common validation helpers
export const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
})

export const searchSchema = z.object({
  query: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

export type UserCreateInput = z.infer<typeof userCreateSchema>
export type UserUpdateInput = z.infer<typeof userUpdateSchema>
export type PatientProfileCreateInput = z.infer<typeof patientProfileCreateSchema>
export type PatientProfileUpdateInput = z.infer<typeof patientProfileUpdateSchema>
export type DentistProfileCreateInput = z.infer<typeof dentistProfileCreateSchema>
export type DentistProfileUpdateInput = z.infer<typeof dentistProfileUpdateSchema>
export type StaffProfileCreateInput = z.infer<typeof staffProfileCreateSchema>
export type StaffProfileUpdateInput = z.infer<typeof staffProfileUpdateSchema>
export type AppointmentCreateInput = z.infer<typeof appointmentCreateSchema>
export type AppointmentUpdateInput = z.infer<typeof appointmentUpdateSchema>
export type TreatmentCreateInput = z.infer<typeof treatmentCreateSchema>
export type TreatmentUpdateInput = z.infer<typeof treatmentUpdateSchema>
export type ServiceCreateInput = z.infer<typeof serviceCreateSchema>
export type ServiceUpdateInput = z.infer<typeof serviceUpdateSchema>
export type BranchCreateInput = z.infer<typeof branchCreateSchema>
export type BranchUpdateInput = z.infer<typeof branchUpdateSchema>
export type InvoiceCreateInput = z.infer<typeof invoiceCreateSchema>
export type PaymentCreateInput = z.infer<typeof paymentCreateSchema>
export type InventoryItemCreateInput = z.infer<typeof inventoryItemCreateSchema>
export type InventoryItemUpdateInput = z.infer<typeof inventoryItemUpdateSchema>
export type PaginationInput = z.infer<typeof paginationSchema>
export type SearchInput = z.infer<typeof searchSchema>
