'use client'

import { useState, useEffect, useCallback } from 'react'
import { AppointmentStatus, AppointmentType } from '@prisma/client'
import { AppointmentDetails } from '@/types/appointment'

export interface AppointmentFilters {
  status?: AppointmentStatus
  dateFrom?: string
  dateTo?: string
  patientName?: string
  serviceId?: string
  type?: AppointmentType
}

export interface UseAppointmentsOptions {
  dentistId?: string
  initialFilters?: AppointmentFilters
  autoRefresh?: boolean
  refreshInterval?: number
}

export interface UseAppointmentsReturn {
  appointments: AppointmentDetails[]
  loading: boolean
  error: string | null
  filters: AppointmentFilters
  totalCount: number
  hasNextPage: boolean
  hasPrevPage: boolean
  currentPage: number
  totalPages: number
  // Actions
  refetch: () => Promise<void>
  updateFilters: (newFilters: Partial<AppointmentFilters>) => void
  clearFilters: () => void
  loadNextPage: () => Promise<void>
  loadPrevPage: () => Promise<void>
  updateAppointmentStatus: (appointmentId: string, status: AppointmentStatus, cancelReason?: string, newScheduledAt?: Date) => Promise<void>
  // Quick filters
  getTodaysAppointments: () => AppointmentDetails[]
  getUpcomingAppointments: () => AppointmentDetails[]
  getPastAppointments: () => AppointmentDetails[]
  getAppointmentsByStatus: (status: AppointmentStatus) => AppointmentDetails[]
}

export const useAppointments = (options: UseAppointmentsOptions = {}): UseAppointmentsReturn => {
  const {
    dentistId,
    initialFilters = {},
    autoRefresh = false,
    refreshInterval = 30000 // 30 seconds
  } = options

  // State management
  const [appointments, setAppointments] = useState<AppointmentDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<AppointmentFilters>(initialFilters)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [totalPages, setTotalPages] = useState(0)

  const limit = 20 // Items per page

  // Build query parameters
  const buildQueryParams = useCallback((page = 1, currentFilters = filters) => {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sortBy: 'scheduledAt',
      sortOrder: 'asc'
    })

    if (dentistId) {
      params.append('dentistId', dentistId)
    }

    if (currentFilters.status) {
      params.append('status', currentFilters.status)
    }

    if (currentFilters.dateFrom) {
      params.append('dateFrom', currentFilters.dateFrom)
    }

    if (currentFilters.dateTo) {
      params.append('dateTo', currentFilters.dateTo)
    }

    if (currentFilters.patientName) {
      params.append('query', currentFilters.patientName)
    }

    if (currentFilters.serviceId) {
      params.append('serviceId', currentFilters.serviceId)
    }

    if (currentFilters.type) {
      params.append('type', currentFilters.type)
    }

    return params
  }, [dentistId, filters])

  // Fetch appointments
  const fetchAppointments = useCallback(async (page = 1, currentFilters = filters) => {
    try {
      setLoading(true)
      setError(null)

      const params = buildQueryParams(page, currentFilters)
      const response = await fetch(`/api/appointments?${params.toString()}`)

      if (!response.ok) {
        throw new Error('Failed to fetch appointments')
      }

      const data = await response.json()
      
      setAppointments(data.appointments || [])
      setTotalCount(data.totalCount || 0)
      setTotalPages(Math.ceil((data.totalCount || 0) / limit))
      setCurrentPage(page)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setAppointments([])
    } finally {
      setLoading(false)
    }
  }, [buildQueryParams, filters])

  // Refetch current data
  const refetch = useCallback(async () => {
    await fetchAppointments(currentPage, filters)
  }, [fetchAppointments, currentPage, filters])

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<AppointmentFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    setCurrentPage(1) // Reset to first page when filters change
    fetchAppointments(1, updatedFilters)
  }, [filters, fetchAppointments])

  // Clear all filters
  const clearFilters = useCallback(() => {
    const clearedFilters = {}
    setFilters(clearedFilters)
    setCurrentPage(1)
    fetchAppointments(1, clearedFilters)
  }, [fetchAppointments])

  // Pagination
  const loadNextPage = useCallback(async () => {
    if (currentPage < totalPages) {
      await fetchAppointments(currentPage + 1, filters)
    }
  }, [currentPage, totalPages, fetchAppointments, filters])

  const loadPrevPage = useCallback(async () => {
    if (currentPage > 1) {
      await fetchAppointments(currentPage - 1, filters)
    }
  }, [currentPage, fetchAppointments, filters])

  // Update appointment status
  const updateAppointmentStatus = useCallback(async (
    appointmentId: string,
    status: AppointmentStatus,
    cancelReason?: string,
    newScheduledAt?: Date
  ) => {
    try {
      const updateData: any = {
        status,
        ...(cancelReason && { cancelReason }),
        ...(newScheduledAt && { scheduledAt: newScheduledAt.toISOString() })
      }

      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update appointment status')
      }

      // Optimistically update the local state
      setAppointments(prev =>
        prev.map(appointment =>
          appointment.id === appointmentId
            ? {
                ...appointment,
                status,
                ...(cancelReason && { cancelReason }),
                ...(newScheduledAt && { scheduledAt: newScheduledAt })
              }
            : appointment
        )
      )

      // Refetch to ensure data consistency
      await refetch()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update appointment')
      throw err
    }
  }, [refetch])

  // Quick filter functions
  const getTodaysAppointments = useCallback(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.scheduledAt)
      return appointmentDate >= today && appointmentDate < tomorrow
    })
  }, [appointments])

  const getUpcomingAppointments = useCallback(() => {
    const now = new Date()
    return appointments.filter(appointment => 
      new Date(appointment.scheduledAt) > now
    )
  }, [appointments])

  const getPastAppointments = useCallback(() => {
    const now = new Date()
    return appointments.filter(appointment => 
      new Date(appointment.scheduledAt) < now
    )
  }, [appointments])

  const getAppointmentsByStatus = useCallback((status: AppointmentStatus) => {
    return appointments.filter(appointment => appointment.status === status)
  }, [appointments])

  // Initial fetch and auto-refresh setup
  useEffect(() => {
    fetchAppointments(1, filters)
  }, [fetchAppointments, filters]) // Include dependencies

  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        refetch()
      }, refreshInterval)

      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval, refetch])

  return {
    appointments,
    loading,
    error,
    filters,
    totalCount,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1,
    currentPage,
    totalPages,
    refetch,
    updateFilters,
    clearFilters,
    loadNextPage,
    loadPrevPage,
    updateAppointmentStatus,
    getTodaysAppointments,
    getUpcomingAppointments,
    getPastAppointments,
    getAppointmentsByStatus
  }
}
