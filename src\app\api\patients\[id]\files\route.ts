import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { UserRole, FileType } from '@prisma/client'
import { z } from 'zod'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

// Legacy schema for JSON uploads (kept for backward compatibility)
const _fileUploadSchema = z.object({
  fileName: z.string().min(1),
  originalName: z.string().min(1),
  fileType: z.nativeEnum(FileType),
  fileSize: z.number().min(1),
  mimeType: z.string().min(1),
  url: z.string().url(),
  description: z.string().optional(),
})

const formDataUploadSchema = z.object({
  fileType: z.nativeEnum(FileType),
  description: z.string().optional(),
})

// GET /api/patients/[id]/files - Get patient files
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const { searchParams } = new URL(req.url)
  
  // Check if patient exists
  const patientProfile = await prisma.patientProfile.findUnique({
    where: { id },
  })
  
  if (!patientProfile) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, id, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }

  const fileType = searchParams.get('type') as FileType | null
  const limit = Number(searchParams.get('limit')) || 50

  try {
    const files = await prisma.patientFile.findMany({
      where: {
        patientId: id,
        ...(fileType && { fileType }),
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    })

    return NextResponse.json({
      files,
      total: files.length,
    })
  } catch (error) {
    console.error('Error fetching patient files:', error)
    return NextResponse.json(
      { error: 'Failed to fetch patient files' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/patients/[id]/files - Upload patient file
export const POST = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  // Check if patient exists
  const patientProfile = await prisma.patientProfile.findUnique({
    where: { id },
  })
  
  if (!patientProfile) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions (only staff, admin, and dentists can upload files)
  if (!canAccessPatientData(user.role, id, user.id) || user.role === UserRole.PATIENT) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }

  try {
    const formData = await req.formData()
    const file = formData.get('file') as File
    const fileType = formData.get('fileType') as FileType
    const description = formData.get('description') as string | null

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate form data
    const validatedData = formDataUploadSchema.parse({
      fileType,
      description: description || undefined,
    })

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'public', 'uploads', 'patients', id)
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true })
    }

    // Generate unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split('.').pop()
    const fileName = `${timestamp}-${Math.random().toString(36).substring(2)}.${fileExtension}`
    const filePath = join(uploadsDir, fileName)

    // Save file to disk
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // Create database record
    const patientFile = await prisma.patientFile.create({
      data: {
        patientId: id,
        uploadedBy: user.id,
        fileName,
        originalName: file.name,
        fileType: validatedData.fileType,
        fileSize: file.size,
        mimeType: file.type,
        url: `/uploads/patients/${id}/${fileName}`,
        description: validatedData.description,
      },
    })

    return NextResponse.json(patientFile, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid file data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error uploading patient file:', error)
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])

// DELETE /api/patients/[id]/files - Delete patient file
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const { searchParams } = new URL(req.url)
  const fileId = searchParams.get('fileId')

  if (!fileId) {
    return NextResponse.json(
      { error: 'File ID is required' },
      { status: 400 }
    )
  }
  
  // Check if patient exists
  const patientProfile = await prisma.patientProfile.findUnique({
    where: { id },
  })
  
  if (!patientProfile) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions (only staff, admin, and dentists can delete files)
  if (!canAccessPatientData(user.role, id, user.id) || user.role === UserRole.PATIENT) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }

  try {
    // Check if file exists and belongs to this patient
    const file = await prisma.patientFile.findFirst({
      where: {
        id: fileId,
        patientId: id,
      },
    })

    if (!file) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    await prisma.patientFile.delete({
      where: { id: fileId },
    })

    return NextResponse.json({ message: 'File deleted successfully' })
  } catch (error) {
    console.error('Error deleting patient file:', error)
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])
