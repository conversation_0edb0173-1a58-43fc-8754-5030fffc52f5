import { NextRequest, NextResponse } from 'next/server'
import { testGoogleCalendarConfiguration, logGoogleCalendarStatus } from '@/lib/google-calendar-test'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/test/google-calendar - Test Google Calendar configuration
export const GET = withAuth(async (_req: NextRequest, _user) => {
  try {
    // Log configuration status to server console
    logGoogleCalendarStatus()
    
    // Get configuration details
    const config = testGoogleCalendarConfiguration()
    
    // Test basic Google Calendar service initialization
    let serviceStatus = 'unknown'
    let serviceError = null
    
    try {
      const { getGoogleCalendarService } = await import('@/lib/google-calendar')
      getGoogleCalendarService() // Test service initialization
      serviceStatus = 'initialized'
    } catch (error) {
      serviceStatus = 'failed'
      serviceError = error instanceof Error ? error.message : 'Unknown error'
    }
    
    return NextResponse.json({
      status: 'success',
      configuration: config,
      service: {
        status: serviceStatus,
        error: serviceError
      },
      environment: {
        hasGoogleClientId: !!process.env.GOOGLE_CLIENT_ID,
        hasGoogleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
        hasServiceAccountKey: !!process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE,
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary (default)',
        timezone: process.env.TIMEZONE || 'Asia/Manila (default)'
      },
      recommendations: config.recommendations
    })
  } catch (error) {
    console.error('Google Calendar test failed:', error)
    return NextResponse.json(
      { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to test Google Calendar configuration'
      },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF]) // Only admins and staff can test configuration
