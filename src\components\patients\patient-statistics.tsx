'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Users,
  UserPlus,
  Activity,
  FileText,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw
} from 'lucide-react'
import { PatientStatistics } from '@/types/patient'
import { cn } from '@/lib/utils'

interface PatientStatisticsProps {
  className?: string
}

export function PatientStatisticsCards({ className }: PatientStatisticsProps) {
  const [statistics, setStatistics] = useState<PatientStatistics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchStatistics = async () => {
    try {
      setError(null)
      const response = await fetch('/api/patients/statistics')

      if (!response.ok) {
        throw new Error('Failed to fetch statistics')
      }

      const data = await response.json()
      setStatistics(data)
      setLastUpdated(new Date())
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load statistics')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchStatistics()

    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchStatistics, 30000)

    return () => clearInterval(interval)
  }, [])

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color, 
    description,
    trend 
  }: {
    title: string
    value: number | string
    icon: React.ComponentType<{ className?: string }>
    color: string
    description: string
    trend?: 'up' | 'down' | 'neutral'
  }) => {
    const getTrendIcon = () => {
      switch (trend) {
        case 'up':
          return <TrendingUp className="h-3 w-3 text-green-600" />
        case 'down':
          return <TrendingDown className="h-3 w-3 text-red-600" />
        default:
          return <Minus className="h-3 w-3 text-gray-400" />
      }
    }

    return (
      <Card className="hover:shadow-md transition-shadow duration-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
          <Icon className={cn("h-4 w-4", color)} />
        </CardHeader>
        <CardContent>
          <div className="flex items-baseline space-x-2">
            <div className={cn("text-2xl font-bold", color)}>
              {typeof value === 'number' ? value.toLocaleString() : value}
            </div>
            {trend && getTrendIcon()}
          </div>
          <p className="text-xs text-gray-600 mt-1">{description}</p>
        </CardContent>
      </Card>
    )
  }

  const LoadingSkeleton = () => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-16 mb-2" />
        <Skeleton className="h-3 w-32" />
      </CardContent>
    </Card>
  )

  if (error) {
    return (
      <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", className)}>
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="border-red-200">
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="text-red-500 mb-2">
                  <Users className="h-6 w-6 mx-auto" />
                </div>
                <p className="text-sm text-red-600">Failed to load</p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (isLoading || !statistics) {
    return (
      <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", className)}>
        {[...Array(4)].map((_, i) => (
          <LoadingSkeleton key={i} />
        ))}
      </div>
    )
  }

  const getComparisonText = (current: number, previous: number, period: string) => {
    if (previous === 0) return `${current} ${period}`
    const change = current - previous
    const percentage = Math.round((change / previous) * 100)
    if (change > 0) return `+${change} (+${percentage}%) vs last ${period}`
    if (change < 0) return `${change} (${percentage}%) vs last ${period}`
    return `No change vs last ${period}`
  }

  return (
    <div className="space-y-4">
      {/* Last Updated Indicator */}
      {lastUpdated && (
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Statistics updated every 30 seconds</span>
          <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
        </div>
      )}

      <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6", className)}>
        <StatCard
          title="Total Patients"
          value={statistics.totalPatients}
          icon={Users}
          color="text-blue-600"
          description="Active patients"
          trend={statistics.trends.totalPatients}
        />

        <StatCard
          title="New This Month"
          value={statistics.newThisMonth}
          icon={UserPlus}
          color="text-green-600"
          description={getComparisonText(statistics.newThisMonth, statistics.newLastMonth, 'month')}
          trend={statistics.trends.newPatients}
        />

        <StatCard
          title="Active Treatments"
          value={statistics.activeTreatments}
          icon={Activity}
          color="text-purple-600"
          description={`${statistics.completedTreatmentsThisMonth} completed this month`}
          trend={statistics.trends.treatments}
        />

        <StatCard
          title="Records Updated"
          value={statistics.recordsUpdatedThisWeek}
          icon={FileText}
          color="text-orange-600"
          description={getComparisonText(statistics.recordsUpdatedThisWeek, statistics.recordsUpdatedLastWeek, 'week')}
          trend={statistics.trends.records}
        />
      </div>
    </div>
  )
}
