import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canAccessPatientData } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/patients/[id]/timeline - Get patient treatment timeline
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  // Check if patient exists
  const patientProfile = await prisma.patientProfile.findUnique({
    where: { id },
    include: { user: true },
  })
  
  if (!patientProfile) {
    return NextResponse.json(
      { error: 'Patient not found' },
      { status: 404 }
    )
  }
  
  // Check access permissions
  if (!canAccessPatientData(user.role, id, user.id)) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }

  try {
    // Get appointments
    const appointments = await prisma.appointment.findMany({
      where: { patientId: id },
      include: {
        service: { select: { name: true } },
        dentist: { 
          include: { 
            user: { select: { name: true } } 
          } 
        },
      },
      orderBy: { scheduledAt: 'desc' },
    })

    // Get treatments
    const treatments = await prisma.treatment.findMany({
      where: { patientId: id },
      include: {
        service: { select: { name: true } },
        dentist: { 
          include: { 
            user: { select: { name: true } } 
          } 
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    // Get payments
    const payments = await prisma.payment.findMany({
      where: {
        invoice: {
          patientId: id,
        },
      },
      include: {
        invoice: {
          select: {
            invoiceNumber: true,
            totalAmount: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    // Combine and sort timeline entries
    const timelineEntries = [
      ...appointments.map(apt => ({
        id: `appointment-${apt.id}`,
        date: apt.scheduledAt,
        type: 'appointment' as const,
        title: `${apt.service.name} Appointment`,
        description: `Appointment with Dr. ${apt.dentist.user.name}`,
        status: apt.status,
        dentist: apt.dentist.user.name,
      })),
      ...treatments.map(treatment => ({
        id: `treatment-${treatment.id}`,
        date: treatment.startedAt || treatment.createdAt,
        type: 'treatment' as const,
        title: treatment.service.name,
        description: treatment.diagnosis || treatment.procedure || 'Treatment performed',
        status: treatment.status,
        cost: Number(treatment.cost),
        dentist: treatment.dentist.user.name,
      })),
      ...payments.map(payment => ({
        id: `payment-${payment.id}`,
        date: payment.processedAt || payment.createdAt,
        type: 'payment' as const,
        title: `Payment - ${payment.invoice.invoiceNumber}`,
        description: `${payment.method} payment of ₱${Number(payment.amount).toLocaleString()}`,
        status: payment.status,
        cost: Number(payment.amount),
      })),
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    return NextResponse.json({
      timeline: timelineEntries,
      total: timelineEntries.length,
    })
  } catch (error) {
    console.error('Error fetching patient timeline:', error)
    return NextResponse.json(
      { error: 'Failed to fetch patient timeline' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])
