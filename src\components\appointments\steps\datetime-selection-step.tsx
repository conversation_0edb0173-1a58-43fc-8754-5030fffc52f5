'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { HealthcareCalendar } from '@/components/ui/healthcare-calendar'
import { Loader2, Clock, CheckCircle, Calendar as CalendarIcon } from 'lucide-react'
import { AvailabilityResponse } from '@/types/appointment'

interface DateTimeSelectionStepProps {
  dentistId: string
  duration: number
  selectedDateTime?: Date
  onSelect: (dateTime: Date) => void
  onNext: () => void
}

export function DateTimeSelectionStep({ 
  dentistId, 
  duration, 
  selectedDateTime, 
  onSelect, 
  onNext 
}: DateTimeSelectionStepProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(selectedDateTime)
  const [selectedTime, setSelectedTime] = useState<string | undefined>(
    selectedDateTime ? selectedDateTime.toTimeString().slice(0, 5) : undefined
  )
  const [availability, setAvailability] = useState<AvailabilityResponse | null>(null)
  const [isLoadingAvailability, setIsLoadingAvailability] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchAvailability = useCallback(async (date: Date) => {
    try {
      setIsLoadingAvailability(true)
      setError(null)
      
      const dateString = date.toISOString().split('T')[0]
      const response = await fetch(
        `/api/appointments/availability?dentistId=${dentistId}&date=${dateString}&duration=${duration}`
      )
      
      if (!response.ok) {
        throw new Error('Failed to fetch availability')
      }
      
      const data = await response.json()
      setAvailability(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load availability')
      setAvailability(null)
    } finally {
      setIsLoadingAvailability(false)
    }
  }, [dentistId, duration])

  useEffect(() => {
    if (selectedDate) {
      fetchAvailability(selectedDate)
    }
  }, [selectedDate, fetchAvailability])

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date)
    setSelectedTime(undefined)
    if (date) {
      fetchAvailability(date)
    }
  }

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time)
    if (selectedDate) {
      const [hours, minutes] = time.split(':').map(Number)
      const dateTime = new Date(selectedDate)
      dateTime.setHours(hours, minutes, 0, 0)
      onSelect(dateTime)
    }
  }

  const handleNext = () => {
    if (selectedDate && selectedTime) {
      onNext()
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':').map(Number)
    const date = new Date()
    date.setHours(hours, minutes)
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  // Disable past dates and Sundays (assuming clinic is closed on Sundays)
  const isDateDisabled = (date: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return date < today || date.getDay() === 0
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold text-gray-900">Select Date & Time</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Please select your preferred date and time for your appointment. Available time slots will be shown based on your selected date.
        </p>
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-8 lg:grid-cols-2 lg:gap-12">
        {/* Calendar Section */}
        <div className="space-y-4">
          <Card className="bg-white border-blue-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <CardHeader className="pb-6 border-b border-gray-100">
              <CardTitle className="text-xl text-gray-900 flex items-center">
                <CalendarIcon className="h-6 w-6 mr-3 text-blue-600" />
                Select Date
              </CardTitle>
              <CardDescription className="text-gray-600 text-base">
                Choose your preferred appointment date
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6 pb-8">
              <div className="flex justify-center">
                <div className="w-full max-w-md">
                  <HealthcareCalendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateSelect}
                    disabled={isDateDisabled}
                    className="w-full"
                  />
                </div>
              </div>
              {/* Date Selection Hint */}
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-xl border border-blue-200">
                <div className="flex items-start space-x-3">
                  <div className="w-3 h-3 bg-blue-600 rounded-full mt-1 flex-shrink-0"></div>
                  <div className="text-sm text-blue-800">
                    <p className="font-semibold mb-1">Clinic Hours</p>
                    <p>Monday through Saturday, 8:00 AM - 6:00 PM</p>
                    <p className="text-xs text-blue-600 mt-1">Sundays are closed for appointments</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Time Slots Section */}
        <div className="space-y-4">
          <Card className="bg-white border-blue-200 shadow-lg hover:shadow-xl transition-shadow duration-300 h-fit">
            <CardHeader className="pb-6 border-b border-gray-100">
              <CardTitle className="text-xl text-gray-900 flex items-center">
                <Clock className="h-6 w-6 mr-3 text-blue-600" />
                Select Time
              </CardTitle>
              <CardDescription className="text-gray-600 text-base">
                {selectedDate ? `Available times for ${formatDate(selectedDate)}` : 'Please select a date first'}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6 pb-8">
              {!selectedDate ? (
                <div className="text-center py-16 text-gray-500">
                  <div className="w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <Clock className="h-10 w-10 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium mb-2 text-gray-700">Select a Date First</h3>
                  <p className="text-sm text-gray-500 max-w-xs mx-auto">
                    Choose a date from the calendar to view available appointment times
                  </p>
                </div>
              ) : isLoadingAvailability ? (
                <div className="flex flex-col items-center justify-center py-16">
                  <Loader2 className="h-10 w-10 animate-spin text-blue-600 mb-6" />
                  <h3 className="text-lg font-medium text-gray-700 mb-2">Loading availability...</h3>
                  <p className="text-sm text-gray-500">Please wait while we check available times</p>
                </div>
              ) : error ? (
                <Alert variant="destructive" className="border-red-200 bg-red-50">
                  <AlertDescription className="text-red-800">{error}</AlertDescription>
                </Alert>
              ) : !availability?.available ? (
                <Alert className="border-amber-200 bg-amber-50">
                  <AlertDescription className="text-amber-800">
                    No available time slots for this date. Please select another date.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-6">
                  {/* Working Hours Info */}
                  <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-xl p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-600 rounded-full flex-shrink-0"></div>
                      <div>
                        <p className="text-sm font-semibold text-blue-900 mb-1">Working Hours</p>
                        <p className="text-sm text-blue-700">
                          {formatTime(availability.workingHours.start)} - {formatTime(availability.workingHours.end)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Time Slots Grid */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-700">Available Time Slots</h4>
                    <div className="max-h-96 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-blue-200 scrollbar-track-gray-100">
                      <div className="grid grid-cols-2 gap-3">
                        {availability.timeSlots
                          .filter(slot => slot.available)
                          .map((slot) => (
                            <Button
                              key={slot.startTime}
                              variant={selectedTime === slot.startTime ? "default" : "outline"}
                              size="default"
                              onClick={() => handleTimeSelect(slot.startTime)}
                              className={`h-12 justify-center transition-all duration-300 font-medium ${
                                selectedTime === slot.startTime
                                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg scale-105 border-blue-600'
                                  : 'hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 border-gray-300 bg-white text-gray-700'
                              }`}
                            >
                              <div className="flex items-center space-x-2">
                                <span>{formatTime(slot.startTime)}</span>
                                {selectedTime === slot.startTime && (
                                  <CheckCircle className="h-4 w-4" />
                                )}
                              </div>
                            </Button>
                          ))}
                      </div>
                    </div>
                  </div>

                  {availability.timeSlots.filter(slot => slot.available).length === 0 && (
                    <Alert className="border-amber-200 bg-amber-50">
                      <AlertDescription className="text-amber-800">
                        No available time slots for this date. Please select another date.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Selected Date/Time Summary */}
      {selectedDate && selectedTime && (
        <div className="space-y-6">
          <Card className="bg-gradient-to-r from-blue-50 to-green-50 border-blue-200 shadow-lg">
            <CardContent className="pt-6 pb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">Selected Appointment Time</h4>
                    <p className="text-blue-700 font-medium">
                      {formatDate(selectedDate)} at {formatTime(selectedTime)}
                    </p>
                    <p className="text-sm text-gray-600 mt-1">
                      Duration: {duration} minutes
                    </p>
                  </div>
                </div>
                <div className="hidden sm:flex items-center space-x-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  <span className="text-sm font-medium">Confirmed</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Continue Button */}
          <div className="flex justify-center sm:justify-end">
            <Button
              onClick={handleNext}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Continue to Patient Information
              <CheckCircle className="h-5 w-5 ml-2" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
