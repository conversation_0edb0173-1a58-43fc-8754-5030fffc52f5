import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { serviceCreateSchema, paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/services - List services with pagination and filtering
export const GET = withAuth(async (req: NextRequest, _user) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 20,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'name',
    sortOrder: searchParams.get('sortOrder') || 'asc',
  })
  
  const category = searchParams.get('category')
  const isActive = searchParams.get('isActive')
  const branchId = searchParams.get('branchId')
  
  const skip = (page - 1) * limit
  
  // Build where clause
  const where: Record<string, unknown> = {}
  
  if (category) where.category = category
  if (isActive !== null) where.isActive = isActive === 'true'
  
  // Search filter
  if (query) {
    where.OR = [
      { name: { contains: query, mode: 'insensitive' } },
      { description: { contains: query, mode: 'insensitive' } },
      { category: { contains: query, mode: 'insensitive' } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'name' ? { name: sortOrder } :
                  sortBy === 'category' ? { category: sortOrder } :
                  sortBy === 'price' ? { price: sortOrder } :
                  sortBy === 'duration' ? { duration: sortOrder } :
                  { name: sortOrder as 'asc' | 'desc' }

  const [services, total] = await Promise.all([
    prisma.service.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        branches: branchId ? {
          where: { branchId },
        } : true,
        _count: {
          select: {
            appointments: true,
            treatments: true,
          },
        },
      },
    }),
    prisma.service.count({ where }),
  ])
  
  return NextResponse.json({
    services,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// POST /api/services - Create new service
export const POST = withAuth(async (req: NextRequest, _user) => {
  const body = await req.json()
  const validatedData = serviceCreateSchema.parse(body)
  
  // Create service
  const service = await prisma.service.create({
    data: validatedData,
    include: {
      _count: {
        select: {
          appointments: true,
          treatments: true,
        },
      },
    },
  })
  
  return NextResponse.json(service, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF])
