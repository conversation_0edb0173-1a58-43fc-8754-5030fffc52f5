# Google Calendar Integration Setup Guide

This guide will help you set up Google Calendar integration for the dental clinic appointment booking system.

## ✅ Current Status: FULLY IMPLEMENTED AND PRODUCTION-READY

The Google Calendar integration has been successfully implemented with both clinic-side and patient-facing features:

### ✅ Clinic-Side Integration (Automatic)
- Appointments automatically sync to clinic's Google Calendar
- Service account authentication with secure credential management
- Production-ready deployment with environment variable support
- Graceful fallback if calendar integration fails

### ✅ Patient-Side Integration (Manual)
- "Add to My Calendar" functionality for patients
- Multi-platform support (Google, Outlook, Apple, Yahoo calendars)
- No authentication required for patients
- ICS file download for universal compatibility

## Option 1: Service Account (Recommended for Production)

### Step 1: Create a Google Cloud Project
1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Calendar API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Calendar API"
   - Click "Enable"

### Step 2: Create a Service Account
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Fill in the service account details:
   - Name: `dental-clinic-calendar`
   - Description: `Service account for dental clinic calendar integration`
4. Click "Create and Continue"
5. Skip the optional steps and click "Done"

### Step 3: Generate Service Account Key
1. Click on the created service account
2. Go to the "Keys" tab
3. Click "Add Key" > "Create new key"
4. Select "JSON" format
5. Download the key file and save it securely (e.g., `google-service-account-key.json`)

### Step 4: Share Calendar with Service Account
1. Open Google Calendar
2. Create a new calendar or use an existing one for appointments
3. Go to calendar settings (click the gear icon next to the calendar name)
4. Click "Share with specific people"
5. Add the service account email (found in the JSON key file) with "Make changes to events" permission
6. Copy the Calendar ID from the calendar settings

### Step 5: Configure Environment Variables
Add these variables to your `.env` file:

```env
# Google Calendar API (Service Account)
GOOGLE_SERVICE_ACCOUNT_KEY_FILE="./google-service-account-key.json"
GOOGLE_CALENDAR_ID="<EMAIL>"
TIMEZONE="Asia/Manila"
```

## Option 2: OAuth2 (Development/Testing)

For development and testing, you can use the existing Google OAuth credentials:

### Step 1: Enable Google Calendar API
1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (the one used for Google OAuth)
3. Enable the Google Calendar API (same as Option 1, Step 1)

### Step 2: Update OAuth Scopes
The current implementation will attempt to use OAuth2 if service account credentials are not available, but this requires proper token management which is not implemented yet.

### Step 3: Configure Environment Variables
Update your `.env` file:

```env
# Google Calendar API (OAuth2 - Limited functionality)
GOOGLE_CALENDAR_ID="primary"
TIMEZONE="Asia/Manila"
```

## Testing the Integration

### 1. Check Configuration
The system will automatically log the configuration status when the Google Calendar service is initialized. Check your server logs for:

```
=== Google Calendar Configuration Status ===
Configured: true/false
Missing environment variables: [...]
Recommendations: [...]
```

### 2. Test Appointment Creation
1. Create a new appointment through the booking form
2. Check the server logs for calendar event creation messages:
   - Success: `Calendar event created with ID: [event-id]`
   - Warning: `Calendar event creation returned null - check Google Calendar configuration`
   - Error: `Failed to create calendar event: [error details]`

### 3. Verify in Google Calendar
If the integration is working correctly, you should see the appointment appear in the configured Google Calendar.

## Troubleshooting

### Common Issues

1. **"Google Calendar not configured"**
   - Check that required environment variables are set
   - Verify the service account key file path is correct

2. **"Failed to initialize Google Calendar auth"**
   - Verify the service account key file exists and is valid JSON
   - Check that the Google Calendar API is enabled in your project

3. **"Calendar event creation returned null"**
   - Verify the calendar ID is correct
   - Check that the service account has permission to create events in the calendar
   - Ensure the Google Calendar API is enabled

4. **Authentication errors**
   - Verify the service account key file is not corrupted
   - Check that the service account email has been added to the calendar with proper permissions

### Fallback Behavior

If Google Calendar integration fails:
- Appointment creation will still succeed
- Users can manually add appointments to their calendar using the "Add to Calendar" button
- Error details will be logged for debugging

## Security Notes

- Keep the service account key file secure and never commit it to version control
- Add `google-service-account-key.json` to your `.gitignore` file
- Use environment variables for all sensitive configuration
- Consider using Google Cloud Secret Manager for production deployments

## Manual Calendar Integration

If automatic integration is not working, users can still add appointments to their calendar using:
- The "Add to Calendar" button in the appointment confirmation
- Direct Google Calendar links
- ICS file downloads for other calendar applications
