'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { AppointmentList } from '@/components/appointments/appointment-list'
import { RescheduleAppointmentModal } from '@/components/appointments/modals/reschedule-appointment-modal'
import { CancelAppointmentModal } from '@/components/appointments/modals/cancel-appointment-modal'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Calendar, Clock, FileText, Plus } from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'
import { UserRole } from '@prisma/client'

export default function UpcomingAppointmentsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [error] = useState<string | null>(null)
  const [refreshKey, setRefresh<PERSON>ey] = useState(0)

  // Modal states
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentDetails | null>(null)
  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false)
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/appointments/upcoming')
    } else if (isAuthenticated && user?.role !== UserRole.PATIENT) {
      router.push('/')
    }
  }, [isAuthenticated, isLoading, user, router])

  const handleViewDetails = (appointmentId: string) => {
    router.push(`/appointments/${appointmentId}`)
  }

  const handleReschedule = (appointment: AppointmentDetails) => {
    setSelectedAppointment(appointment)
    setIsRescheduleModalOpen(true)
  }

  const handleCancel = (appointment: AppointmentDetails) => {
    setSelectedAppointment(appointment)
    setIsCancelModalOpen(true)
  }

  const handleBookNew = () => {
    router.push('/appointments/book')
  }

  const handleGoBack = () => {
    router.push('/dashboard')
  }

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your upcoming appointments...</p>
        </div>
      </div>
    )
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-gray-900">Authentication Required</CardTitle>
                <CardDescription>
                  Please sign in to view your upcoming appointments
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button 
                  onClick={() => router.push('/auth/signin?callbackUrl=/appointments/upcoming')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Sign In to Continue
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              onClick={handleGoBack}
              variant="outline"
              size="sm"
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Upcoming Appointments</h1>
              <p className="text-gray-600 mt-1">
                Manage your scheduled dental appointments
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={() => router.push('/appointments/history')}
                variant="outline"
                size="sm"
                className="flex items-center"
              >
                <FileText className="h-4 w-4 mr-2" />
                Appointment History
              </Button>
              <Button
                onClick={handleBookNew}
                className="bg-blue-600 hover:bg-blue-700 flex items-center"
                size="sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Book New Appointment
              </Button>
            </div>
          </div>
        </div>

        {/* Welcome message */}
        <Card className="bg-white/70 backdrop-blur-sm border-blue-200 mb-8">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Your Scheduled Appointments</p>
                <p className="text-sm text-gray-600">
                  View, reschedule, or cancel your upcoming dental appointments.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Upcoming Appointments List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-blue-600" />
              Scheduled Appointments
            </CardTitle>
            <CardDescription>
              Your confirmed and pending appointments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AppointmentList
              key={refreshKey}
              onReschedule={handleReschedule}
              onCancel={handleCancel}
              onViewDetails={handleViewDetails}
              onBookNew={handleBookNew}
              showActions={true}
              statusFilter="SCHEDULED,CONFIRMED,PENDING"
              sortBy="scheduledAt"
              sortOrder="asc"
            />
          </CardContent>
        </Card>

        {/* Modals */}
        {selectedAppointment && (
          <>
            <RescheduleAppointmentModal
              appointment={selectedAppointment}
              isOpen={isRescheduleModalOpen}
              onClose={() => {
                setIsRescheduleModalOpen(false)
                setSelectedAppointment(null)
              }}
              onSuccess={() => {
                setIsRescheduleModalOpen(false)
                setSelectedAppointment(null)
                handleRefresh()
              }}
            />
            
            <CancelAppointmentModal
              appointment={selectedAppointment}
              isOpen={isCancelModalOpen}
              onClose={() => {
                setIsCancelModalOpen(false)
                setSelectedAppointment(null)
              }}
              onSuccess={() => {
                setIsCancelModalOpen(false)
                setSelectedAppointment(null)
                handleRefresh()
              }}
            />
          </>
        )}
      </div>
    </div>
  )
}
