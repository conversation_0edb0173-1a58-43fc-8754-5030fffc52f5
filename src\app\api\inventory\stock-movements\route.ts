import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth, canManageInventory } from '@/lib/auth-utils'
import { paginationSchema, searchSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'
import { z } from 'zod'

const stockMovementCreateSchema = z.object({
  itemId: z.string().cuid(),
  type: z.enum(['IN', 'OUT', 'ADJUSTMENT']),
  quantity: z.number().int().min(1),
  reason: z.string().min(1),
  reference: z.string().optional(),
  notes: z.string().optional(),
})

// GET /api/inventory/stock-movements - List stock movements
export const GET = withAuth(async (req: NextRequest, _user) => {
  const { searchParams } = new URL(req.url)
  
  const { page, limit } = paginationSchema.parse({
    page: Number(searchParams.get('page')) || 1,
    limit: Number(searchParams.get('limit')) || 20,
  })
  
  const { query, sortBy, sortOrder } = searchSchema.parse({
    query: searchParams.get('query') || undefined,
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc',
  })
  
  const itemId = searchParams.get('itemId')
  const type = searchParams.get('type')
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')
  
  const skip = (page - 1) * limit
  
  // Build where clause
  const where: Record<string, unknown> = {}
  
  if (itemId) where.itemId = itemId
  if (type) where.type = type
  
  // Date range filter
  if (dateFrom || dateTo) {
    where.createdAt = {}
    if (dateFrom) (where.createdAt as any).gte = new Date(dateFrom)
    if (dateTo) (where.createdAt as any).lte = new Date(dateTo)
  }
  
  // Search filter
  if (query) {
    where.OR = [
      { reason: { contains: query, mode: 'insensitive' } },
      { reference: { contains: query, mode: 'insensitive' } },
      { notes: { contains: query, mode: 'insensitive' } },
      { item: { name: { contains: query, mode: 'insensitive' } } },
      { item: { sku: { contains: query, mode: 'insensitive' } } },
    ]
  }
  
  // Create type-safe orderBy
  const orderBy = sortBy === 'createdAt' ? { createdAt: sortOrder } :
                  sortBy === 'type' ? { type: sortOrder } :
                  sortBy === 'quantity' ? { quantity: sortOrder } :
                  { createdAt: sortOrder as 'asc' | 'desc' }

  const [movements, total] = await Promise.all([
    prisma.stockMovement.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      include: {
        item: {
          select: {
            id: true,
            name: true,
            sku: true,
            unit: true,
            branch: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    }),
    prisma.stockMovement.count({ where }),
  ])
  
  return NextResponse.json({
    movements,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  })
}, [UserRole.ADMIN, UserRole.STAFF])

// POST /api/inventory/stock-movements - Create stock movement
export const POST = withAuth(async (req: NextRequest, user) => {
  if (!canManageInventory(user.role)) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    )
  }
  
  const body = await req.json()
  const validatedData = stockMovementCreateSchema.parse(body)
  
  // Verify inventory item exists
  const item = await prisma.inventoryItem.findUnique({
    where: { id: validatedData.itemId },
  })
  
  if (!item) {
    return NextResponse.json(
      { error: 'Inventory item not found' },
      { status: 404 }
    )
  }
  
  // Calculate new stock level
  let newStock = item.currentStock
  
  switch (validatedData.type) {
    case 'IN':
      newStock += validatedData.quantity
      break
    case 'OUT':
      newStock -= validatedData.quantity
      if (newStock < 0) {
        return NextResponse.json(
          { error: 'Insufficient stock' },
          { status: 400 }
        )
      }
      break
    case 'ADJUSTMENT':
      // For adjustments, the quantity represents the new total stock
      newStock = validatedData.quantity
      break
  }
  
  // Create stock movement and update inventory in a transaction
  const [movement] = await prisma.$transaction([
    prisma.stockMovement.create({
      data: {
        ...validatedData,
        performedBy: user.id,
      },
      include: {
        item: {
          select: {
            id: true,
            name: true,
            sku: true,
            unit: true,
            currentStock: true,
            branch: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    }),
    prisma.inventoryItem.update({
      where: { id: validatedData.itemId },
      data: { currentStock: newStock },
    }),
  ])
  
  return NextResponse.json(movement, { status: 201 })
}, [UserRole.ADMIN, UserRole.STAFF])
