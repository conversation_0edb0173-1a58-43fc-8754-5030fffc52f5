import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/patients/statistics - Get patient statistics for dashboard
export const GET = withAuth(async (_req: NextRequest) => {
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

  try {
    // Get total patients count
    const totalPatients = await prisma.patientProfile.count({
      where: {
        user: {
          status: 'ACTIVE'
        }
      }
    })

    // Get new patients this month
    const newThisMonth = await prisma.patientProfile.count({
      where: {
        createdAt: {
          gte: startOfMonth
        },
        user: {
          status: 'ACTIVE'
        }
      }
    })

    // Get active treatments count
    const activeTreatments = await prisma.treatment.count({
      where: {
        status: {
          in: ['PLANNED', 'IN_PROGRESS']
        }
      }
    })

    // Get records updated this week
    const recordsUpdatedThisWeek = await prisma.patientProfile.count({
      where: {
        updatedAt: {
          gte: startOfWeek
        }
      }
    })

    // Get recent patients (last 5)
    const recentPatients = await prisma.patientProfile.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
            phone: true,
            status: true,
            createdAt: true,
          }
        }
      }
    })

    return NextResponse.json({
      totalPatients,
      newThisMonth,
      activeTreatments,
      recordsUpdatedThisWeek,
      recentPatients
    })
  } catch (error) {
    console.error('Error fetching patient statistics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch patient statistics' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])
