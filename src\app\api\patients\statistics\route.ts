import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { UserRole } from '@prisma/client'

// GET /api/patients/statistics - Get patient statistics for dashboard
export const GET = withAuth(async (_req: NextRequest) => {
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
  const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const startOfLastWeek = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000)

  try {
    // Get total patients count
    const totalPatients = await prisma.patientProfile.count({
      where: {
        user: {
          status: 'ACTIVE'
        }
      }
    })

    // Get new patients this month
    const newThisMonth = await prisma.patientProfile.count({
      where: {
        createdAt: {
          gte: startOfMonth
        },
        user: {
          status: 'ACTIVE'
        }
      }
    })

    // Get new patients last month for comparison
    const newLastMonth = await prisma.patientProfile.count({
      where: {
        createdAt: {
          gte: startOfLastMonth,
          lte: endOfLastMonth
        },
        user: {
          status: 'ACTIVE'
        }
      }
    })

    // Get active treatments count
    const activeTreatments = await prisma.treatment.count({
      where: {
        status: {
          in: ['PLANNED', 'IN_PROGRESS']
        }
      }
    })

    // Get completed treatments this month for comparison
    const completedTreatmentsThisMonth = await prisma.treatment.count({
      where: {
        status: 'COMPLETED',
        updatedAt: {
          gte: startOfMonth
        }
      }
    })

    // Get records updated this week
    const recordsUpdatedThisWeek = await prisma.patientProfile.count({
      where: {
        updatedAt: {
          gte: startOfWeek
        }
      }
    })

    // Get records updated last week for comparison
    const recordsUpdatedLastWeek = await prisma.patientProfile.count({
      where: {
        updatedAt: {
          gte: startOfLastWeek,
          lt: startOfWeek
        }
      }
    })

    // Get recent patients (last 5)
    const recentPatients = await prisma.patientProfile.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            image: true,
            phone: true,
            status: true,
            createdAt: true,
          }
        }
      }
    })

    // Calculate trends
    const newPatientsTrend = newLastMonth === 0 ? 'neutral' :
      newThisMonth > newLastMonth ? 'up' :
      newThisMonth < newLastMonth ? 'down' : 'neutral'

    const treatmentsTrend = completedTreatmentsThisMonth > 0 ? 'up' : 'neutral'

    const recordsTrend = recordsUpdatedLastWeek === 0 ? 'neutral' :
      recordsUpdatedThisWeek > recordsUpdatedLastWeek ? 'up' :
      recordsUpdatedThisWeek < recordsUpdatedLastWeek ? 'down' : 'neutral'

    return NextResponse.json({
      totalPatients,
      newThisMonth,
      newLastMonth,
      activeTreatments,
      completedTreatmentsThisMonth,
      recordsUpdatedThisWeek,
      recordsUpdatedLastWeek,
      recentPatients,
      trends: {
        newPatients: newPatientsTrend,
        treatments: treatmentsTrend,
        records: recordsTrend,
        totalPatients: totalPatients > 0 ? 'up' : 'neutral'
      },
      lastUpdated: now.toISOString()
    })
  } catch (error) {
    console.error('Error fetching patient statistics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch patient statistics' },
      { status: 500 }
    )
  }
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST])
