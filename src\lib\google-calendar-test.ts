// Test utility for Google Calendar integration
import { AppointmentDetails } from '@/types/appointment'

export function testGoogleCalendarConfiguration(): {
  isConfigured: boolean
  missingVars: string[]
  recommendations: string[]
} {
  const missingVars: string[] = []
  const recommendations: string[] = []

  // Check for service account configuration
  if (!process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE) {
    missingVars.push('GOOGLE_SERVICE_ACCOUNT_KEY_FILE')
  }

  // Check for OAuth2 configuration
  if (!process.env.GOOGLE_CLIENT_ID) {
    missingVars.push('GOOGLE_CLIENT_ID')
  }

  if (!process.env.GOOGLE_CLIENT_SECRET) {
    missingVars.push('GOOGLE_CLIENT_SECRET')
  }

  // Check for calendar ID
  if (!process.env.GOOGLE_CALENDAR_ID) {
    recommendations.push('Set GOOGLE_CALENDAR_ID to specify which calendar to use (defaults to "primary")')
  }

  // Check for timezone
  if (!process.env.TIMEZONE) {
    recommendations.push('Set TIMEZONE for proper event scheduling (defaults to "Asia/Manila")')
  }

  const isConfigured = !!(
    process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE || 
    (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET)
  )

  if (!isConfigured) {
    recommendations.push(
      'To enable Google Calendar integration, you need either:',
      '1. Service Account: Set GOOGLE_SERVICE_ACCOUNT_KEY_FILE to the path of your service account JSON file',
      '2. OAuth2: Ensure GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are set (same as NextAuth Google provider)'
    )
  }

  return {
    isConfigured,
    missingVars,
    recommendations
  }
}

export function createMockCalendarEvent(appointment: AppointmentDetails): {
  summary: string
  description: string
  start: string
  end: string
  location: string
  attendees: string[]
} {
  const startTime = new Date(appointment.scheduledAt)
  const endTime = new Date(startTime.getTime() + appointment.duration * 60000)

  return {
    summary: `${appointment.service.name} - ${appointment.patient.user.name}`,
    description: `Dental appointment with Dr. ${appointment.dentist.user.name} at ${appointment.branch.name}`,
    start: startTime.toISOString(),
    end: endTime.toISOString(),
    location: `${appointment.branch.name}, ${appointment.branch.address}`,
    attendees: [appointment.patient.user.email]
  }
}

export function generateGoogleCalendarUrl(appointment: AppointmentDetails): string {
  const event = createMockCalendarEvent(appointment)
  const startTime = new Date(appointment.scheduledAt)
  const endTime = new Date(startTime.getTime() + appointment.duration * 60000)
  
  const formatDateForUrl = (date: Date): string => {
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'
  }

  const params = new URLSearchParams({
    action: 'TEMPLATE',
    text: event.summary,
    dates: `${formatDateForUrl(startTime)}/${formatDateForUrl(endTime)}`,
    details: event.description,
    location: event.location
  })

  return `https://calendar.google.com/calendar/render?${params.toString()}`
}

// Log configuration status for debugging
export function logGoogleCalendarStatus(): void {
  const config = testGoogleCalendarConfiguration()
  
  console.log('=== Google Calendar Configuration Status ===')
  console.log('Configured:', config.isConfigured)
  
  if (config.missingVars.length > 0) {
    console.log('Missing environment variables:', config.missingVars)
  }
  
  if (config.recommendations.length > 0) {
    console.log('Recommendations:')
    config.recommendations.forEach(rec => console.log(`  - ${rec}`))
  }
  
  console.log('Environment variables present:')
  console.log('  GOOGLE_CLIENT_ID:', !!process.env.GOOGLE_CLIENT_ID)
  console.log('  GOOGLE_CLIENT_SECRET:', !!process.env.GOOGLE_CLIENT_SECRET)
  console.log('  GOOGLE_SERVICE_ACCOUNT_KEY_FILE:', !!process.env.GOOGLE_SERVICE_ACCOUNT_KEY_FILE)
  console.log('  GOOGLE_CALENDAR_ID:', process.env.GOOGLE_CALENDAR_ID || 'primary (default)')
  console.log('  TIMEZONE:', process.env.TIMEZONE || 'Asia/Manila (default)')
  console.log('===========================================')
}
