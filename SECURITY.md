# Security Guidelines

## Google Service Account Key Management

### Current Setup
- Service account key file is stored in `config/secrets/google-service-account-key.json`
- This directory is excluded from version control via `.gitignore`
- File permissions should be restricted to the application user only

### Security Best Practices

#### 1. File Permissions
Ensure the service account key file has restricted permissions:
```bash
# Set file permissions (Linux/macOS)
chmod 600 config/secrets/google-service-account-key.json

# On Windows, use File Properties > Security to restrict access
```

#### 2. Environment Variables
- Never hardcode service account keys in source code
- Use environment variables to reference file paths
- Different environments should use different service accounts

#### 3. Production Deployment
For production deployments, consider these alternatives:

**Option A: Google Cloud Secret Manager**
```bash
# Store the key in Secret Manager
gcloud secrets create google-calendar-key --data-file=config/secrets/google-service-account-key.json

# Access in application
const secret = await secretManager.accessSecretVersion({
  name: 'projects/PROJECT_ID/secrets/google-calendar-key/versions/latest'
});
```

**Option B: Environment Variable (Base64 encoded)**
```bash
# Encode the key file
base64 config/secrets/google-service-account-key.json

# Set as environment variable
export GOOGLE_SERVICE_ACCOUNT_KEY_BASE64="encoded_key_here"
```

**Option C: Container Secrets (Docker/Kubernetes)**
```yaml
# Kubernetes Secret
apiVersion: v1
kind: Secret
metadata:
  name: google-calendar-key
type: Opaque
data:
  key.json: <base64-encoded-key>
```

#### 4. Service Account Permissions
The service account should have minimal required permissions:
- Google Calendar API access
- Specific calendar read/write permissions only
- No unnecessary Google Cloud project permissions

#### 5. Key Rotation
- Regularly rotate service account keys (recommended: every 90 days)
- Monitor service account usage in Google Cloud Console
- Remove unused or old keys immediately

### Development Environment
- Each developer should have their own service account for testing
- Use separate Google Cloud projects for development and production
- Never share service account keys via email, chat, or other insecure channels

### Monitoring and Auditing
- Enable Google Cloud Audit Logs for service account usage
- Monitor API usage and quotas
- Set up alerts for unusual activity

### Emergency Procedures
If a service account key is compromised:
1. Immediately disable the service account in Google Cloud Console
2. Generate a new key with a new service account
3. Update the application configuration
4. Review audit logs for unauthorized access
5. Notify the security team

### Backup and Recovery
- Service account keys should be backed up securely
- Document the recovery process for key replacement
- Test the recovery process regularly

## Additional Security Considerations

### Database Security
- Use connection pooling with proper authentication
- Enable SSL/TLS for database connections
- Regularly update database credentials

### API Security
- Implement rate limiting
- Use HTTPS for all API endpoints
- Validate and sanitize all input data
- Implement proper authentication and authorization

### Environment Variables
- Never commit `.env` files to version control
- Use different environment files for different stages
- Regularly audit environment variable usage

### Dependencies
- Regularly update npm packages
- Use `npm audit` to check for vulnerabilities
- Pin dependency versions in production

### Logging and Monitoring
- Log security-relevant events
- Monitor for suspicious activity
- Implement proper error handling without exposing sensitive information
