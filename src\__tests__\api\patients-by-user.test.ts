import { prisma } from '@/lib/prisma'

jest.mock('@/lib/prisma')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('Google OAuth Patient Profile Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Patient Profile Creation for Google OAuth Users', () => {
    it('should create patient profile when Google OAuth user signs in', async () => {
      const userId = 'google-oauth-user-id'

      // Mock that no existing profile exists
      mockPrisma.patientProfile.findUnique.mockResolvedValue(null)

      // Mock successful profile creation
      const mockCreatedProfile = {
        id: 'new-patient-profile-id',
        userId: userId,
      }
      mockPrisma.patientProfile.create.mockResolvedValue(mockCreatedProfile as any)

      // Simulate the signIn callback logic from auth.ts
      const existingProfile = await mockPrisma.patientProfile.findUnique({
        where: { userId },
      })

      let result = null
      if (!existingProfile) {
        result = await mockPrisma.patientProfile.create({
          data: { userId },
        })
      }

      expect(mockPrisma.patientProfile.findUnique).toHaveBeenCalledWith({
        where: { userId },
      })
      expect(mockPrisma.patientProfile.create).toHaveBeenCalledWith({
        data: { userId },
      })
      expect(result?.id).toBe('new-patient-profile-id')
      expect(result?.userId).toBe(userId)
    })

    it('should not create duplicate profile if one already exists', async () => {
      const userId = 'google-oauth-user-id'

      // Mock that profile already exists
      const existingProfile = {
        id: 'existing-patient-profile-id',
        userId: userId,
      }
      mockPrisma.patientProfile.findUnique.mockResolvedValue(existingProfile as any)

      // Simulate the signIn callback logic from auth.ts
      const existingProfileResult = await mockPrisma.patientProfile.findUnique({
        where: { userId },
      })

      // Should not create if profile already exists
      if (!existingProfileResult) {
        await mockPrisma.patientProfile.create({
          data: { userId },
        })
      }

      expect(mockPrisma.patientProfile.findUnique).toHaveBeenCalledWith({
        where: { userId },
      })
      expect(existingProfileResult).toBeTruthy()
      expect(existingProfileResult?.id).toBe('existing-patient-profile-id')
      // Should not call create if profile already exists
      expect(mockPrisma.patientProfile.create).not.toHaveBeenCalled()
    })
  })

  describe('Appointment Booking Integration', () => {
    it('should find patient profile by userId for appointment booking', async () => {
      const userId = 'google-oauth-user-id'
      const patientProfileId = 'patient-profile-id'

      // Mock finding patient profile by userId
      const mockPatientProfile = {
        id: patientProfileId,
        userId: userId,
        user: {
          id: userId,
          email: '<EMAIL>',
          name: 'Google OAuth Patient',
        },
      }

      mockPrisma.patientProfile.findUnique.mockResolvedValue(mockPatientProfile as any)

      // Simulate the appointment booking form logic
      const patientProfile = await mockPrisma.patientProfile.findUnique({
        where: { userId },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              phone: true,
              image: true,
            },
          },
        },
      })

      expect(mockPrisma.patientProfile.findUnique).toHaveBeenCalledWith({
        where: { userId },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              phone: true,
              image: true,
            },
          },
        },
      })
      expect(patientProfile).toBeTruthy()
      expect(patientProfile?.id).toBe(patientProfileId)
      expect(patientProfile?.userId).toBe(userId)
    })
  })
})
