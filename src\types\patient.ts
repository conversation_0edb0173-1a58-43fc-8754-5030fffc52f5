import { 
  UserStatus, 
  AppointmentStatus, 
  AppointmentType, 
  TreatmentStatus, 
  PaymentStatus, 
  PaymentMethod, 
  FileType, 
  ToothCondition 
} from '@prisma/client'

// Base User Information
export interface User {
  id: string
  email: string
  name: string | null
  image: string | null
  phone: string | null
  status: UserStatus
  createdAt: Date
  updatedAt: Date
}

// Patient Profile
export interface PatientProfile {
  id: string
  userId: string
  dateOfBirth: Date | null
  gender: string | null
  address: string | null
  emergencyContact: string | null
  emergencyPhone: string | null
  insuranceProvider: string | null
  insuranceNumber: string | null
  medicalHistory: string | null
  allergies: string | null
  medications: string | null
  createdAt: Date
  updatedAt: Date
  user: User
}

// Extended Patient with Counts
export interface PatientWithCounts extends PatientProfile {
  _count: {
    appointments: number
    treatments: number
    invoices: number
  }
}

// Comprehensive Patient Data
export interface PatientDetails extends PatientProfile {
  appointments: PatientAppointment[]
  treatments: PatientTreatment[]
  invoices: PatientInvoice[]
  dentalChart: DentalChart | null
  files: PatientFile[]
}

// Appointment related to patient
export interface PatientAppointment {
  id: string
  scheduledAt: Date
  duration: number
  status: AppointmentStatus
  type: AppointmentType
  notes: string | null
  symptoms: string | null
  service: {
    name: string
    category: string
  }
  dentist: {
    id: string
    user: {
      name: string
    }
    specialization: string | null
  }
  branch: {
    name: string
    address: string
  }
}

// Treatment related to patient
export interface PatientTreatment {
  id: string
  status: TreatmentStatus
  diagnosis: string | null
  procedure: string | null
  notes: string | null
  cost: number
  startedAt: Date | null
  completedAt: Date | null
  createdAt: Date
  service: {
    name: string
    category: string
  }
  dentist: {
    user: {
      name: string
    }
  }
  prescriptions: Prescription[]
}

// Prescription
export interface Prescription {
  id: string
  medication: string
  dosage: string
  frequency: string
  duration: string
  instructions: string | null
  createdAt: Date
}

// Invoice related to patient
export interface PatientInvoice {
  id: string
  invoiceNumber: string
  totalAmount: number
  paidAmount: number
  status: PaymentStatus
  dueDate: Date
  issuedAt: Date
  paidAt: Date | null
  notes: string | null
  items: InvoiceItem[]
  payments: Payment[]
}

// Invoice Item
export interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
  treatment: {
    service: {
      name: string
    }
  } | null
}

// Payment
export interface Payment {
  id: string
  amount: number
  method: PaymentMethod
  transactionId: string | null
  status: PaymentStatus
  processedAt: Date | null
  notes: string | null
  receiptUrl: string | null
  createdAt: Date
}

// Dental Chart
export interface DentalChart {
  id: string
  patientId: string
  createdAt: Date
  updatedAt: Date
  teeth: ToothRecord[]
}

// Tooth Record
export interface ToothRecord {
  id: string
  toothNumber: number
  condition: ToothCondition
  notes: string | null
  lastUpdated: Date
}

// Patient File
export interface PatientFile {
  id: string
  fileName: string
  originalName: string
  fileType: FileType
  fileSize: number
  mimeType: string
  url: string
  description: string | null
  uploadedBy: string
  createdAt: Date
}

// Patient List Filters
export interface PatientListFilters {
  query?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'name' | 'email'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
  status?: UserStatus
  hasAppointments?: boolean
  hasTreatments?: boolean
  dateFrom?: string
  dateTo?: string
}

// Patient List Response
export interface PatientListResponse {
  patients: PatientWithCounts[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// Patient Statistics
export interface PatientStatistics {
  totalPatients: number
  newThisMonth: number
  newLastMonth: number
  activeTreatments: number
  completedTreatmentsThisMonth: number
  recordsUpdatedThisWeek: number
  recordsUpdatedLastWeek: number
  recentPatients: PatientProfile[]
  trends: {
    newPatients: 'up' | 'down' | 'neutral'
    treatments: 'up' | 'down' | 'neutral'
    records: 'up' | 'down' | 'neutral'
    totalPatients: 'up' | 'down' | 'neutral'
  }
  lastUpdated: string
}

// Patient Form Data
export interface PatientFormData {
  dateOfBirth?: Date
  gender?: string
  address?: string
  emergencyContact?: string
  emergencyPhone?: string
  insuranceProvider?: string
  insuranceNumber?: string
  medicalHistory?: string
  allergies?: string
  medications?: string
}

// Patient Search Result
export interface PatientSearchResult {
  id: string
  name: string
  email: string
  phone: string | null
  lastVisit: Date | null
  totalAppointments: number
  status: UserStatus
}

// Medical History Entry
export interface MedicalHistoryEntry {
  id: string
  date: Date
  type: 'diagnosis' | 'treatment' | 'prescription' | 'note'
  title: string
  description: string
  dentist: string
  attachments?: PatientFile[]
}

// Treatment Timeline Entry
export interface TreatmentTimelineEntry {
  id: string
  date: Date
  type: 'appointment' | 'treatment' | 'payment' | 'note'
  title: string
  description: string
  status: string
  cost?: number
  dentist?: string
}

// File Upload Data
export interface FileUploadData {
  patientId: string
  file: File
  fileType: FileType
  description?: string
}

// Patient Quick Actions
export interface PatientQuickAction {
  id: string
  label: string
  icon: string
  action: () => void
  disabled?: boolean
}
