'use client'

import { useState, useCallback } from 'react'
import { AppointmentFormData } from '@/types/appointment'

interface UseAppointmentBookingReturn {
  isLoading: boolean
  error: string | null
  bookAppointment: (data: AppointmentFormData, patientId: string) => Promise<string>
  rescheduleAppointment: (appointmentId: string, newDateTime: Date) => Promise<void>
  cancelAppointment: (appointmentId: string, reason?: string) => Promise<void>
  clearError: () => void
}

export function useAppointmentBooking(): UseAppointmentBookingReturn {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const bookAppointment = useCallback(async (data: AppointmentFormData, patientId: string): Promise<string> => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId,
          branchId: data.branchId,
          serviceId: data.serviceId,
          dentistId: data.dentistId,
          scheduledAt: data.scheduledAt.toISOString(),
          duration: data.duration,
          type: data.type,
          notes: data.notes,
          symptoms: data.symptoms,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to book appointment')
      }

      const appointment = await response.json()
      return appointment.id
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const rescheduleAppointment = useCallback(async (appointmentId: string, newDateTime: Date): Promise<void> => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scheduledAt: newDateTime.toISOString(),
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to reschedule appointment')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const cancelAppointment = useCallback(async (appointmentId: string, reason?: string): Promise<void> => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'CANCELLED',
          cancelReason: reason,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to cancel appointment')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    isLoading,
    error,
    bookAppointment,
    rescheduleAppointment,
    cancelAppointment,
    clearError,
  }
}
