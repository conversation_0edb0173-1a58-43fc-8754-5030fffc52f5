'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { AppointmentBookingForm } from '@/components/appointments/appointment-booking-form'
import { PatientCalendarIntegration } from '@/components/appointments/patient-calendar-integration'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, Calendar, ArrowLeft, Home, Loader2 } from 'lucide-react'
import { AppointmentDetails } from '@/types/appointment'

export default function BookAppointmentPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [bookingComplete, setBookingComplete] = useState(false)
  const [appointmentId, setAppointmentId] = useState<string | null>(null)
  const [appointmentDetails, setAppointmentDetails] = useState<AppointmentDetails | null>(null)
  const [isLoadingDetails, setIsLoadingDetails] = useState(false)

  const handleBookingSuccess = (id: string) => {
    setAppointmentId(id)
    setBookingComplete(true)
    fetchAppointmentDetails(id)
  }

  const fetchAppointmentDetails = async (id: string) => {
    setIsLoadingDetails(true)
    try {
      const response = await fetch(`/api/appointments/${id}`)
      if (response.ok) {
        const appointment = await response.json()
        setAppointmentDetails(appointment)
      } else {
        console.error('Failed to fetch appointment details')
      }
    } catch (error) {
      console.error('Error fetching appointment details:', error)
    } finally {
      setIsLoadingDetails(false)
    }
  }

  const handleBookingCancel = () => {
    router.push('/')
  }

  const handleViewAppointment = () => {
    if (appointmentId) {
      router.push(`/appointments/${appointmentId}`)
    }
  }

  const handleBookAnother = () => {
    setBookingComplete(false)
    setAppointmentId(null)
    setAppointmentDetails(null)
  }

  const handleGoHome = () => {
    router.push('/dashboard')
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-gray-900">Authentication Required</CardTitle>
                <CardDescription>
                  Please sign in to book an appointment
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button 
                  onClick={() => router.push('/auth/signin?callbackUrl=/appointments/book')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Sign In to Continue
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Booking complete success page
  if (bookingComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Loading state while fetching appointment details */}
            {isLoadingDetails && (
              <Card className="border-blue-200">
                <CardContent className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                    <p className="text-gray-600">Loading appointment details...</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Show calendar integration if appointment details are loaded */}
            {!isLoadingDetails && appointmentDetails && (
              <PatientCalendarIntegration
                appointment={appointmentDetails}
                showAppointmentDetails={true}
              />
            )}

            {/* Fallback confirmation if appointment details failed to load */}
            {!isLoadingDetails && !appointmentDetails && (
              <Card className="border-green-200 bg-green-50">
                <CardHeader className="text-center">
                  <div className="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                    <CheckCircle className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl text-green-900">
                    Appointment Booked Successfully!
                  </CardTitle>
                  <CardDescription className="text-green-700">
                    Your appointment has been confirmed and you will receive a confirmation email shortly.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Alert className="bg-green-100 border-green-300">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      <strong>What&apos;s next?</strong>
                      <ul className="mt-2 space-y-1 text-sm">
                        <li>• You&apos;ll receive a confirmation email with appointment details</li>
                        <li>• A reminder will be sent 24 hours before your appointment</li>
                        <li>• Please arrive 15 minutes early for check-in</li>
                        <li>• Bring a valid ID and insurance information if applicable</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            )}

            {/* Action buttons */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  {appointmentId && (
                    <Button
                      onClick={handleViewAppointment}
                      variant="outline"
                      className="flex items-center"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      View Appointment Details
                    </Button>
                  )}
                  <Button
                    onClick={handleBookAnother}
                    variant="outline"
                    className="flex items-center"
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Book Another Appointment
                  </Button>
                  <Button
                    onClick={handleGoHome}
                    className="bg-blue-600 hover:bg-blue-700 flex items-center"
                  >
                    <Home className="h-4 w-4 mr-2" />
                    Go to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Main booking form
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="mr-4 hover:bg-white/50"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Book an Appointment</h1>
              <p className="text-gray-600 mt-1">
                Schedule your dental appointment in just a few simple steps
              </p>
            </div>
          </div>
          
          {/* Welcome message for authenticated user */}
          <Card className="bg-white/70 backdrop-blur-sm border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-semibold text-lg">
                    {user?.name?.charAt(0) || 'U'}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">Welcome, {user?.name || 'Patient'}!</p>
                  <p className="text-sm text-gray-600">
                    Let&apos;s get your appointment scheduled with our dental team.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Booking Form */}
        <AppointmentBookingForm
          onSuccess={handleBookingSuccess}
          onCancel={handleBookingCancel}
        />
      </div>
    </div>
  )
}
