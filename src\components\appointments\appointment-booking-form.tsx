'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, Circle, ArrowLeft, ArrowRight } from 'lucide-react'
import { AppointmentFormData, AppointmentBookingStep } from '@/types/appointment'
import { BranchSelectionStep } from './steps/branch-selection-step'
import { ServiceSelectionStep } from './steps/service-selection-step'
import { DentistSelectionStep } from './steps/dentist-selection-step'
import { DuplicateCheckStep } from './steps/duplicate-check-step'
import { DateTimeSelectionStep } from './steps/datetime-selection-step'
import { PatientInfoStep } from './steps/patient-info-step'
import { ConfirmationStep } from './steps/confirmation-step'

const BOOKING_STEPS: AppointmentBookingStep[] = [
  {
    id: 'branch',
    title: 'Select Branch',
    description: 'Choose your preferred clinic location',
    isComplete: false,
    isActive: true,
  },
  {
    id: 'service',
    title: 'Select Service',
    description: 'Choose the dental service you need',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'dentist',
    title: 'Select Dentist',
    description: 'Choose your preferred dentist',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'duplicate-check',
    title: 'Check Existing Appointments',
    description: 'Review your current appointments',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'datetime',
    title: 'Select Date & Time',
    description: 'Choose your appointment date and time',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'patient-info',
    title: 'Patient Information',
    description: 'Confirm your personal details',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'confirmation',
    title: 'Confirmation',
    description: 'Review and confirm your appointment',
    isComplete: false,
    isActive: false,
  },
]

interface AppointmentBookingFormProps {
  onSuccess?: (appointmentId: string) => void
  onCancel?: () => void
}

export function AppointmentBookingForm({ onSuccess, onCancel }: AppointmentBookingFormProps) {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()

  const [currentStep, setCurrentStep] = useState(0)
  const [steps, setSteps] = useState(BOOKING_STEPS)
  const [stepHistory, setStepHistory] = useState<number[]>([0]) // Track which steps were actually shown
  const [formData, setFormData] = useState<Partial<AppointmentFormData>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [patientProfile, setPatientProfile] = useState<{ id: string } | null>(null)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/signin?callbackUrl=/appointments/book')
    }
  }, [isAuthenticated, router])

  // Fetch patient profile when user is authenticated
  useEffect(() => {
    const fetchPatientProfile = async () => {
      if (!user?.id) return

      try {
        const response = await fetch(`/api/patients/by-user/${user.id}`)
        if (response.ok) {
          const profile = await response.json()
          setPatientProfile(profile)
        } else if (response.status === 404) {
          // Patient profile doesn't exist, create one
          const createResponse = await fetch('/api/patients', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: user.id,
            }),
          })
          if (createResponse.ok) {
            const newProfile = await createResponse.json()
            setPatientProfile(newProfile)
          } else {
            setError('Failed to create patient profile. Please try again.')
          }
        } else {
          setError('Failed to load patient profile. Please try again.')
        }
      } catch (err) {
        console.error('Error fetching patient profile:', err)
        setError('Failed to load patient profile. Please try again.')
      }
    }

    if (isAuthenticated && user) {
      fetchPatientProfile()
    }
  }, [isAuthenticated, user])

  const updateSteps = (stepIndex: number, isComplete: boolean) => {
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      isComplete: index < stepIndex ? true : index === stepIndex ? isComplete : false,
      isActive: index === stepIndex,
    })))
  }

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      const nextStep = currentStep + 1
      setCurrentStep(nextStep)
      updateSteps(nextStep, false)
      setError(null)

      // Add the new step to history if it's not already there
      setStepHistory(prev => {
        if (!prev.includes(nextStep)) {
          return [...prev, nextStep]
        }
        return prev
      })
    }
  }

  const handlePrevious = () => {
    // Use step history to navigate to the actual previous step that was shown
    const currentHistoryIndex = stepHistory.indexOf(currentStep)
    if (currentHistoryIndex > 0) {
      const prevStep = stepHistory[currentHistoryIndex - 1]
      setCurrentStep(prevStep)
      updateSteps(prevStep, true)
      setError(null)

      // Remove steps from history after the current step
      setStepHistory(prev => prev.slice(0, currentHistoryIndex))
    }
  }

  // Check if there's a previous step in history
  const hasPreviousStep = () => {
    const currentHistoryIndex = stepHistory.indexOf(currentStep)
    return currentHistoryIndex > 0
  }

  const handleStepComplete = (stepData: any) => {
    setFormData(prev => ({ ...prev, ...stepData }))
    updateSteps(currentStep, true)
  }

  // Special handler for auto-skip scenarios (like DuplicateCheckStep)
  const handleAutoSkip = () => {
    if (currentStep < steps.length - 1) {
      const nextStep = currentStep + 1
      setCurrentStep(nextStep)
      updateSteps(nextStep, false)
      setError(null)

      // For auto-skip: replace the current step with the next step in history
      // This ensures the skipped step is not in the navigation history
      setStepHistory(prev => {
        const currentIndex = prev.indexOf(currentStep)
        if (currentIndex !== -1) {
          // Replace the current step with the next step
          const newHistory = [...prev]
          newHistory[currentIndex] = nextStep
          return newHistory
        } else {
          // If current step not in history, just add the next step
          return [...prev, nextStep]
        }
      })
    }
  }

  const handleSubmit = async () => {
    if (!user || !patientProfile || !formData.branchId || !formData.serviceId || !formData.dentistId || !formData.scheduledAt) {
      setError('Please complete all required fields')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId: patientProfile.id, // Use the patient profile ID, not user ID
          branchId: formData.branchId,
          serviceId: formData.serviceId,
          dentistId: formData.dentistId,
          scheduledAt: formData.scheduledAt.toISOString(),
          duration: formData.duration || 60,
          type: formData.type || 'CONSULTATION',
          notes: formData.notes,
          symptoms: formData.symptoms,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to book appointment')
      }

      const appointment = await response.json()
      onSuccess?.(appointment.id)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <BranchSelectionStep
            selectedBranchId={formData.branchId}
            onSelect={(branchId) => handleStepComplete({ branchId })}
            onNext={handleNext}
          />
        )
      case 1:
        return (
          <ServiceSelectionStep
            branchId={formData.branchId!}
            selectedServiceId={formData.serviceId}
            onSelect={(serviceId, duration) => handleStepComplete({ serviceId, duration })}
            onNext={handleNext}
          />
        )
      case 2:
        return (
          <DentistSelectionStep
            branchId={formData.branchId!}
            selectedDentistId={formData.dentistId}
            onSelect={(dentistId) => handleStepComplete({ dentistId })}
            onNext={handleNext}
          />
        )
      case 3:
        return (
          <DuplicateCheckStep
            onNext={handleAutoSkip}
            onContinueAnyway={handleNext}
            onManageExisting={(appointment) => {
              // Navigate to appointment details or management page
              window.location.href = `/appointments/${appointment.id}`
            }}
          />
        )
      case 4:
        return (
          <DateTimeSelectionStep
            dentistId={formData.dentistId!}
            duration={formData.duration || 60}
            selectedDateTime={formData.scheduledAt}
            onSelect={(scheduledAt) => handleStepComplete({ scheduledAt })}
            onNext={handleNext}
          />
        )
      case 5:
        return (
          <PatientInfoStep
            notes={formData.notes}
            symptoms={formData.symptoms}
            onUpdate={(notes, symptoms) => handleStepComplete({ notes, symptoms })}
            onNext={handleNext}
          />
        )
      case 6:
        return (
          <ConfirmationStep
            formData={formData as AppointmentFormData}
            onConfirm={handleSubmit}
            isLoading={isLoading}
          />
        )
      default:
        return null
    }
  }

  if (!isAuthenticated || !patientProfile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">
          {!isAuthenticated ? 'Authenticating...' : 'Loading patient profile...'}
        </span>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Steps */}
      <div className="mb-8">
        {/* Desktop and Tablet View */}
        <div className="hidden sm:flex items-center justify-center">
          <div className="flex items-center space-x-4 lg:space-x-6">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center flex-shrink-0">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-200 ${
                      step.isComplete
                        ? 'bg-green-600 border-green-600 text-white'
                        : step.isActive
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : 'bg-white border-gray-300 text-gray-400'
                    }`}
                  >
                    {step.isComplete ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Circle className="h-5 w-5" />
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <p className={`text-sm font-medium whitespace-nowrap ${step.isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-400 max-w-[100px]">
                      {step.description}
                    </p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 lg:w-16 h-0.5 mx-3 lg:mx-4 ${step.isComplete ? 'bg-green-600' : 'bg-gray-300'}`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Mobile View - Compact horizontal scrollable */}
        <div className="sm:hidden">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-3 overflow-x-auto pb-2 px-4">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center flex-shrink-0">
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all duration-200 ${
                        step.isComplete
                          ? 'bg-green-600 border-green-600 text-white'
                          : step.isActive
                          ? 'bg-blue-600 border-blue-600 text-white'
                          : 'bg-white border-gray-300 text-gray-400'
                      }`}
                    >
                      {step.isComplete ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Circle className="h-4 w-4" />
                      )}
                    </div>
                    <div className="mt-1 text-center">
                      <p className={`text-xs font-medium whitespace-nowrap max-w-[60px] truncate ${step.isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                        {step.title.split(' ')[0]} {/* Show only first word on mobile */}
                      </p>
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-6 h-0.5 mx-2 ${step.isComplete ? 'bg-green-600' : 'bg-gray-300'}`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Current step indicator for mobile */}
          <div className="text-center mt-2">
            <p className="text-sm font-medium text-blue-600">
              Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
            </p>
            <p className="text-xs text-gray-500">
              {steps[currentStep].description}
            </p>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Current Step Content */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl text-gray-900">
            {steps[currentStep].title}
          </CardTitle>
          <CardDescription>
            {steps[currentStep].description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderCurrentStep()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={!hasPreviousStep() ? onCancel : handlePrevious}
          disabled={isLoading}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {!hasPreviousStep() ? 'Cancel' : 'Previous'}
        </Button>

        {currentStep < steps.length - 1 && (
          <Button
            onClick={handleNext}
            disabled={!steps[currentStep].isComplete || isLoading}
          >
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        )}
      </div>
    </div>
  )
}
