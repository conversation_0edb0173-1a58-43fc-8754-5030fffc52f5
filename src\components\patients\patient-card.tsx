'use client'

import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Phone,
  Mail,
  Calendar,
  MapPin,
  FileText,
  Activity,
  CreditCard,
  MoreVertical,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { PatientWithCounts } from '@/types/patient'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface PatientCardProps {
  patient: PatientWithCounts
  onView?: (patient: PatientWithCounts) => void
  onEdit?: (patient: PatientWithCounts) => void
  onDelete?: (patient: PatientWithCounts) => void
  className?: string
}

export function PatientCard({ patient, onView, onEdit, onDelete, className }: PatientCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getInitials = (name: string | null) => {
    if (!name) return 'U'
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatAge = (dateOfBirth: Date | null) => {
    if (!dateOfBirth) return null
    const today = new Date()
    const birth = new Date(dateOfBirth)
    const age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1
    }
    return age
  }

  return (
    <Card className={cn("hover:shadow-md transition-shadow duration-200", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={patient.user.image || undefined} alt={patient.user.name || 'Patient'} />
              <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                {getInitials(patient.user.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate">
                {patient.user.name || 'Unknown Patient'}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <Badge className={cn("text-xs", getStatusColor(patient.user.status))}>
                  {patient.user.status}
                </Badge>
                {patient.dateOfBirth && (
                  <span className="text-sm text-gray-500">
                    Age {formatAge(patient.dateOfBirth)}
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onView?.(patient)}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit?.(patient)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete?.(patient)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Patient
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Contact Information */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <Mail className="h-4 w-4 mr-2 text-gray-400" />
            <span className="truncate">{patient.user.email}</span>
          </div>
          {patient.user.phone && (
            <div className="flex items-center text-sm text-gray-600">
              <Phone className="h-4 w-4 mr-2 text-gray-400" />
              <span>{patient.user.phone}</span>
            </div>
          )}
          {patient.address && (
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="h-4 w-4 mr-2 text-gray-400" />
              <span className="truncate">{patient.address}</span>
            </div>
          )}
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-3 gap-4 py-3 border-t border-gray-100">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Calendar className="h-4 w-4 text-blue-600" />
            </div>
            <div className="text-lg font-semibold text-blue-600">
              {patient._count.appointments}
            </div>
            <div className="text-xs text-gray-500">Appointments</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <Activity className="h-4 w-4 text-green-600" />
            </div>
            <div className="text-lg font-semibold text-green-600">
              {patient._count.treatments}
            </div>
            <div className="text-xs text-gray-500">Treatments</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <CreditCard className="h-4 w-4 text-purple-600" />
            </div>
            <div className="text-lg font-semibold text-purple-600">
              {patient._count.invoices}
            </div>
            <div className="text-xs text-gray-500">Invoices</div>
          </div>
        </div>

        {/* Registration Date */}
        <div className="pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Registered</span>
            <span>{format(new Date(patient.createdAt), 'MMM d, yyyy')}</span>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2 mt-4">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={() => onView?.(patient)}
          >
            <FileText className="h-4 w-4 mr-1" />
            View Records
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            className="flex-1"
            onClick={() => onEdit?.(patient)}
          >
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
