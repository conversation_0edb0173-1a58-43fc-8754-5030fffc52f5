import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withAuth } from '@/lib/auth-utils'
import { userUpdateSchema } from '@/lib/validations'
import { UserRole } from '@prisma/client'

// GET /api/users/[id] - Get user by ID
export const GET = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  // Users can only access their own data unless they're admin/staff
  if (user.role === UserRole.PATIENT && user.id !== id) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const targetUser = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      name: true,
      image: true,
      phone: true,
      role: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      patientProfile: {
        include: {
          appointments: {
            take: 5,
            orderBy: { scheduledAt: 'desc' },
            include: {
              service: { select: { name: true } },
              dentist: { 
                include: { 
                  user: { select: { name: true } } 
                } 
              },
            },
          },
          treatments: {
            take: 5,
            orderBy: { createdAt: 'desc' },
            include: {
              service: { select: { name: true } },
              dentist: { 
                include: { 
                  user: { select: { name: true } } 
                } 
              },
            },
          },
        },
      },
      dentistProfile: {
        include: {
          appointments: {
            take: 5,
            orderBy: { scheduledAt: 'desc' },
            include: {
              patient: { 
                include: { 
                  user: { select: { name: true, email: true } } 
                } 
              },
              service: { select: { name: true } },
            },
          },
          treatments: {
            take: 5,
            orderBy: { createdAt: 'desc' },
            include: {
              patient: { 
                include: { 
                  user: { select: { name: true } } 
                } 
              },
              service: { select: { name: true } },
            },
          },
        },
      },
      staffProfile: true,
    },
  })
  
  if (!targetUser) {
    return NextResponse.json(
      { error: 'User not found' },
      { status: 404 }
    )
  }
  
  return NextResponse.json(targetUser)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// PUT /api/users/[id] - Update user
export const PUT = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  const body = await req.json()
  
  // Users can only update their own data unless they're admin
  if (user.role !== UserRole.ADMIN && user.id !== id) {
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    )
  }
  
  const validatedData = userUpdateSchema.parse(body)
  
  const updatedUser = await prisma.user.update({
    where: { id },
    data: validatedData,
    select: {
      id: true,
      email: true,
      name: true,
      image: true,
      phone: true,
      role: true,
      status: true,
      updatedAt: true,
    },
  })
  
  return NextResponse.json(updatedUser)
}, [UserRole.ADMIN, UserRole.STAFF, UserRole.DENTIST, UserRole.PATIENT])

// DELETE /api/users/[id] - Delete user (Admin only)
export const DELETE = withAuth(async (req: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params
  
  // Prevent self-deletion
  if (user.id === id) {
    return NextResponse.json(
      { error: 'Cannot delete your own account' },
      { status: 400 }
    )
  }
  
  await prisma.user.delete({
    where: { id },
  })
  
  return NextResponse.json({ message: 'User deleted successfully' })
}, [UserRole.ADMIN])
