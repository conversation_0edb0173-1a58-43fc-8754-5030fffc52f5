'use client'

import { useState, useCallback, useEffect } from 'react'
import { AppointmentFormData, AppointmentBookingStep } from '@/types/appointment'
import { AppointmentType } from '@prisma/client'

interface UseAppointmentFormReturn {
  formData: Partial<AppointmentFormData>
  currentStep: number
  steps: AppointmentBookingStep[]
  isStepValid: (stepIndex: number) => boolean
  updateFormData: (data: Partial<AppointmentFormData>) => void
  nextStep: () => void
  previousStep: () => void
  goToStep: (stepIndex: number) => void
  resetForm: () => void
  isFormComplete: boolean
}

const INITIAL_STEPS: AppointmentBookingStep[] = [
  {
    id: 'branch',
    title: 'Select Branch',
    description: 'Choose your preferred clinic location',
    isComplete: false,
    isActive: true,
  },
  {
    id: 'service',
    title: 'Select Service',
    description: 'Choose the dental service you need',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'dentist',
    title: 'Select Dentist',
    description: 'Choose your preferred dentist',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'datetime',
    title: 'Select Date & Time',
    description: 'Choose your appointment date and time',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'patient-info',
    title: 'Patient Information',
    description: 'Confirm your personal details',
    isComplete: false,
    isActive: false,
  },
  {
    id: 'confirmation',
    title: 'Confirmation',
    description: 'Review and confirm your appointment',
    isComplete: false,
    isActive: false,
  },
]

export function useAppointmentForm(): UseAppointmentFormReturn {
  const [formData, setFormData] = useState<Partial<AppointmentFormData>>({
    type: AppointmentType.CONSULTATION,
    duration: 60,
  })
  const [currentStep, setCurrentStep] = useState(0)
  const [steps, setSteps] = useState(INITIAL_STEPS)

  const updateSteps = useCallback((stepIndex: number, isComplete: boolean) => {
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      isComplete: index < stepIndex ? true : index === stepIndex ? isComplete : false,
      isActive: index === stepIndex,
    })))
  }, [])

  const isStepValid = useCallback((stepIndex: number): boolean => {
    switch (stepIndex) {
      case 0: // Branch selection
        return !!formData.branchId
      case 1: // Service selection
        return !!formData.serviceId && !!formData.duration
      case 2: // Dentist selection
        return !!formData.dentistId
      case 3: // Date/time selection
        return !!formData.scheduledAt
      case 4: // Patient info (optional fields)
        return true
      case 5: // Confirmation
        return !!(formData.branchId && formData.serviceId && formData.dentistId && formData.scheduledAt)
      default:
        return false
    }
  }, [formData])

  const updateFormData = useCallback((data: Partial<AppointmentFormData>) => {
    setFormData(prev => ({ ...prev, ...data }))
  }, [])

  const nextStep = useCallback(() => {
    if (currentStep < steps.length - 1 && isStepValid(currentStep)) {
      const nextStepIndex = currentStep + 1
      setCurrentStep(nextStepIndex)
      updateSteps(nextStepIndex, false)
    }
  }, [currentStep, steps.length, isStepValid, updateSteps])

  const previousStep = useCallback(() => {
    if (currentStep > 0) {
      const prevStepIndex = currentStep - 1
      setCurrentStep(prevStepIndex)
      updateSteps(prevStepIndex, true)
    }
  }, [currentStep, updateSteps])

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      // Check if all previous steps are valid
      let canGoToStep = true
      for (let i = 0; i < stepIndex; i++) {
        if (!isStepValid(i)) {
          canGoToStep = false
          break
        }
      }

      if (canGoToStep) {
        setCurrentStep(stepIndex)
        updateSteps(stepIndex, isStepValid(stepIndex))
      }
    }
  }, [steps.length, isStepValid, updateSteps])

  const resetForm = useCallback(() => {
    setFormData({
      type: AppointmentType.CONSULTATION,
      duration: 60,
    })
    setCurrentStep(0)
    setSteps(INITIAL_STEPS)
  }, [])

  // Update step completion when form data changes
  useEffect(() => {
    updateSteps(currentStep, isStepValid(currentStep))
  }, [formData, currentStep, isStepValid, updateSteps])

  const isFormComplete = !!(
    formData.branchId &&
    formData.serviceId &&
    formData.dentistId &&
    formData.scheduledAt &&
    formData.duration
  )

  return {
    formData,
    currentStep,
    steps,
    isStepValid,
    updateFormData,
    nextStep,
    previousStep,
    goToStep,
    resetForm,
    isFormComplete,
  }
}

// Hook for managing form validation errors
interface UseFormValidationReturn {
  errors: Record<string, string>
  setError: (field: string, message: string) => void
  clearError: (field: string) => void
  clearAllErrors: () => void
  hasErrors: boolean
}

export function useFormValidation(): UseFormValidationReturn {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const setError = useCallback((field: string, message: string) => {
    setErrors(prev => ({ ...prev, [field]: message }))
  }, [])

  const clearError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[field]
      return newErrors
    })
  }, [])

  const clearAllErrors = useCallback(() => {
    setErrors({})
  }, [])

  const hasErrors = Object.keys(errors).length > 0

  return {
    errors,
    setError,
    clearError,
    clearAllErrors,
    hasErrors,
  }
}

// Hook for managing loading states across multiple operations
interface UseLoadingStatesReturn {
  loadingStates: Record<string, boolean>
  setLoading: (operation: string, isLoading: boolean) => void
  isLoading: (operation: string) => boolean
  isAnyLoading: boolean
}

export function useLoadingStates(): UseLoadingStatesReturn {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  const setLoading = useCallback((operation: string, isLoading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [operation]: isLoading }))
  }, [])

  const isLoading = useCallback((operation: string) => {
    return loadingStates[operation] || false
  }, [loadingStates])

  const isAnyLoading = Object.values(loadingStates).some(Boolean)

  return {
    loadingStates,
    setLoading,
    isLoading,
    isAnyLoading,
  }
}
